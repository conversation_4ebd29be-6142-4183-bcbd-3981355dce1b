package com.concise.common.consts;

import java.util.HashSet;
import java.util.Set;

import cn.hutool.core.collection.CollectionUtil;

/**
 * 通用常量
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date 2020/3/11 16:51
 */
public interface CommonConstant {

    /**
     * id
     */
    String ID = "id";

    /**
     * 名称
     */
    String NAME = "name";

    /**
     * 编码
     */
    String CODE = "code";

    /**
     * 值
     */
    String VALUE = "value";

    /**
     * 默认标识状态的字段名称
     */
    String STATUS = "status";

    /**
     * 默认逻辑删除的状态值
     */
    String DEFAULT_LOGIC_DELETE_VALUE = "2";

    /**
     * 用户代理
     */
    String USER_AGENT = "User-Agent";

    /**
     * 请求头token表示
     */
    String AUTHORIZATION = "Authorization";

    /**
     * token名称
     */
    String TOKEN_NAME = "token";

    /**
     * token类型
     */
    String TOKEN_TYPE_BEARER = "Bearer";

    /**
     * 首页提示语
     */
    String INDEX_TIPS = "Welcome To Snowy";

    /**
     * 未知标识
     */
    String UNKNOWN = "Unknown";

    /**
     * 默认包名
     */
    String DEFAULT_PACKAGE_NAME = "com.concise";

    /**
     * 默认密码
     */
    String DEFAULT_PASSWORD = "123456";

    /**
     * 请求号在header中的唯一标识
     */
    String REQUEST_NO_HEADER_NAME = "Request-No";

    /**
     * 数据库链接URL标识
     */
    String DATABASE_URL_NAME = "DATABASE_URL_NAME";

    /**
     * 数据库链接驱动标识
     */
    String DATABASE_DRIVER_NAME = "DATABASE_DRIVER_NAME";

    /**
     * 数据库用户标识
     */
    String DATABASE_USER_NAME = "DATABASE_USER_NAME";

    /**
     * 点选验证码
     */
    String IMAGE_CODE_TYPE = "clickWord";

    /**
     * undefined未知
     */
    String UNDEFINED = "undefined";

    /**
     * 登录次数
     */
    Integer LOGIN_ERROR_COUNT = 5;

    /**
     * 语气词
     */
    Set<String> TONE_WORDS = new HashSet<>(CollectionUtil.newArrayList("吗", "呢", "吧", "啊", "呀", "呐", "吗", "吧", "哦", "嘛", "么", "嘞", "唉", "嗯", "欤", "咚", "咦", "喏", "诶", "嘻", "嘿", "哈", "呵", "咳", "哼", "唔", "嗯", "哼", "嗬", "咳", "咦", "哎", "哟", "哎", "呀", "呦", "咚", "咦", "喔", "喽", "嘞", "嘛", "嘻", "嘿", "欤", "欸"));

    /**
     * 顶级机构
     */
    String TOP_ORG_ID = "DEPT0000000000000000000000900000";
}
