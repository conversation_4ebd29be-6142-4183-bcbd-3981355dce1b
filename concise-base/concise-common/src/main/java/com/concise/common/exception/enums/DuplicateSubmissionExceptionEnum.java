package com.concise.common.exception.enums;

import com.concise.common.annotion.ExpEnumType;
import com.concise.common.consts.ExpEnumConstant;
import com.concise.common.exception.enums.abs.AbstractBaseExceptionEnum;
import com.concise.common.factory.ExpEnumCodeFactory;

/**
 * 防重复提交相关异常枚举
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
@ExpEnumType(module = ExpEnumConstant.SNOWY_CORE_MODULE_EXP_CODE, kind = ExpEnumConstant.DUPLICATE_SUBMISSION_EXCEPTION_ENUM)
public enum DuplicateSubmissionExceptionEnum implements AbstractBaseExceptionEnum {

    /**
     * 重复提交
     */
    DUPLICATE_SUBMISSION(1, "操作过于频繁，请稍后再试"),

    /**
     * Redis操作异常
     */
    REDIS_OPERATION_ERROR(2, "系统繁忙，请稍后再试");

    private final Integer code;

    private final String message;

    DuplicateSubmissionExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return ExpEnumCodeFactory.getExpEnumCode(this.getClass(), code);
    }

    @Override
    public String getMessage() {
        return message;
    }
}
