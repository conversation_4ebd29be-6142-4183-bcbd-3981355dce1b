package com.concise.common.util;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.digest.SM3;
import cn.hutool.crypto.symmetric.SM4;
import com.alibaba.fastjson.JSON;

import java.util.Map;
import java.util.TreeMap;

/**
 * <h1>
 * SMUtils
 * </h1>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020/12/19 14:40
 */
public class SmUtils {

    private static byte[] sm4Secret = "el2MjcMNFkd0LgXb".getBytes();


    public static void main(String[] args) {
        Map<String, Object> map = new TreeMap<>();
        map.put("pageSize", 10);
        map.put("pageNo", 1);
        map.put("correctionObjId", "1412314130333585410");
        map.put("locateTime", "2021-07-14");
        System.out.println(JSON.toJSONString(map));
        String sign = generateSm3(map, "wg7pjgr2tfnTqYVESCDiGQnFJMYJxmaYJ0BCeH6J0BOErTH69Q019507YxO0KQyc".getBytes());
        System.out.println(sign);
    }

    /**
     * 生成sm3
     *
     * @param map
     * @return
     */
    public static String generateSm3(Map<String, Object> map, byte[] appsecretBytes) {
        StringBuilder param = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            param.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        String paramResult = param.toString();
        if (paramResult.endsWith("&")) {
            paramResult = paramResult.substring(0, paramResult.length() - 1);
        }
        SM3 sm3 = SmUtil.sm3();
        sm3.setSalt(appsecretBytes);
        return sm3.digestHex(paramResult);
    }

    public static String decryptSm4ToDeptId(String undecrypted) {
        undecrypted = undecrypted.split("\\.")[1];
        SM4 sm4 = new SM4(sm4Secret);
        return sm4.decryptStr(undecrypted);
    }


    /**
     * 加密sm4
     *
     * @param city
     * @param deptId
     * @return
     */
    public static String encryptSm4ToDeptId(String city, String deptId) {
        SM4 sm4 = new SM4(sm4Secret);
        String encryptBase64 = sm4.encryptBase64(deptId);
        return city + "." + encryptBase64;
    }
}
