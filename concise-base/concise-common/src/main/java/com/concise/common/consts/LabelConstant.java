package com.concise.common.consts;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 标签相关常量
 * @Title: LabelConstant
 * @Package com.concise.common.consts
 * @date 2022/03/25 13:37
 */
public class LabelConstant {

    /**
     * 矫正阶段标签ID 1: 初期 2：中期 3：末期
     */
    public static final Map<Integer, String> PHASE = new HashMap(3){{
        put(1, "0626c3e0fe954e0ebe77234b4500b591");
        put(2, "151308d389784cc58534b4f4a05208d6");
        put(3, "5c62374e27c44f2eae5dc6ff9ed6e20f");
    }};
}
