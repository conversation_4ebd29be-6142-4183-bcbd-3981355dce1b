package com.concise.common.file.util;

import java.io.IOException;
import java.net.URL;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectResult;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 阿里云 oss 上传工具类
 * @Date: 2019/5/10
 */
@Slf4j
public class OssBootUtil {

    private static String endPoint;
    private static String accessKeyId;
    private static String accessKeySecret;
    private static String bucketName;
    private static String staticDomain;
    private static String publicDomain;

    public static void setEndPoint(String endPoint) {
        OssBootUtil.endPoint = endPoint;
    }

    public static void setAccessKeyId(String accessKeyId) {
        OssBootUtil.accessKeyId = accessKeyId;
    }

    public static void setAccessKeySecret(String accessKeySecret) {
        OssBootUtil.accessKeySecret = accessKeySecret;
    }

    public static void setBucketName(String bucketName) {
        OssBootUtil.bucketName = bucketName;
    }

    public static void setStaticDomain(String staticDomain) {
        OssBootUtil.staticDomain = staticDomain;
    }

    public static String getStaticDomain() {
        return staticDomain;
    }

    public static String getEndPoint() {
        return endPoint;
    }

    public static String getAccessKeyId() {
        return accessKeyId;
    }

    public static String getAccessKeySecret() {
        return accessKeySecret;
    }

    public static String getBucketName() {
        return bucketName;
    }

    public static OSSClient getOssClient() {
        return ossClient;
    }

    public static String getPublicDomain() {
        return publicDomain;
    }

    public static void setPublicDomain(String publicDomain) {
        OssBootUtil.publicDomain = publicDomain;
    }


    /**
     * oss 工具客户端
     */
    private static OSSClient ossClient = null;

    /**
     * 上传文件至阿里云 OSS
     * 文件上传成功,返回文件完整访问路径
     * 文件上传失败,返回 null
     *
     * @param file    待上传文件
     * @param fileDir 文件保存目录
     * @return oss 中的相对文件路径
     */
    public static String upload(MultipartFile file, String fileDir, String customBucket, String endPoint, String accessKeyId, String accessKeySecret, String bucketName) {
        String FILE_URL = null;
        initOSS(endPoint, accessKeyId, accessKeySecret);
        StringBuilder fileUrl = new StringBuilder();
        String newBucket = bucketName;
        if(StringUtils.isNotEmpty(customBucket)){
            newBucket = customBucket;
        }
        try {
            //判断桶是否存在,不存在则创建桶
            if(!ossClient.doesBucketExist(newBucket)){
                ossClient.createBucket(newBucket);
            }
            // 获取文件名
            String orgName = file.getOriginalFilename();
            if("" == orgName){
                orgName=file.getName();
            }
            orgName = getFileName(orgName);
            String fileName = orgName.substring(0, orgName.lastIndexOf(".")) + "_" + System.currentTimeMillis() + orgName.substring(orgName.indexOf("."));
            if (!fileDir.endsWith("/")) {
                fileDir = fileDir.concat("/");
            }
            fileUrl = fileUrl.append(fileDir + fileName);

            FILE_URL = "https://" + newBucket + "." + endPoint + "/" + fileUrl;

            PutObjectResult result = ossClient.putObject(newBucket, fileUrl.toString(), file.getInputStream());
            // 设置权限(公开读)
//            ossClient.setBucketAcl(newBucket, CannedAccessControlList.PublicRead);
            if (result != null) {
                log.info("------OSS文件上传成功------" + fileUrl);
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        return FILE_URL;
    }

    /**
     * 文件上传
     * @param file
     * @return
     */
    public static String upload(MultipartFile file, String endPoint, String accessKeyId, String accessKeySecret, String bucketName, String folder) {
        return upload(file, folder,null, endPoint, accessKeyId, accessKeySecret, bucketName);
    }

    /**
     * 初始化 oss 客户端
     *
     * @return
     */
    private static OSSClient initOSS(String endpoint, String accessKeyId, String accessKeySecret) {
        if (ossClient == null) {
            // 私有云要关闭CNAME
            ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
            conf.setSupportCname(false);
            ossClient = (OSSClient)new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret, conf);
        }
        return ossClient;
    }

    /**
     * 判断文件名是否带盘符，重新处理
     * @param fileName
     * @return
     */
    public static String getFileName(String fileName){
        //判断是否带有盘符信息
        // Check for Unix-style path
        int unixSep = fileName.lastIndexOf('/');
        // Check for Windows-style path
        int winSep = fileName.lastIndexOf('\\');
        // Cut off at latest possible point
        int pos = (winSep > unixSep ? winSep : unixSep);
        if (pos != -1)  {
            // Any sort of path separator found...
            fileName = fileName.substring(pos + 1);
        }
        //替换上传文件名字的特殊字符
        fileName = fileName.replace("=","").replace(",","").replace("&","").replace("#", "");
        return fileName;
    }

    public static String filter(String str) throws PatternSyntaxException {
        // 清除掉所有特殊字符
        String regEx = "[`_《》~!@#$%^&*()+=|{}':;',\\[\\].<>?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }

    /**
     * 生成预签名URL，用于临时访问OSS对象
     *
     * @param objectName 对象名称（文件路径）
     * @param expireTime 过期时间，单位为毫秒
     * @param bucket 自定义桶名称，为空则使用默认桶
     * @return 预签名的URL
     */
    public static String generatePresignedUrl(String objectName, long expireTime, String bucket) {
        String newBucket = bucketName;
        if (ObjectUtil.isNotEmpty(bucket)) {
            newBucket = bucket;
        } else {
            newBucket = bucketName;
        }
        initOSS(endPoint, accessKeyId, accessKeySecret);
        try {
            // 设置URL过期时间
            Date expiration = new Date(System.currentTimeMillis() + expireTime);
            // 生成预签名URL - 移除解码操作
            URL url = ossClient.generatePresignedUrl(newBucket, objectName, expiration);
            // 不进行解码，直接返回
            return url.toString();
        } catch (Exception e) {
            log.error("生成预签名URL失败：" + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成预签名URL，使用默认桶
     *
     * @param objectName 对象名称（文件路径）
     * @param expireTime 过期时间，单位为毫秒
     * @return 预签名的URL
     */
    public static String generatePresignedUrl(String objectName, long expireTime) {
        return generatePresignedUrl(objectName, expireTime, null);
    }
    /**
     * 生成预签名URL，使用默认桶，默认7天
     *
     * @param objectName 对象名称（文件路径）
     * @return 预签名的URL
     */
    public static String generatePresignedUrl(String objectName) {
        return generatePresignedUrl(objectName, 7*24*60*60*1000, null);
    }

    /**
     * 生成预签名URL，使用默认桶，默认7天，适用于已经经过转发的外网地址
     *
     * @param formalUrl 待处理的完整的转发过的url，例如
     * @param originPrefix oss原始前缀
     * @param newPrefix 转发后的前缀
     * @return 预签名的URL
     */
    public static String generatePresignedUrl(String formalUrl,String originPrefix ,String newPrefix) {
        String objectName = formalUrl.replace(newPrefix+"/", "");
        String url= generatePresignedUrl(objectName, 7*24*60*60*1000, null);
        return url.replace(originPrefix, newPrefix);
    }

    /**
     * 生成预签名URL，使用默认桶，默认7天，适用于已经经过转发的外网地址,适配本项目
     * 
     * @param formalUrl 待处理的完整的转发过的url，例如
     * @return 预签名的URL
     */
    public static String generatePresignedUrlEdu(String formalUrl) {
        return generatePresignedUrl(formalUrl, staticDomain, publicDomain);
    }
}
