package com.concise.common.pojo.node;

import com.concise.common.pojo.base.node.BaseTreeStringNode;
import lombok.Data;

import java.util.List;

/**
 * antd通用前端树节点
 *
 * <AUTHOR>
 * @date 2020/6/9 12:42
 */
@Data
public class AntdBaseTreeNode implements BaseTreeStringNode {

    /**
     * 主键
     */
    private String id;

    /**
     * 父id
     */
    private String parentId;

    /**
     * 名称
     */
    private String title;

    /**
     * 值
     */
    private String value;

    /**
     * 排序，越小优先级越高
     */
    private Integer weight;

    /**
     * 子节点
     */
    private List children;

    /**
     * 是否禁用
     */
    private boolean disabled;

    /**
     * 父id别名
     */
    @Override
    public String getPid() {
        return this.parentId;
    }

    /**
     * 子节点
     */
    @Override
    public void setChildren(List children) {
        this.children = children;
    }

}
