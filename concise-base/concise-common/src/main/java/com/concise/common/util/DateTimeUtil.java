package com.concise.common.util;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description:
 * @Title: DateTimeUtil
 * @Package com.concise.common.util
 * @date 2022/03/09 11:38
 */
public class DateTimeUtil {

    /**
     * 获取n分钟前的时间
     * @param minute 正数则表示当前时间之后， 负数则是当前时间之前
     * @return
     */
    public static Date beforeMinuteNowDate(int minute) {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MINUTE, minute);
        return c.getTime();
    }

    /**
     * 获取当前时间 N 天前后的日期
     * @param dayNum 天数
     * @return
     */
    public static Date getDay(int dayNum) {
        //请求参数 更新时间
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DATE, dayNum);
        Date date = c.getTime();
        return date;
    }

    /**
     * 计算2个时间之间的相差的天数
     * @param dateAhead 早一点的时间
     * @param dateLater 晚一点的时间
     * @return
     */
    public static int getDayNum(Date dateAhead, Date dateLater) {
        long dqcday = (long)((dateLater.getTime() - dateAhead.getTime()) / (1000 * 60 * 60 *24) + 0.5);
        return Integer.parseInt(String.valueOf(dqcday));
    }

    /**
     * 获取上个月月份
     * @return
     */
    public static int getLastMonth() {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        // 设置为当前时间
        calendar.setTime(date);
        calendar.add(Calendar.MONTH,-1);
        // 设置为上一个月
        date = calendar.getTime();
        return Integer.parseInt(format.format(date));
    }

    /**
     * 循环2个时间之间的月份
     * @param startDay  格式： yyyyMMdd
     * @param endDay    格式： yyyyMMdd
     * @return
     */
    public static List<String> getMonths(String startDay, String endDay) {
        List<String> result = new ArrayList<String>();
        try {
            SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
            //    获取两个时间之间的月份
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");//格式化，调整为自己需要的格式
            Calendar min = Calendar.getInstance();
            Calendar max = Calendar.getInstance();
            //    给calendar设置开始时间
            min.setTime(sf.parse(startDay));
            //    set方法设置年月日 年为开始时间的年份 后面同理
            min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);
            //    给calendar设置结束时间
            max.setTime(sf.parse(endDay));
            //    set方法设置年月日 年为结束时间的年份 后面同理，最后面的1和2不要改
            max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);
            //    创建一个临时的变量，代表当前的时间
            Calendar curr = min;
            //    如果当前的时间在结束时间之前，循环知道超过结束时间就结束，返回结果集合
            while (curr.before(max)) {
                //        将这个当前的时间格式化之后保存到result集合
                result.add(sdf.format(curr.getTime()));
                //        将当前的时间加上1个月
                curr.add(Calendar.MONTH, 1);
            }
        } catch (Exception e) {

        }
        return result;
    };

    /**
     * 根据月份获取该月最后一天，格式 yyyyMMdd
     * @param month 格式： yyyyMM
     * @return
     */
    public static String getLastDate(String month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, Integer.parseInt(month.substring(0, 4)));
        //设置月份
        cal.set(Calendar.MONTH, Integer.parseInt(month.substring(4, 6)));
        //获取当月最小值
        int lastDay = cal.getMinimum(Calendar.DAY_OF_MONTH);
        //设置日历中的月份，当月+1月-1天=当月最后一天
        cal.set(Calendar.DAY_OF_MONTH, lastDay-1);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(cal.getTime());
    }

    public static void main(String[] args) {
        System.out.println(getLastDate("202202"));
    }
}
