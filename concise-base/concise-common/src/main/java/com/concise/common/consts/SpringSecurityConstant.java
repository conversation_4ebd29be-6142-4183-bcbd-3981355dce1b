package com.concise.common.consts;

import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.annotation.Value;

/**
 * SpringSecurity相关常量
 *
 * <AUTHOR>
 * @date 2020/3/18 17:49
 */
@Configurable
public class SpringSecurityConstant {

    @Value("${spring.profiles.active}")
    private String active;

    /**
     * 放开权限校验的接口
     */
    public static String[] NONE_SECURITY_URL_PATTERNS = {

            //前端的
            "/favicon.ico",
            "/yhScreen/**",
            "/correctionRiskLevel/riskAssessment",

            //后端的
            "/",
            "/login",
            "/loginQrCode",
            "/loginAuthCode",
            "/loginScreen",
            "/logout",
            "/oauth/**",
            // "/getSjtj",
            // "/sys/zgSsoLogin",
            // "/sys/yjSsoLogin",
            "/sso/login",
            // "/sso/login/sjAdminLogin",
            // "/sso/login/getParams",
            "/zjhLogin",
            "/correctionSearchHome/**",
            "/correctionPortrayalHome/**",
            "/test/**",
            "/loadPDFTest",
            "/uploadCategory",
            "/ssologinLx",

            //文件的
            "/sysFileInfo/upload",
            "/sysFileInfo/download",
            "/sysFileInfo/preview",

            //druid的
            "/druid/**",

            //获取验证码
            "/captcha/**",
            "/getCaptchaOpen",

            //外部接口
            "/openApi/**",
            //统一用户
            "/unifyUser/userReceive",
            "/unifyUser/departReceive",
            "/unifyDepart/departReceive",
            "/loginByUsername",
            //危险性评估ccgf
            "/riskTranscriptManagement/ccgf/**",

    };


    public static String[] SWAGGER_NONE_SECURITY_URL_PATTERNS = {
            //swagger相关的
            "/doc.html",
            "/webjars/**",
            "/swagger-resources/**",
            "/v2/api-docs",
            "/v2/api-docs-ext",
            "/configuration/ui",
            "/configuration/security",
    };
}
