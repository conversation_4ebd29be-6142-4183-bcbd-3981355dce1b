package com.concise.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description: 标签相关枚举 message中的值是标签ID
 * @Title: LabelEnum
 * @Package com.concise.common.enums
 * @date 2022/03/25 11:50
 */
@Getter
public enum LabelEnum {

    // 矫正阶段_入矫初期
    PHASE_ONE("1", "0626c3e0fe954e0ebe77234b4500b591"),

    // 矫正阶段_矫正中期
    PHASE_TWO("2", "151308d389784cc58534b4f4a05208d6"),

    // 矫正阶段_矫正末期
    PHASE_THREE("3", "5c62374e27c44f2eae5dc6ff9ed6e20f");

    private final String code;

    private final String message;

    LabelEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

}
