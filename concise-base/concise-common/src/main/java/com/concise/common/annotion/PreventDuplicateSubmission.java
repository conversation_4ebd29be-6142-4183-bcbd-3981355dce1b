package com.concise.common.annotion;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 防重复提交注解
 * 基于Redis实现，通过用户ID+方法签名+参数生成唯一key，在指定时间内防止重复提交
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface PreventDuplicateSubmission {

    /**
     * 防重复提交的时间间隔，默认5秒
     */
    long interval() default 7;

    /**
     * 时间单位，默认秒
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * 提示信息
     */
    String message() default "操作过于频繁，请稍后再试";

    /**
     * 是否包含请求参数生成key，默认true
     * 如果为false，则只根据用户ID+方法签名生成key
     */
    boolean includeParams() default true;

    /**
     * 自定义key的前缀，默认为空
     */
    String keyPrefix() default "";
}
