package com.concise.common.util;

import cn.hutool.core.collection.CollectionUtil;
import com.concise.common.consts.SymbolConstant;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
public class SplitUtils {

    /**
     * 字符串转list
     *
     * @param str
     * @param symbolConstant
     * @return
     */
    public static List<String> splitToList(String str, String symbolConstant) {
        String[] split = str.split(symbolConstant);
        return CollectionUtil.newArrayList(split);
    }

    /**
     * 字符串转set
     *
     * @param str
     * @param symbolConstant
     * @return
     */
    public static Set<String> splitToSet(String str, String symbolConstant) {
        String[] split = str.split(symbolConstant);
        return CollectionUtil.newHashSet(split);
    }

    /**
     * 字符串转list，使用逗号","
     *
     * @param str
     * @return
     */

    public static List<String> splitToList(String str) {
        return CollectionUtil.newArrayList(splitToList(str, SymbolConstant.COMMA));
    }

    /**
     * 字符串转set,使用逗号","
     *
     * @param str
     * @return
     */
    public static Set<String> splitToSet(String str) {
        return CollectionUtil.newHashSet(splitToSet(str, SymbolConstant.COMMA));
    }

}
