package com.concise.gen.portraitImage.correctionquestionitem.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.portraitImage.correctionquestionitem.entity.CorrectionQuestionItem;
import com.concise.gen.portraitImage.correctionquestionitem.enums.CorrectionQuestionItemExceptionEnum;
import com.concise.gen.portraitImage.correctionquestionitem.mapper.CorrectionQuestionItemMapper;
import com.concise.gen.portraitImage.correctionquestionitem.param.CorrectionQuestionItemParam;
import com.concise.gen.portraitImage.correctionquestionitem.service.CorrectionQuestionItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 量表配置--试题选项表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:38
 */
@Service
public class CorrectionQuestionItemServiceImpl extends ServiceImpl<CorrectionQuestionItemMapper, CorrectionQuestionItem> implements CorrectionQuestionItemService {

    @Override
    public PageResult<CorrectionQuestionItem> page(CorrectionQuestionItemParam correctionQuestionItemParam) {
        QueryWrapper<CorrectionQuestionItem> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionQuestionItemParam)) {

            // 根据问题id 查询
            if (ObjectUtil.isNotEmpty(correctionQuestionItemParam.getQuestionId())) {
                queryWrapper.lambda().eq(CorrectionQuestionItem::getQuestionId, correctionQuestionItemParam.getQuestionId());
            }
            // 根据选项序号 查询
            if (ObjectUtil.isNotEmpty(correctionQuestionItemParam.getOrderIndex())) {
                queryWrapper.lambda().eq(CorrectionQuestionItem::getOrderIndex, correctionQuestionItemParam.getOrderIndex());
            }
            // 根据选项内容 查询
            if (ObjectUtil.isNotEmpty(correctionQuestionItemParam.getContent())) {
                queryWrapper.lambda().eq(CorrectionQuestionItem::getContent, correctionQuestionItemParam.getContent());
            }
            // 根据是否有关联问题（0：没有 1：有） 查询
            if (ObjectUtil.isNotEmpty(correctionQuestionItemParam.getHaveHigherQuestion())) {
                queryWrapper.lambda().eq(CorrectionQuestionItem::getHaveHigherQuestion, correctionQuestionItemParam.getHaveHigherQuestion());
            }
            // 根据是否删除（0：未删除，1删除） 查询
            if (ObjectUtil.isNotEmpty(correctionQuestionItemParam.getDelFlag())) {
                queryWrapper.lambda().eq(CorrectionQuestionItem::getDelFlag, correctionQuestionItemParam.getDelFlag());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionQuestionItem> list(CorrectionQuestionItemParam correctionQuestionItemParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionQuestionItemParam correctionQuestionItemParam) {
        CorrectionQuestionItem correctionQuestionItem = new CorrectionQuestionItem();
        BeanUtil.copyProperties(correctionQuestionItemParam, correctionQuestionItem);
        this.save(correctionQuestionItem);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionQuestionItemParam correctionQuestionItemParam) {
        this.removeById(correctionQuestionItemParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionQuestionItemParam correctionQuestionItemParam) {
        CorrectionQuestionItem correctionQuestionItem = this.queryCorrectionQuestionItem(correctionQuestionItemParam);
        BeanUtil.copyProperties(correctionQuestionItemParam, correctionQuestionItem);
        this.updateById(correctionQuestionItem);
    }

    @Override
    public CorrectionQuestionItem detail(CorrectionQuestionItemParam correctionQuestionItemParam) {
        return this.queryCorrectionQuestionItem(correctionQuestionItemParam);
    }

    /**
     * 获取量表配置--试题选项表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:38
     */
    private CorrectionQuestionItem queryCorrectionQuestionItem(CorrectionQuestionItemParam correctionQuestionItemParam) {
        CorrectionQuestionItem correctionQuestionItem = this.getById(correctionQuestionItemParam.getId());
        if (ObjectUtil.isNull(correctionQuestionItem)) {
            throw new ServiceException(CorrectionQuestionItemExceptionEnum.NOT_EXIST);
        }
        return correctionQuestionItem;
    }
}
