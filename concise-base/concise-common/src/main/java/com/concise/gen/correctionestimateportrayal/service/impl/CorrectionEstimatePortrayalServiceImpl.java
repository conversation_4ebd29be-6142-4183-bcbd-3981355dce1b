package com.concise.gen.correctionestimateportrayal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.base.entity.BaseEntity;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimateportrayal.dto.PortrayalKfmxDTO;
import com.concise.gen.correctionestimateportrayal.entity.CorrectionEstimatePortrayal;
import com.concise.gen.correctionestimateportrayal.entity.vo.PortrayalGjqsVO;
import com.concise.gen.correctionestimateportrayal.mapper.CorrectionEstimatePortrayalMapper;
import com.concise.gen.correctionestimateportrayal.param.CorrectionEstimatePortrayalParam;
import com.concise.gen.correctionestimateportrayal.service.CorrectionEstimatePortrayalService;
import com.concise.gen.correctionestimateportrayaldetail.entity.CorrectionEstimatePortrayalDetail;
import com.concise.gen.correctionestimateportrayaldetail.service.CorrectionEstimatePortrayalDetailService;
import com.concise.gen.correctionestimateportrayalparticulars.entity.CorrectionEstimatePortrayalParticulars;
import com.concise.gen.correctionestimateportrayalparticulars.service.CorrectionEstimatePortrayalParticularsService;
import com.concise.gen.correctionestimatereport.dto.ReportDetailRiskDTO;
import com.concise.gen.correctionestimatereport.entity.CorrectionEstimateReport;
import com.concise.gen.correctionestimatereport.service.CorrectionEstimateReportService;
import com.concise.gen.correctionestimatestay.enums.CorrectionEstimateStayExceptionEnum;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformationNoEncryption;
import com.concise.gen.correctionobjectinformation.mapper.CorrectionObjectInformationMapper;
import com.concise.gen.scoringmodeldetailmanageportrayal.entity.ScoringModelDetailManagePortrayal;
import com.concise.gen.scoringmodeldetailmanageportrayal.param.ScoringModelDetailManagePortrayalParam;
import com.concise.gen.scoringmodeldetailmanageportrayal.service.ScoringModelDetailManagePortrayalService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 在矫评估service接口实现类
 *
 * <AUTHOR>
 * @date 2022-05-17 11:48:38
 */
@Service
@Slf4j
public class CorrectionEstimatePortrayalServiceImpl extends ServiceImpl<CorrectionEstimatePortrayalMapper, CorrectionEstimatePortrayal> implements CorrectionEstimatePortrayalService {

    @Resource
    private CorrectionObjectInformationMapper correctionObjectInformationMapper;

    @Resource
    private ScoringModelDetailManagePortrayalService scoringModelDetailManagePortrayalService;

    @Autowired
    private CorrectionEstimatePortrayalDetailService correctionEstimatePortrayalDetailService;

    @Autowired
    private CorrectionEstimatePortrayalParticularsService correctionEstimatePortrayalParticularsService;

    @Autowired
    private CorrectionEstimateReportService reportService;

    @Override
    public PageResult<CorrectionEstimatePortrayal> page(CorrectionEstimatePortrayalParam correctionEstimatePortrayalParam, Set<String> org) {
        QueryWrapper<CorrectionEstimatePortrayal> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(
                CorrectionEstimatePortrayal::getId, CorrectionEstimatePortrayal::getSqjzryId,
                CorrectionEstimatePortrayal::getCreateTime, CorrectionEstimatePortrayal::getXbName,
                CorrectionEstimatePortrayal::getJzlbName, CorrectionEstimatePortrayal::getSfzh,
                CorrectionEstimatePortrayal::getSqjzryName, CorrectionEstimatePortrayal::getEstimateMonth,
                CorrectionEstimatePortrayal::getJzjgName, CorrectionEstimatePortrayal::getGrlxdh,
                CorrectionEstimatePortrayal::getLevelDown, CorrectionEstimatePortrayal::getSqjzksrq,
                CorrectionEstimatePortrayal::getSqjzjsrq, CorrectionEstimatePortrayal::getScoreEstimate,
                CorrectionEstimatePortrayal::getScoreTotal, CorrectionEstimatePortrayal::getXlhxScore,
                CorrectionEstimatePortrayal::getZfhxScore, CorrectionEstimatePortrayal::getJthxScore,
                CorrectionEstimatePortrayal::getJyhxScore, CorrectionEstimatePortrayal::getXyhxScore,
                CorrectionEstimatePortrayal::getJbhxScore, CorrectionEstimatePortrayal::getSfcn, CorrectionEstimatePortrayal::getSfcnName,
                CorrectionEstimatePortrayal::getMz, CorrectionEstimatePortrayal::getMzName,
                CorrectionEstimatePortrayal::getUpdateTime, CorrectionEstimatePortrayal::getXlhxScore,
                CorrectionEstimatePortrayal::getZfhxScore, CorrectionEstimatePortrayal::getJyhxScore,
                CorrectionEstimatePortrayal::getJthxScore, CorrectionEstimatePortrayal::getJbhxScore,
                CorrectionEstimatePortrayal::getJyhxScore
        );
        if (ObjectUtil.isNotNull(correctionEstimatePortrayalParam)) {
            // 根据矫正机构id 查询
            queryWrapper.lambda().in(CorrectionEstimatePortrayal::getJzjg, org);
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParam.getSqjzryName())) {
                queryWrapper.lambda().like(CorrectionEstimatePortrayal::getSqjzryName, correctionEstimatePortrayalParam.getSqjzryName());
            }
            // 根据评估月份 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParam.getEstimateMonth())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayal::getEstimateMonth, correctionEstimatePortrayalParam.getEstimateMonth());
            } else {
                SimpleDateFormat sf = new SimpleDateFormat("yyyyMM");
                int month = Integer.parseInt(sf.format(new Date()));
                queryWrapper.lambda().eq(CorrectionEstimatePortrayal::getEstimateMonth, month);
            }
        }
        queryWrapper.lambda().eq(CorrectionEstimatePortrayal::getDelFlag, 0);
        queryWrapper.lambda().orderByDesc(CorrectionEstimatePortrayal::getEstimateMonth);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionEstimatePortrayal> list(CorrectionEstimatePortrayalParam correctionEstimatePortrayalParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionEstimatePortrayalParam correctionEstimatePortrayalParam, String scoringModelId) {
        CorrectionEstimatePortrayal correctionEstimatePortrayal = new CorrectionEstimatePortrayal();
        BeanUtil.copyProperties(correctionEstimatePortrayalParam, correctionEstimatePortrayal);
        //总分
        BigDecimal tot = getTotal(scoringModelId);
        correctionEstimatePortrayal.setScoreTotal(tot);
        //计算评估分
        //correctionEstimateStay.setScoreEstimate(getScoreEstimate(correctionEstimateStay, scoringModelId));
//        BigDecimal[] scoreArr = getScoreEstimate(correctionEstimatePortrayal, scoringModelId);
//        correctionEstimatePortrayal.setScoreEstimate(scoreArr[0]);
//        correctionEstimatePortrayal.setRcjgScore(scoreArr[1]);
//        correctionEstimatePortrayal.setCfScore(scoreArr[2]);
//        correctionEstimatePortrayal.setXwdtScore(scoreArr[3]);
//        correctionEstimatePortrayal.setXlztScore(scoreArr[4]);
        this.save(correctionEstimatePortrayal);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionEstimatePortrayalParam correctionEstimatePortrayalParam) {
        CorrectionEstimatePortrayal correctionEstimatePortrayal = new CorrectionEstimatePortrayal();
        correctionEstimatePortrayal.setId(correctionEstimatePortrayalParam.getId());
        correctionEstimatePortrayal.setDelFlag(1);
        this.updateById(correctionEstimatePortrayal);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionEstimatePortrayalParam correctionEstimatePortrayalParam, String scoringModelId) {
        CorrectionEstimatePortrayal correctionEstimatePortrayal = this.queryCorrectionEstimateStay(correctionEstimatePortrayalParam);
        BeanUtil.copyProperties(correctionEstimatePortrayalParam, correctionEstimatePortrayal);
        //总分
        BigDecimal tot = getTotal(scoringModelId);
        correctionEstimatePortrayal.setScoreTotal(tot);
        //计算评估分
        //评分模型
        ScoringModelDetailManagePortrayalParam scoringModelDetailManagePortrayalParam = new ScoringModelDetailManagePortrayalParam();
        scoringModelDetailManagePortrayalParam.setScoringModelId("1598151776047714306");
        List<ScoringModelDetailManagePortrayal> detailManagePortrayals = scoringModelDetailManagePortrayalService.list(scoringModelDetailManagePortrayalParam);
        getScoreEstimateNew(correctionEstimatePortrayal, detailManagePortrayals);
    }

    @Override
    public CorrectionEstimatePortrayal detail(CorrectionEstimatePortrayalParam correctionEstimatePortrayalParam) {
        return this.queryCorrectionEstimateStay(correctionEstimatePortrayalParam);
    }

    /**
     * 获取在矫评估
     *
     * <AUTHOR>
     * @date 2022-05-17 11:48:38
     */
    private CorrectionEstimatePortrayal queryCorrectionEstimateStay(CorrectionEstimatePortrayalParam correctionEstimatePortrayalParam) {
        CorrectionEstimatePortrayal correctionEstimatePortrayal = this.getById(correctionEstimatePortrayalParam.getId());
        if (ObjectUtil.isNull(correctionEstimatePortrayal)) {
            throw new ServiceException(CorrectionEstimateStayExceptionEnum.NOT_EXIST);
        }
        return correctionEstimatePortrayal;
    }

    /**
     * 根据选中的矫正对象转换下拉选项的值
     *
     * <AUTHOR>
     * @date 2022-05-13 14:52:58
     */
    @Override
    public CorrectionEstimatePortrayal transform(String sqjzryId, int estimateMonth) {
        CorrectionEstimatePortrayal stay = new CorrectionEstimatePortrayal();
        // 先根据万达平台的社区矫正人员id 查找云雀平台的人员id
        String correctionObjectId = this.baseMapper.getCorrectionObjectId(sqjzryId);
        //信息化监管违规*训诫*警告*治安处罚*提请逮捕*提请撤缓*提请撤销假释*提请收监执行*工作变动*夜不归宿*是否流动人口
        List<Integer> numList = this.baseMapper.getNums(sqjzryId, correctionObjectId, estimateMonth);
        stay.setYqViolate(numList.get(0)); //信息化监管违规
        stay.setAdvise(numList.get(1)); //训诫
        stay.setWarn(numList.get(2)); //警告
        stay.setPublicSecurity(numList.get(3)); //治安处罚
        stay.setAskArrest(numList.get(4)); //提请逮捕
        stay.setCancelProbation(numList.get(5)); //提请撤缓
        stay.setCancelParole(numList.get(6)); //提请撤销假释
        stay.setCommittedToPrison(numList.get(7)); //提请收监执行
        stay.setWorkChange(numList.get(8)); //工作变动
        stay.setNightOut(numList.get(9)); //夜不归宿
        stay.setPraise(numList.get(10));//受到表扬
        stay.setPenaltyDown(numList.get(11));//获得减刑
        stay.setIsLdrk(numList.get(12) == 0 ? 0 : 1);//是否流动人口
        //心情码  5：绿码、4：蓝码、3：黄码、2：橙码、1：红码'
        String moonCode = this.baseMapper.getMoonCode(sqjzryId);
        if (ObjectUtil.isNotEmpty(moonCode) && 1 == moonCode.trim().length() && Integer.parseInt(moonCode) < 4) {
            stay.setHeartCode(Integer.parseInt(moonCode));
        }
        //是否等级降到严管
        stay.setLevelDown(0);
        String gljb = this.baseMapper.getGljb(sqjzryId, estimateMonth);
        if ("1".equals(gljb)) {
            stay.setLevelDown(1);
        }
        return stay;
    }

    /**
     * 初始化在矫评估数据,每月月初一次
     */
    @Override
    public void initCorrectionEstimateStay() {
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMM");
        int month = Integer.parseInt(sf.format(new Date()));
        CorrectionEstimatePortrayal stay = null;
        List<CorrectionObjectInformationNoEncryption> list = correctionObjectInformationMapper.getCorrectionMultifieldNoEncryption();
        //去除本月要解矫的
        //  List<CorrectionObjectInformationNoEncryption> first = list.stream().filter(item -> item.getSqjzjsrq() != null && Integer.parseInt(sf.format(item.getSqjzjsrq())) > month).collect(Collectors.toList());
        for (CorrectionObjectInformationNoEncryption correctionObjectInformation : list) {
            stay = new CorrectionEstimatePortrayal();
            stay.setSqjzryId(correctionObjectInformation.getId());
            stay.setSqjzryName(correctionObjectInformation.getXm());
            stay.setSfzh(correctionObjectInformation.getSfzh());
            stay.setXb(correctionObjectInformation.getXb());
            stay.setXbName(correctionObjectInformation.getXbName());
            stay.setGrlxdh(correctionObjectInformation.getGrlxdh());
            stay.setJzjg(correctionObjectInformation.getJzjg());
            stay.setJzjgName(correctionObjectInformation.getJzjgName());
            stay.setJzlb(correctionObjectInformation.getJzlb());
            stay.setJzlbName(correctionObjectInformation.getJzlbName());
            stay.setJtzm(correctionObjectInformation.getJtzm());
            stay.setJtzmName(correctionObjectInformation.getJtzmName());
            stay.setJzdz(new StringBuilder().append(correctionObjectInformation.getGdjzdszdsName()).append(correctionObjectInformation.getGdjzdszxqName()).append(correctionObjectInformation.getGdjzdmx()).toString());
            stay.setSqjzksrq(correctionObjectInformation.getSqjzksrq());
            stay.setSqjzjsrq(correctionObjectInformation.getSqjzjsrq());
            stay.setEstimateMonth(month);
            stay.setSfcn(correctionObjectInformation.getSfcn());
            stay.setSfcnName(correctionObjectInformation.getSfcnName());
            stay.setMz(correctionObjectInformation.getMz());
            stay.setMzName(correctionObjectInformation.getMzName());
            //年龄
            try {
                stay.setAge(IdcardUtil.getAgeByIdCard(correctionObjectInformation.getSfzh()));
            } catch (Exception e) {
                log.info("身份证号{}", correctionObjectInformation.getSfzh());
            }
            //学历
            if (correctionObjectInformation.getWhcdName() != null) {
                switch (correctionObjectInformation.getWhcdName()) {
                    case "本科":
                    case "硕士":
                    case "博士":
                    case "博士及以上（包含博士）":
                        stay.setEducation("1");
                        break;
                    case "大专":
                        stay.setEducation("2");
                        break;
                    case "高中":
                        stay.setEducation("3");
                        break;
                    case "中专和中技":
                        stay.setEducation("4");
                        break;
                    case "初中":
                        stay.setEducation("5");
                        break;
                    case "小学":
                        stay.setEducation("6");
                        break;
                    case "文盲":
                        stay.setEducation("7");
                        break;
                }
            }
            stay.setEducation(correctionObjectInformation.getWhcdName());
            //婚姻状况
            stay.setHyzk(correctionObjectInformation.getHyzkName());
            //是否有前科
            stay.setCriminality(StringUtils.isBlank(correctionObjectInformation.getSfyqk()) ? 0 : Integer.parseInt(correctionObjectInformation.getSfyqk()));
            //是否累计惯犯
            stay.setRecidivist(StringUtils.isBlank(correctionObjectInformation.getSflf()) ? 0 : Integer.parseInt(correctionObjectInformation.getSflf()));
            //就业就学情况
            stay.setJobCondition(correctionObjectInformation.getJyjxqkName());
            this.save(stay);

        }
    }

    /**
     * 每天更新在矫评估分数
     */
    @Override
    public void updateCorrectionEstimateStay() {
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMM");
        int month = Integer.parseInt(sf.format(new Date()));
        //评分模型
        ScoringModelDetailManagePortrayalParam scoringModelDetailManagePortrayalParam = new ScoringModelDetailManagePortrayalParam();
        scoringModelDetailManagePortrayalParam.setScoringModelId("1595611387696734210");
        List<ScoringModelDetailManagePortrayal> detailManagePortrayals = scoringModelDetailManagePortrayalService.list(scoringModelDetailManagePortrayalParam);
        QueryWrapper<CorrectionEstimatePortrayal> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CorrectionEstimatePortrayal::getDelFlag, 0);
        queryWrapper.lambda().eq(CorrectionEstimatePortrayal::getEstimateMonth, month);
        List<CorrectionEstimatePortrayal> list = this.list(queryWrapper);
        //如果list为空初始化
        for (CorrectionEstimatePortrayal correctionEstimatePortrayal : list) {
            CorrectionEstimatePortrayal newObj = transform(correctionEstimatePortrayal.getSqjzryId(), month);
            correctionEstimatePortrayal.setYqViolate(newObj.getYqViolate()); //信息化监管违规
            correctionEstimatePortrayal.setAdvise(newObj.getAdvise()); //训诫
            correctionEstimatePortrayal.setWarn(newObj.getWarn()); //警告
            correctionEstimatePortrayal.setPublicSecurity(newObj.getPublicSecurity()); //治安处罚
            correctionEstimatePortrayal.setAskArrest(newObj.getAskArrest()); //提请逮捕
            correctionEstimatePortrayal.setCancelProbation(newObj.getCancelProbation()); //提请撤缓
            correctionEstimatePortrayal.setCancelParole(newObj.getCancelParole()); //提请撤销假释
            correctionEstimatePortrayal.setCommittedToPrison(newObj.getCommittedToPrison()); //提请收监执行
            correctionEstimatePortrayal.setWorkChange(newObj.getWorkChange()); //工作变动
            correctionEstimatePortrayal.setNightOut(newObj.getNightOut()); //夜不归宿
            correctionEstimatePortrayal.setPraise(newObj.getPraise());//受到表扬
            correctionEstimatePortrayal.setPenaltyDown(newObj.getPenaltyDown());//获得减刑
            correctionEstimatePortrayal.setHeartCode(newObj.getHeartCode());//心情码
            correctionEstimatePortrayal.setLevelDown(newObj.getLevelDown());//是否降到严管
            correctionEstimatePortrayal.setIsLdrk(newObj.getIsLdrk());//是否流动人口
            //correctionEstimatePortrayal.setAdministrativePenalty();//行政处罚
            //计算评估分、日常监管分数、处罚分数、行为动态分数、心理状态分数
            getScoreEstimateNew(correctionEstimatePortrayal, detailManagePortrayals);
        }
    }

    /**
     * 计算评估分、日常监管分数、处罚分数、行为动态分数、心理状态分数
     *
     * @param portrayal
     * @param scoringModelDetailManagePortrayals 模型详情
     * @return
     */
    @Override
    public CorrectionEstimatePortrayal getScoreEstimate(CorrectionEstimatePortrayal portrayal, List<ScoringModelDetailManagePortrayal> scoringModelDetailManagePortrayals) {
        int defaultIndexPoint = 100;
        int age = portrayal.getAge();
        Map<String, Integer> scoringModelDetailMap = scoringModelDetailManagePortrayals.stream().collect(Collectors.toMap(item -> item.getDetailName(), item -> item.getIndexPoint()));
//--------------------------------------------------------------------------------------------------------------------------
        //日常监管得分计算规则：
        //1、降到严管扣50分
        //2、出现一次违规，扣20分，扣完为止
        int ygqzf = MapUtil.getInt(scoringModelDetailMap, "是否降到严管", defaultIndexPoint);
        int wgqzf = MapUtil.getInt(scoringModelDetailMap, "信息化监管违规次数", defaultIndexPoint);
        int rcjgqzzf = ygqzf + wgqzf;//监管权重总分
        int rcjgScore = 0;//日常监管得分
        int ygdf = 0;
        int wgdf = 0;
        if (portrayal.getLevelDown() == 1) {
            //是否降到严管
            ygdf = ygqzf - 50 <= 0 ? 0 : ygqzf - 50;
        } else {
            ygdf = ygqzf - 0;
        }
        wgdf = wgqzf - portrayal.getYqViolate() * 20 <= 0 ? 0 : wgqzf - portrayal.getYqViolate() * 20;
        rcjgScore = ygdf + wgdf;
        portrayal.setRcjgScore(new BigDecimal(rcjgScore));
//--------------------------------------------------------------------------------------------------------------------------
        //处罚得分计算规则：
        //
        //受到一次训诫扣25分、受到一次警告扣75分、
        // 受到一次治安处罚扣150分、提请撤缓、提请撤销假释，扣250分，
        // 出现过一次逮捕扣200分，行政处罚，扣100，提请收监执行，扣300
        int xjqzf = MapUtil.getInt(scoringModelDetailMap, "训诫次数", defaultIndexPoint);
        int zgqzf = MapUtil.getInt(scoringModelDetailMap, "警告次数", defaultIndexPoint);
        int zaqzf = MapUtil.getInt(scoringModelDetailMap, "治安处罚次数", defaultIndexPoint);
        int xzqzf = MapUtil.getInt(scoringModelDetailMap, "行政处罚", defaultIndexPoint);
        int txcxqzf = MapUtil.getInt(scoringModelDetailMap, "提请撤销假释", defaultIndexPoint);
        int tqchqzf = MapUtil.getInt(scoringModelDetailMap, "提请撤缓", defaultIndexPoint);
        int tqdbqzf = MapUtil.getInt(scoringModelDetailMap, "提请逮捕", defaultIndexPoint);
        int tqsjqzf = MapUtil.getInt(scoringModelDetailMap, "提请收监执行", defaultIndexPoint);
        int cfqzzf = xjqzf + zgqzf + zaqzf + xzqzf + txcxqzf + tqchqzf + tqdbqzf + tqsjqzf;//处罚权重总分
        int cfScore;//处罚得分
        int xjdf = xjqzf - portrayal.getAdvise() * 25 <= 0 ? 0 : xjqzf - portrayal.getAdvise() * 25;//训诫
        int zgdf = zgqzf - portrayal.getAdvise() * 75 <= 0 ? 0 : zgqzf - portrayal.getAdvise() * 75;//警告
        int zadf = zaqzf - portrayal.getPublicSecurity() * 150 <= 0 ? 0 : zaqzf - portrayal.getPublicSecurity();//治安处罚
        int txcxqdf = txcxqzf - portrayal.getCancelParole() * 250 <= 0 ? 0 : txcxqzf - portrayal.getCancelParole() * 250;//提请撤销假释
        int tqchqdf = tqchqzf - portrayal.getCancelProbation() * 250 <= 0 ? 0 : tqchqzf - portrayal.getCancelParole() * 250;//提请撤缓
        int tqdbqdf = tqdbqzf - portrayal.getAskArrest() * 200 <= 0 ? 0 : tqdbqzf - portrayal.getAskArrest() * 200;//提请逮捕
        int tqsjqdf = tqsjqzf - portrayal.getCommittedToPrison() * 300 <= 0 ? 0 : tqsjqzf - portrayal.getCommittedToPrison() * 300;//提请收监执行
        cfScore = xjdf + zgdf + zadf + txcxqdf + tqchqdf + tqdbqdf + tqsjqdf;
        portrayal.setCfScore(new BigDecimal(cfScore));
//--------------------------------------------------------------------------------------------------------------------------
        //行为动态
        //1.非流动人口换算规则：出现一次扣10分，出现2次，扣分翻倍，即20分，3次扣40分，依次类推，扣完为止。
        //2.流动人口换算规则：出现一次扣15分，出现2次，扣分翻倍，即30分，依次类推，扣完为止。
        int xwdtqzf = MapUtil.getInt(scoringModelDetailMap, "行为动态", defaultIndexPoint);
        int xwdtScore = 0;//行为动态得分
        int isLdrk = portrayal.getIsLdrk();
        if (isLdrk == 0 && portrayal.getNightOut() > 0) {
            //非流动人口
            xwdtScore = (int) (10 * Math.pow(2, portrayal.getNightOut() - 1));
        } else if (isLdrk == 1 && portrayal.getNightOut() > 0) {
            //流动人口
            xwdtScore = (int) (15 * Math.pow(2, portrayal.getNightOut() - 1));
        }
        portrayal.setXwdtScore(new BigDecimal(xwdtScore));
        List<CorrectionEstimatePortrayalDetail> correctionEstimatePortrayalDetails = correctionEstimatePortrayalDetailService.listByPortrayalId(portrayal.getId(), null);
        Map<Integer, CorrectionEstimatePortrayalDetail> detailMap = correctionEstimatePortrayalDetails.stream().collect(Collectors.toMap(item -> item.getType(), item -> item));
//--------------------------------------------------------------------------------------------------------------------------
//    心理画像
        //出现红码：扣70，以下指标无需计算
        //出现橙码或黄码：扣35
        StringBuilder xlhxmsOne = new StringBuilder("1、该矫正对象心理画像风险为");
        StringBuilder xlhxmsTwo = new StringBuilder("2、该矫正对象心晴码显示");
        StringBuilder xlhxmsThree = new StringBuilder("3、该矫正对象认罪服法态度");
        StringBuilder xlhxmsFour = new StringBuilder("4、该矫正对象在心情日记中出现");
        StringBuilder xlhxmsFive = new StringBuilder("5、该矫正对象在心情日记中出现");
        int xqmqzf = MapUtil.getInt(scoringModelDetailMap, "心晴码", defaultIndexPoint);
        int jsbqzf = MapUtil.getInt(scoringModelDetailMap, "是否有精神病史或精神病遗传史", defaultIndexPoint);
        int fmxqqzf = MapUtil.getInt(scoringModelDetailMap, "负面心情", defaultIndexPoint);
        int pjkfqzf = MapUtil.getInt(scoringModelDetailMap, "对本次法院最终判决结果的看法", defaultIndexPoint);
        int fmcqzf = MapUtil.getInt(scoringModelDetailMap, "负面关键词预警", defaultIndexPoint);
        int xlhxqzf = xqmqzf + jsbqzf + fmxqqzf + pjkfqzf + fmcqzf;//心理画像权重总分
        int xlhxScore;//心理画像得分
        int xlhxkf = 0;
        List<PortrayalKfmxDTO> hxmxdtos = new ArrayList<>();
        PortrayalKfmxDTO hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("心晴码");
        hxKfmxDTO.setQzf(xqmqzf);
        switch (portrayal.getHeartCode()) {
            case 0:
                xlhxmsTwo.append("<h2>无码</h2>");
                hxKfmxDTO.setZbz("无码");
                break;
            case 1:
                xlhxkf = xlhxkf + 70;
                xlhxmsTwo.append("<h2>红码</h2>");
                hxKfmxDTO.setZbz("红码");
                hxKfmxDTO.setKf(70);
                break;
            case 2:
                xlhxkf = xlhxkf + 35;
                xlhxmsTwo.append("<h2>橙码</h2>");
                hxKfmxDTO.setZbz("橙码");
                hxKfmxDTO.setKf(35);
                break;
            case 3:
                xlhxkf = xlhxkf + 35;
                xlhxmsTwo.append("<h2>黄码</h2>");
                hxKfmxDTO.setZbz("黄码");
                hxKfmxDTO.setKf(35);
                break;
            case 4:
                xlhxmsTwo.append("<h2>蓝码</h2>");
                hxKfmxDTO.setZbz("蓝码");
                break;
            case 5:
                xlhxmsTwo.append("<h2>绿码</h2>");
                hxKfmxDTO.setZbz("绿码");
                break;
        }
        hxmxdtos.add(hxKfmxDTO);
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("有无精神病史或精神病遗传史");
        hxKfmxDTO.setQzf(jsbqzf);
        //有精神病史或精神病遗传史
        if (portrayal.getIsPsychosis() == 1) {
            xlhxkf = xlhxkf + 10;
            hxKfmxDTO.setZbz("有");
            hxKfmxDTO.setKf(10);
        } else if (portrayal.getIsPsychosis() == 0) {
            hxKfmxDTO.setZbz("无");
        }
        hxmxdtos.add(hxKfmxDTO);
        //负面心情
        //"交心日记中“想发火”1次，扣10分
        //交心日记中“想发火”、“不顺心”3次以上（含），扣20分
        //交心日记中“想发火”、“不顺心”5次以上（含），扣30分"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("负面心情");
        hxKfmxDTO.setQzf(fmxqqzf);
        hxKfmxDTO.setZbz("交心日记中出现负面心情" + portrayal.getNegativeMood() + "次");
        xlhxmsFive.append("<h2>负面心情").append(portrayal.getNegativeMood()).append("次</h2>");
        if (portrayal.getNegativeMood() == 0) {
            hxKfmxDTO.setZbz("交心日记中未出现负面心情");
        } else if (portrayal.getNegativeMood() >= 1 && portrayal.getNegativeMood() < 3) {
            xlhxkf = xlhxkf + 10;
            hxKfmxDTO.setKf(10);
        } else if (portrayal.getNegativeMood() >= 3 && portrayal.getNegativeMood() < 5) {
            xlhxkf = xlhxkf + 20;
            hxKfmxDTO.setKf(20);
        } else if (portrayal.getNegativeMood() >= 5) {
            xlhxkf = xlhxkf + 30;
            hxKfmxDTO.setKf(30);
        }
        hxmxdtos.add(hxKfmxDTO);
        //对本次法院最终判决结果的看法
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("认罪服法态度");
        hxKfmxDTO.setQzf(pjkfqzf);
        switch (portrayal.getWritteWritten()) {
            case 1:
                xlhxmsThree.append("很好，认为本次法院最终判决结果<h2>很轻</h2>");
                hxKfmxDTO.setZbz("对本次法院最终判决结果的看法：很轻");
                break;
            case 2:
                xlhxmsThree.append("较好，认为本次法院最终判决结果<h2>较轻</h2>");
                hxKfmxDTO.setZbz("对本次法院最终判决结果的看法：较轻");
                break;
            case 3:
                xlhxmsThree.append("比较好，认为本次法院最终判决结果<h2>适当</h2>");
                hxKfmxDTO.setZbz("对本次法院最终判决结果的看法：适当");
                break;
            case 4:
                xlhxmsThree.append("比较差，认为本次法院最终判决结果<h2>比较重</h2>");
                hxKfmxDTO.setZbz("对本次法院最终判决结果的看法：比较重");
                hxKfmxDTO.setKf(5);
                xlhxkf = xlhxkf + 5;
                break;
            case 5:
                xlhxmsThree.append("很差，认为本次法院最终判决结果<h2>太重了</h2>");
                hxKfmxDTO.setZbz("对本次法院最终判决结果的看法：太重了");
                hxKfmxDTO.setKf(10);
                xlhxkf = xlhxkf + 10;
                break;
            default:
                xlhxmsThree.append("很差，认为本次法院最终判决结果<h2>未知</h2>");
                hxKfmxDTO.setZbz("对本次法院最终判决结果的看法：未知");
                hxKfmxDTO.setKf(0);
        }
        hxmxdtos.add(hxKfmxDTO);
        xlhxmsFour.append("<h2>负面关键词预警").append(portrayal.getNegativeWords()).append("次</h2>");
        //负面关键词预警
        //"交心日记中出现负面关键词预警：扣15；
        //出现超过3次以上（含）：扣30"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("负面关键词预警");
        hxKfmxDTO.setQzf(fmcqzf);
        hxKfmxDTO.setZbz("交心日记中出现负面关键词预警" + portrayal.getNegativeWords() + "次");
        if (portrayal.getNegativeWords() == 0) {
            hxKfmxDTO.setZbz("交心日记中未出现负面关键词预警");
            hxKfmxDTO.setKf(0);
        } else if (portrayal.getNegativeWords() >= 1 && portrayal.getNegativeWords() < 3) {
            xlhxkf = xlhxkf + 15;
            hxKfmxDTO.setKf(15);
        } else if (portrayal.getNegativeMood() >= 3) {
            xlhxkf = xlhxkf + 30;
            hxKfmxDTO.setKf(30);
        }
        hxmxdtos.add(hxKfmxDTO);
        xlhxScore = xlhxqzf - xlhxkf;
        portrayal.setXlhxScore(new BigDecimal(xlhxScore));
        CorrectionEstimatePortrayalDetail correctionEstimatePortrayalDetail = MapUtil.get(detailMap, 1, CorrectionEstimatePortrayalDetail.class);
        if (correctionEstimatePortrayalDetail == null) {
            correctionEstimatePortrayalDetail = new CorrectionEstimatePortrayalDetail();
        }
        if (xlhxScore >= 60) {
            correctionEstimatePortrayalDetail.setLevel("低风险");
            xlhxmsOne.append("<h2>低风险</h2>");
        } else if (xlhxScore >= 50 && xlhxScore < 60) {
            xlhxmsOne.append("<h2>较低风险</h2>");
            correctionEstimatePortrayalDetail.setLevel("较低风险");
        } else if (xlhxScore >= 30 && xlhxScore < 50) {
            xlhxmsOne.append("<h2>中风险</h2>");
            correctionEstimatePortrayalDetail.setLevel("中风险");
        } else if (xlhxScore >= 20 && xlhxScore < 30) {
            xlhxmsOne.append("<h2>较高风险</h2>");
            correctionEstimatePortrayalDetail.setLevel("较高风险");
        } else if (xlhxScore >= 0 && xlhxScore < 20) {
            xlhxmsOne.append("<h2>高风险</h2>");
            correctionEstimatePortrayalDetail.setLevel("高风险");
        }
        int qzzf = xqmqzf + jsbqzf + fmxqqzf + pjkfqzf + fmcqzf;//权重总分

        correctionEstimatePortrayalDetail.setType(1);
        correctionEstimatePortrayalDetail.setPortrayalId(portrayal.getId());
        correctionEstimatePortrayalDetail.setHxms(JSON.toJSONString(Lists.newArrayList(xlhxmsOne.toString(), xlhxmsTwo.toString(), xlhxmsThree.toString(), xlhxmsFour.toString())));
        correctionEstimatePortrayalDetail.setXlhxGjfx("该矫正对象入矫以来，心理画像从较低风险变为高风险，趋势恶化，建议重点关注。");
        correctionEstimatePortrayalDetail.setJy(JSON.toJSONString(Lists.newArrayList("1、心理专家介入。", "2、进行个别谈话了解近期矫正生活。")));
        correctionEstimatePortrayalDetail.setKfmx(JSON.toJSONString(hxmxdtos));
        correctionEstimatePortrayalDetail.setZf(String.valueOf(qzzf));
        correctionEstimatePortrayalDetail.setDf(String.valueOf(xlhxScore));
        correctionEstimatePortrayalDetail.setKf(String.valueOf(qzzf - xlhxScore));
        correctionEstimatePortrayalDetailService.saveOrUpdate(correctionEstimatePortrayalDetail);
//--------------------------------------------------------------------------------------------------------------------------
        CorrectionEstimatePortrayalDetail jyhxDetail = MapUtil.get(detailMap, 3, CorrectionEstimatePortrayalDetail.class);
        //就业画像
        StringBuilder jyhxmsOne = new StringBuilder("1、该矫正对象就业画像风险为");
        StringBuilder jyhxmsTwo = new StringBuilder("2、该矫正对象是");
        StringBuilder jyhxmsThree = new StringBuilder("3、该矫正对象在");
        int jyztqzf = MapUtil.getInt(scoringModelDetailMap, "就业状态", defaultIndexPoint);
        int gzbdqzf = MapUtil.getInt(scoringModelDetailMap, "工作变动", defaultIndexPoint);
        int jylxqzf = MapUtil.getInt(scoringModelDetailMap, "就业类型", defaultIndexPoint);
        int zwlxqzf = MapUtil.getInt(scoringModelDetailMap, "职务类型", defaultIndexPoint);
        int jyhxqzzf = jyztqzf + gzbdqzf + jylxqzf + zwlxqzf;//就业画像权重总分
        int jyhxScore;//就业画像总得分
        int jyztdf = 0;
        int gzbddf = 0;
        int jylxdf = 0;
        int zwlxdf = 0;
        //"老年（60岁以上）、未成年：就业画像满分，不扣分 ，以下指标无需计算
        //中年（45-60岁）、壮年（35-45岁）、青年（18-35岁）：进入第二步"
        jyhxmsTwo.append(portrayal.getJobCondition());
        hxmxdtos = new ArrayList<>();
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("年龄");
        if (age < 18) {
            //未成年
            jyhxmsTwo.append("未成年");
            jyztdf = jyztqzf;
            gzbddf = gzbdqzf;
            jylxdf = jylxqzf;
            zwlxdf = zwlxqzf;
            hxKfmxDTO.setZbz("未成年");
        } else if (age >= 18 && age < 35) {
            //青年（18-35岁）
            jyhxmsTwo.append("青年");
            hxKfmxDTO.setZbz("青年");
        } else if (age >= 35 && age < 45) {
            //壮年（35-45岁）
            jyhxmsTwo.append("壮年");
            hxKfmxDTO.setZbz("壮年");
        } else if (age >= 45 && age < 60) {
            //中年（45-60岁）.
            jyhxmsTwo.append("中年");
            hxKfmxDTO.setZbz("中年");
        } else if (age >= 60) {
            //老年（60岁以上）
            jyhxmsTwo.append("老年");
            hxKfmxDTO.setZbz("老年");
            jyztdf = jyztqzf;
            gzbddf = gzbdqzf;
            jylxdf = jylxqzf;
            zwlxdf = zwlxqzf;
        }
        hxmxdtos.add(hxKfmxDTO);
        jyhxmsTwo.append("一年内出现<h2>").append(portrayal.getJobMove()).append("次工作变动</h2>");
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("就业状态");
        hxKfmxDTO.setQzf(jyztqzf);
        if (age >= 18 && age < 60) {
            //就业状态
            hxKfmxDTO.setZbz(portrayal.getJobCondition());
            if (portrayal.getJobCondition().equals("无业")) {
                jyztdf = jyztqzf - 60;
                hxKfmxDTO.setKf(60);
            }
        }
        hxmxdtos.add(hxKfmxDTO);
        //工作变动
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("工作变动");
        hxKfmxDTO.setQzf(jyztqzf);
        if (age >= 18 && age < 60) {
            if (portrayal.getJobMove() == 0) {
                hxKfmxDTO.setZbz("一年内出现没有工作变动");
            } else if (portrayal.getJobMove() == 1) {
                gzbddf = gzbdqzf - 5;
                hxKfmxDTO.setZbz("一年内出现1次工作变动");
                hxKfmxDTO.setKf(5);
            } else if (portrayal.getJobMove() == 2) {
                gzbddf = gzbdqzf - 10;
                hxKfmxDTO.setZbz("一年内出现2次工作变动");
                hxKfmxDTO.setKf(10);
            } else if (portrayal.getJobMove() >= 3) {
                gzbddf = gzbdqzf - 20;
                hxKfmxDTO.setZbz("一年内出现3次及以上工作变动");
                hxKfmxDTO.setKf(20);
            }


        }
        hxmxdtos.add(hxKfmxDTO);
        //就业类型
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("就业类型");
        hxKfmxDTO.setQzf(jyztqzf);
        jyhxmsThree.append("<h2>");
        if (age >= 18 && age < 60) {
            switch (portrayal.getJobType()) {
                case 1:
                    jylxdf = jylxqzf;
                    hxKfmxDTO.setZbz("企事业单位");
                    jyhxmsThree.append("企事业单位");
                    break;
                case 2:
                    jylxdf = jylxqzf;
                    hxKfmxDTO.setZbz("个体户");
                    jyhxmsThree.append("个体户");
                    break;
                case 3:
                    jylxdf = jylxqzf - 20;
                    hxKfmxDTO.setZbz("自由职业");
                    jyhxmsThree.append("自由职业");
                    hxKfmxDTO.setKf(20);
                    break;
                case 4:
                    jylxdf = jylxqzf - 20;
                    hxKfmxDTO.setZbz("兼职");
                    jyhxmsThree.append("兼职");
                    hxKfmxDTO.setKf(20);
                    break;
            }
        }
        hxmxdtos.add(hxKfmxDTO);
        //职务类型
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("职务类型");
        hxKfmxDTO.setQzf(jyztqzf);
        if (age >= 18 && age < 60) {
            switch (portrayal.getPositionType()) {
                case 1:
                    zwlxdf = zwlxqzf - 20;
                    hxKfmxDTO.setZbz("普通员工");
                    jyhxmsThree.append("普通员工");
                    hxKfmxDTO.setKf(20);
                    break;
                case 2:
                    zwlxdf = zwlxqzf - 10;
                    hxKfmxDTO.setZbz("专业人才");
                    jyhxmsThree.append("专业人才");
                    hxKfmxDTO.setKf(10);
                    break;
                case 3:
                    zwlxdf = zwlxqzf - 10;
                    hxKfmxDTO.setZbz("管理人员");
                    jyhxmsThree.append("管理人员");
                    hxKfmxDTO.setKf(10);
                    break;
                case 4:
                    zwlxdf = zwlxqzf;
                    hxKfmxDTO.setZbz("老板");
                    jyhxmsThree.append("老板");
                    break;
            }
        }
        jyhxmsThree.append("</h2>");
        hxmxdtos.add(hxKfmxDTO);
        jyhxScore = jyztdf + gzbddf + jylxdf + zwlxdf;
        portrayal.setJyhxScore(new BigDecimal(jyhxScore));
        if (jyhxDetail == null) {
            jyhxDetail = new CorrectionEstimatePortrayalDetail();
        }
        if (jyhxScore >= 50) {
            jyhxmsOne.append("<h2>低风险</h2>");
            jyhxDetail.setLevel("低风险");
        } else if (jyhxScore >= 40 && jyhxScore < 50) {
            jyhxmsOne.append("<h2>较低风险</h2>");
            jyhxDetail.setLevel("较低风险");
        } else if (jyhxScore >= 30 && jyhxScore < 40) {
            jyhxmsOne.append("<h2>中风险</h2>");
            jyhxDetail.setLevel("中风险");
        } else if (jyhxScore >= 20 && jyhxScore < 30) {
            jyhxmsOne.append("<h2>较高风险</h2>");
            jyhxDetail.setLevel("较高风险");
        } else if (jyhxScore >= 0 && jyhxScore < 20) {
            jyhxmsOne.append("<h2>高风险</h2>");
            jyhxDetail.setLevel("高风险");
        }
        int qzzfJyhx = jyztqzf + gzbdqzf + jylxqzf + zwlxqzf;//权重总分
        jyhxDetail.setType(3);
        jyhxDetail.setPortrayalId(portrayal.getId());
        jyhxDetail.setHxms(JSON.toJSONString(Lists.newArrayList(jyhxmsOne.toString(), jyhxmsTwo.toString(), jyhxmsThree.toString())));
        jyhxDetail.setXlhxGjfx("该矫正对象入矫以来，就业画像从较低风险变为高风险，趋势恶化，建议重点关注。");
        jyhxDetail.setJy(JSON.toJSONString(Lists.newArrayList("1、心理专家介入。", "2、进行个别谈话了解近期矫正生活。")));
        jyhxDetail.setKfmx(JSON.toJSONString(hxmxdtos));
        jyhxDetail.setZf(String.valueOf(qzzfJyhx));
        jyhxDetail.setDf(String.valueOf(jyhxScore));
        jyhxDetail.setKf(String.valueOf(qzzfJyhx - jyhxScore));
        correctionEstimatePortrayalDetailService.saveOrUpdate(jyhxDetail);
//--------------------------------------------------------------------------------------------------------------------------
        CorrectionEstimatePortrayalDetail jthxDetail = MapUtil.get(detailMap, 4, CorrectionEstimatePortrayalDetail.class);
        //家庭画像
        StringBuilder jthxmsOne = new StringBuilder("1、该矫正对象家庭画像风险为");
        StringBuilder jthxmsTwo = new StringBuilder("2、该矫正对象");
        StringBuilder jthxmsThree = new StringBuilder("3、该矫正对象");
        StringBuilder jthxmsFour = new StringBuilder("4、该矫正对象");
        int hyzkqzf = MapUtil.getInt(scoringModelDetailMap, "婚姻状况", defaultIndexPoint);
        int jtgxqzf = MapUtil.getInt(scoringModelDetailMap, "家庭关系", defaultIndexPoint);
        int jtmdqzf = MapUtil.getInt(scoringModelDetailMap, "家庭矛盾", defaultIndexPoint);
        int jbzgqzf = MapUtil.getInt(scoringModelDetailMap, "家人是否有重大疾病需要照顾", defaultIndexPoint);
        int jtbgqzf = MapUtil.getInt(scoringModelDetailMap, "家庭变故", defaultIndexPoint);
        int sfdbqzf = MapUtil.getInt(scoringModelDetailMap, "是否低保", defaultIndexPoint);
        int sftkqzf = MapUtil.getInt(scoringModelDetailMap, "是否特困", defaultIndexPoint);
        int jtsrqzf = MapUtil.getInt(scoringModelDetailMap, "家庭收入", defaultIndexPoint);
        int jtfzqzf = MapUtil.getInt(scoringModelDetailMap, "家庭负债", defaultIndexPoint);
        int jthxqzzf = hyzkqzf + jtgxqzf + jtmdqzf + jbzgqzf + jtbgqzf + sfdbqzf + sftkqzf + jtsrqzf + jtfzqzf;//家庭画像权重总分
        int jthxScore;//家庭画像总得分
        int hyzkdf = 0;
        int jtgxdf = 0;
        int jtmddf = 0;
        int jbzgdf = 0;
        int jtbgdf = 0;
        int sfdbdf = 0;
        int sftkdf = 0;
        int jtsrdf = 0;
        int jtfzdf = 0;
        hxmxdtos = new ArrayList<>();
        hxKfmxDTO = new PortrayalKfmxDTO();
        jthxmsTwo.append("<h2>").append(portrayal.getHyzk()).append("</h2>");
        hxKfmxDTO.setZbx("婚姻状况");
        hxKfmxDTO.setQzf(hyzkqzf);
        hxKfmxDTO.setZbz(portrayal.getHyzk());
        if (portrayal.getHyzk().equals("已婚")) {
            //已婚或25周岁以下未婚：不扣分
            hyzkdf = hyzkqzf;
        } else if (portrayal.getHyzk().equals("未婚")) {
            if (age <= 25) {
                hyzkdf = hyzkqzf;
            } else {
                hxKfmxDTO.setKf(20);
                hyzkdf = hyzkdf - 20;
            }
            //丧偶、离异、大龄未婚（25周岁以上）：扣20
        } else {
            hyzkdf = hyzkdf - 20;
        }
        hxmxdtos.add(hxKfmxDTO);
        //家庭关系
        // "好、较好：不扣分
        //一般：扣3
        //较差、差：扣5"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("家庭关系");
        hxKfmxDTO.setQzf(jtgxqzf);
        jthxmsThree.append("<h2>");
        if (portrayal.getPositionType() == 1) {
            hxKfmxDTO.setZbz("好");
            jthxmsThree.append("家庭关系好、");
        } else if (portrayal.getPositionType() == 2) {
            hxKfmxDTO.setZbz("较好");
            jthxmsThree.append("家庭关系较好、");
        } else if (portrayal.getFamilyTies() == 3) {
            hxKfmxDTO.setZbz("一般");
            jthxmsThree.append("家庭关系一般、");
            jtgxdf = jtgxqzf - 3;
            hxKfmxDTO.setKf(3);
        } else if (portrayal.getFamilyTies() == 4) {
            hxKfmxDTO.setZbz("较差");
            jthxmsThree.append("家庭关系较差、");
            jtgxdf = jtgxqzf - 5;
            hxKfmxDTO.setKf(5);
        } else if (portrayal.getFamilyTies() == 5) {
            hxKfmxDTO.setZbz("差");
            jthxmsThree.append("家庭关系差、");
            jtgxdf = jtgxqzf - 5;
        }
        jthxmsThree.append("最近与家里人");
        hxmxdtos.add(hxKfmxDTO);
        //家庭矛盾
        //"最近是否和家里人发生过比较大的矛盾
        //有：扣5
        //无：不扣分"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("家庭矛盾");
        hxKfmxDTO.setQzf(jtmdqzf);
        hxKfmxDTO.setZbz("无");
        if (portrayal.getFamilyConflict() == 1) {
            jtmddf = jtmdqzf - 5;
            hxKfmxDTO.setZbz("有");
            hxKfmxDTO.setKf(5);
            jthxmsThree.append("发生过矛盾");
        } else {
            jthxmsThree.append("没有发生过矛盾");
        }
        jthxmsThree.append("</h2>");
        hxmxdtos.add(hxKfmxDTO);
        //家庭变故
        //"是：扣5
        //否：不扣分"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("家庭变故");
        hxKfmxDTO.setQzf(jtbgqzf);
        hxKfmxDTO.setZbz("无");
        if (portrayal.getFamilyCalamity() == 1) {
            jtbgdf = jtbgqzf - 5;
            hxKfmxDTO.setZbz("有");
            hxKfmxDTO.setKf(5);
        }
        hxmxdtos.add(hxKfmxDTO);
        //家人是否有重大疾病需要照顾
        //"家人是否有重大疾病需要照顾
        //是：扣5
        //否：不扣分"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("家庭健康");
        hxKfmxDTO.setQzf(jtbgqzf);
        hxKfmxDTO.setZbz("家人无重大疾病");
        if (portrayal.getFamilyIllness() == 1) {
            jbzgdf = jbzgqzf - 5;
            hxKfmxDTO.setZbz("家人重有大疾病");
            hxKfmxDTO.setKf(5);
        }
        hxmxdtos.add(hxKfmxDTO);
        //是否特困
        //"是：扣20，以下指标无需计算
        //否：不扣分"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("经济状况");
        hxKfmxDTO.setQzf(sfdbqzf + sftkqzf + jtsrqzf + jtfzqzf);
        StringBuilder jjzkTempMs = new StringBuilder();
        if (portrayal.getPoverty() == 1) {
            jjzkTempMs.append("特困");
            sftkdf = sftkqzf - 20;
        }
        //是否低保
        //"是：扣20，以下指标无需计算
        //否：不扣分"
        if (portrayal.getInsured() == 1) {
            jjzkTempMs.append("  低保");
            sfdbdf = sfdbqzf - 20;
        }
        //家庭年收入
        //"无收入：扣20分，以下指标无需计算
        //8万以下：扣15分
        //8-15万：扣10分
        //15-20万：扣5分
        //20万以上：不扣分"
        switch (portrayal.getFamilyIncome()) {
            case 1:
                jjzkTempMs.append("  无家庭收入");
                jtsrdf = jtsrqzf - 20;
                break;
            case 2:
                jjzkTempMs.append("  家庭收入8万以下");
                jtsrdf = jtsrqzf - 15;
                break;
            case 3:
                jjzkTempMs.append("  家庭收入8-15万");
                jtsrdf = jtsrqzf - 10;
                break;
            case 4:
                jjzkTempMs.append("  家庭收入15-20万");
                jtsrdf = jtsrqzf - 5;
                break;
            case 5:
                jjzkTempMs.append("  家庭收入20万以上");
                break;
        }
        //家庭负债
        //"无：不扣分
        //有：扣10"
        if (portrayal.getFamilyLiabilities() == 1) {
            jjzkTempMs.append("  有家庭负债");
            jtfzdf = jtfzqzf - 10;
        }
        jthxmsFour.append(jjzkTempMs);
        hxKfmxDTO.setZbz(jjzkTempMs.toString());
        hxKfmxDTO.setKf(sfdbqzf + sftkqzf + jtsrqzf + jtfzqzf - sfdbdf - sftkdf - jtsrdf - jtfzdf);
        hxmxdtos.add(hxKfmxDTO);
        jthxScore = hyzkdf + jtgxdf + jtmddf + jbzgdf + jtbgdf + sfdbdf + sftkdf + jtsrdf + jtfzdf;
        portrayal.setJthxScore(new BigDecimal(jthxScore));
        if (jthxDetail == null) {
            jthxDetail = new CorrectionEstimatePortrayalDetail();
        }
        if (jthxScore >= 50) {
            jthxmsOne.append("<h2>低风险</h2>");
            jthxDetail.setLevel("低风险");
        } else if (jthxScore >= 40 && jthxScore < 50) {
            jthxmsOne.append("<h2>较低风险</h2>");
            jthxDetail.setLevel("较低风险");
        } else if (jthxScore >= 30 && jthxScore < 40) {
            jthxmsOne.append("<h2>中风险</h2>");
            jthxDetail.setLevel("中风险");
        } else if (jthxScore >= 20 && jthxScore < 30) {
            jthxmsOne.append("<h2>较高风险</h2>");
            jthxDetail.setLevel("较高风险");
        } else if (jthxScore >= 0 && jthxScore < 20) {
            jthxmsOne.append("<h2>高风险</h2>");
            jthxDetail.setLevel("高风险");
        }
        jthxDetail.setType(4);
        jthxDetail.setPortrayalId(portrayal.getId());
        jthxDetail.setHxms(JSON.toJSONString(Lists.newArrayList(jthxmsOne.toString(), jthxmsTwo.toString(), jthxmsThree.toString(), jthxmsFour.toString())));
        jthxDetail.setXlhxGjfx("该矫正对象入矫以来，家庭画像从较低风险变为高风险，趋势恶化，建议重点关注。");
        jthxDetail.setJy(JSON.toJSONString(Lists.newArrayList("1、心理专家介入。", "2、进行个别谈话了解近期矫正生活。")));
        jthxDetail.setKfmx(JSON.toJSONString(hxmxdtos));
        jthxDetail.setZf(String.valueOf(jthxqzzf));
        jthxDetail.setDf(String.valueOf(jthxScore));
        jthxDetail.setKf(String.valueOf(jthxqzzf - jthxScore));
        correctionEstimatePortrayalDetailService.saveOrUpdate(jthxDetail);
//--------------------------------------------------------------------------------------------------------------------------
        //个人基本画像
        CorrectionEstimatePortrayalDetail jbhxDetail = MapUtil.get(detailMap, 5, CorrectionEstimatePortrayalDetail.class);
        StringBuilder jbhxmsOne = new StringBuilder("1、该矫正对象个人基本画像风险为");
        StringBuilder jbhxmsTwo = new StringBuilder("2、该矫正对象");
        StringBuilder jbhxmsThree = new StringBuilder("3、该矫正对象<h2>");
        StringBuilder jbhxmsFour = new StringBuilder("4、该矫正对象<h2>近期与人");
        int grsrqzf = MapUtil.getInt(scoringModelDetailMap, "个人收入", defaultIndexPoint);
        int grfzqzf = MapUtil.getInt(scoringModelDetailMap, "个人负债", defaultIndexPoint);
        int sfcjqzf = MapUtil.getInt(scoringModelDetailMap, "是否残疾", defaultIndexPoint);
        int grjkqzf = MapUtil.getInt(scoringModelDetailMap, "个人健康", defaultIndexPoint);
        int mzqkqzf = MapUtil.getInt(scoringModelDetailMap, "门诊情况", defaultIndexPoint);
        int zyqkqzf = MapUtil.getInt(scoringModelDetailMap, "住院情况", defaultIndexPoint);
        int ymjzqzf = MapUtil.getInt(scoringModelDetailMap, "是否完成新冠疫苗接种", defaultIndexPoint);
        int jzqkqzf = MapUtil.getInt(scoringModelDetailMap, "居住情况", defaultIndexPoint);
        int ymdqzf = MapUtil.getInt(scoringModelDetailMap, "近期是否与人有矛盾", defaultIndexPoint);
        int jbhxqzzf = grsrqzf + grfzqzf + sfcjqzf + grjkqzf + mzqkqzf + ymjzqzf + zyqkqzf + jzqkqzf + ymdqzf; //个人基本画像权重总分
        int jbhxScore = 0;//个人基本画像得分
        int grsrdf = 0;
        int grfzdf = 0;
        int sfcjdf = 0;
        int grjkdf = 0;
        int mzqkdf = 0;
        int ymjzdf = 0;
        int zyqkdf = 0;
        int jzqkdf = 0;
        int ymddf = 0;
        hxmxdtos = new ArrayList<>();
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("经济状况");
        hxKfmxDTO.setQzf(grsrqzf + grfzqzf);
        StringBuilder grjjzkTemp = new StringBuilder();
        //个人收入
        //"未成年无收入：不扣分
        //已成年无收入：扣10分
        //5000以下：扣8
        //5000-8000：扣5
        //8000以上：不扣分 "

        //未成年无收入：不扣分
        if (portrayal.getPersonIncome() == 1) {
            if (age < 18) {
                grsrdf = grsrqzf;
            } else {
                //已成年无收入：扣10分
                grsrdf = grsrqzf - 10;
            }
            grjjzkTemp.append("无收入");
        } else if (portrayal.getPersonIncome() == 2) {
            //5000以下：扣8
            grsrdf = grsrqzf - 8;
            grjjzkTemp.append("5000以下");
        } else if (portrayal.getPersonIncome() == 3) {
            //5000-8000：扣5
            grsrdf = grsrqzf - 8;
            grjjzkTemp.append("5000-8000");
        } else if (portrayal.getPersonIncome() == 4) {
            //8000以上：不扣分
            grjjzkTemp.append("8000以上");
        }
        //个人负债
        //"无：不扣分
        //有：扣10"
        if (portrayal.getPersonLiabilities() == 1) {
            //有：扣10"
            grfzdf = grfzqzf - 10;
            grjjzkTemp.append("  有个人负债");
            jbhxmsTwo.append("<h2>有个人负债</h2>");
        } else if (portrayal.getPersonLiabilities() == 0) {
            ////"无：不扣分
            grfzdf = grfzqzf;
            grjjzkTemp.append("  无个人负债");
            jbhxmsTwo.append("<h2>无个人负债</h2>");
        }
        hxKfmxDTO.setZbz(grjjzkTemp.toString());
        hxKfmxDTO.setKf(grsrqzf + grfzqzf - grsrdf - grfzdf);
        hxmxdtos.add(hxKfmxDTO);

        //个人健康
        //"好、良好：不扣分
        //一般、较差： 扣5
        //差：扣10"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("健康状况");
        hxKfmxDTO.setQzf(sfcjqzf + grjkqzf + mzqkqzf + zyqkqzf);
        StringBuilder grjkTemp = new StringBuilder("个人健康");
        switch (portrayal.getHealth()) {
            case 1:
                grjkTemp.append("好");
                break;
            case 2:
                grjkTemp.append("良好");
                break;
            case 3:
                grjkdf = grjkqzf - 5;
                grjkTemp.append("一般");
                break;
            case 4:
                grjkdf = grjkqzf - 5;
                grjkTemp.append("较差");
                break;
            case 5:
                grjkdf = grjkqzf - 10;
                grjkTemp.append("差");
                break;
        }
        //门诊情况
        //"无：不扣分
        //有：扣5"
        if (portrayal.getOutpatientCondition() == 1) {
            mzqkdf = mzqkqzf - 5;
            grjkTemp.append("  近期有门诊记录");
        } else if (portrayal.getOutpatientCondition() == 0) {
            //"无：不扣分
        }
        //住院情况
        //"无：不扣分
        //有：扣10"
        if (portrayal.getHospitalization() == 1) {
            zyqkdf = zyqkqzf - 10;
            grjkTemp.append("  近期有住院");
        } else if (portrayal.getHospitalization() == 0) {
            //"无：不扣分
        }
        //是否残疾
        //"否：不扣分
        //残疾三、四级（生活基本能自理）：扣10
        //残疾一、二级（生活不能自理）：扣15"
        if (portrayal.getDisability() == 0) {
            //"否：不扣分
        } else if (portrayal.getDisability() == 1) {
            //残疾三、四级（生活基本能自理）：扣10
            grjkTemp.append("  残疾三、四级（生活基本能自理");
            sfcjdf = sfcjqzf - 10;
        } else if (portrayal.getDisability() == 2) {
            //残疾一、二级（生活不能自理）：扣15"
            grjkTemp.append("  残疾一、二级（生活不能自理）");
            sfcjdf = sfcjqzf - 15;
        }
        hxKfmxDTO.setZbz(grjkTemp.toString());
        hxKfmxDTO.setKf(sfcjqzf + grjkqzf + mzqkqzf + zyqkqzf - sfcjdf - grjkdf - mzqkdf - zyqkdf);
        jbhxmsThree.append(grjkTemp).append("</h2>");
        hxmxdtos.add(hxKfmxDTO);
        //疫苗接种
        //"是否完成新冠疫苗接种：
        //否：扣5
        //是：不扣分"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("新冠疫苗接种");
        hxKfmxDTO.setQzf(ymjzqzf);
        if (portrayal.getXgybjz() == 1) {
            hxKfmxDTO.setZbz("已接种");
        } else if (portrayal.getXgybjz() == 0) {
            ymjzdf = ymjzqzf - 5;
            hxKfmxDTO.setZbz("未接种");
            hxKfmxDTO.setKf(5);
        }
        hxmxdtos.add(hxKfmxDTO);
        //居住情况
        //"家人、朋友、男/女朋友同住：不扣分
        //独居：扣10"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("居住情况");
        hxKfmxDTO.setQzf(jzqkqzf);
        if (portrayal.getResidentialCondition() == 2) {
            jzqkdf = jzqkqzf - 10;
            hxKfmxDTO.setZbz("独居");
            hxKfmxDTO.setKf(10);
        } else if (portrayal.getResidentialCondition() == 1) {
            //"无：不扣分
            hxKfmxDTO.setZbz("非独居");
        }
        hxmxdtos.add(hxKfmxDTO);
        //人际关系
        //"近期是否与人有矛盾：
        //是：扣10
        //否：不扣分"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("人际关系");
        hxKfmxDTO.setQzf(ymdqzf);
        if (portrayal.getContradiction() == 1) {
            ymddf = ymdqzf - 10;
            hxKfmxDTO.setZbz("近期与人有矛盾");
            jbhxmsFour.append("有矛盾</h2>");
        } else if (portrayal.getContradiction() == 0) {
            //"无：不扣分
            jbhxmsFour.append("无矛盾</h2>");
            hxKfmxDTO.setZbz("近期与人无矛盾");
        }

        jbhxScore = grsrdf + grfzdf + sfcjdf + grjkdf + mzqkdf + ymjzdf + zyqkdf + jzqkdf + ymddf;
        portrayal.setJbhxScore(new BigDecimal(jbhxScore));
        if (jbhxDetail == null) {
            jbhxDetail = new CorrectionEstimatePortrayalDetail();
        }
        if (jbhxScore >= 50) {
            jbhxmsOne.append("<h2>低风险</h2>");
            jbhxDetail.setLevel("低风险");
        } else if (jbhxScore >= 40 && jbhxScore < 50) {
            jbhxmsOne.append("<h2>较低风险</h2>");
            jbhxDetail.setLevel("较低风险");
        } else if (jbhxScore >= 30 && jbhxScore < 40) {
            jbhxmsOne.append("<h2>中风险</h2>");
            jbhxDetail.setLevel("中风险");
        } else if (jbhxScore >= 20 && jbhxScore < 30) {
            jbhxmsOne.append("<h2>较高风险</h2>");
            jbhxDetail.setLevel("较高风险");
        } else if (jbhxScore >= 0 && jbhxScore < 20) {
            jbhxmsOne.append("<h2>高风险</h2>");
            jbhxDetail.setLevel("高风险");
        }
        jbhxDetail.setType(5);
        jbhxDetail.setPortrayalId(portrayal.getId());
        jbhxDetail.setHxms(JSON.toJSONString(Lists.newArrayList(jbhxmsOne.toString(), jbhxmsTwo.toString(), jbhxmsThree.toString(), jbhxmsFour.toString())));
        jbhxDetail.setXlhxGjfx("该矫正对象入矫以来，个人基本画像从较低风险变为高风险，趋势恶化，建议重点关注。");
        jbhxDetail.setJy(JSON.toJSONString(Lists.newArrayList("1、心理专家介入。", "2、进行个别谈话了解近期矫正生活。")));
        jbhxDetail.setKfmx(JSON.toJSONString(hxmxdtos));
        jbhxDetail.setZf(String.valueOf(jbhxqzzf));
        jbhxDetail.setDf(String.valueOf(jbhxScore));
        jbhxDetail.setKf(String.valueOf(jbhxqzzf - jbhxScore));
        correctionEstimatePortrayalDetailService.saveOrUpdate(jbhxDetail);
//--------------------------------------------------------------------------------------------------------------------------
        //知法画像
        //"老年（60岁以上）、未成年：不扣分
        //中年（45-60岁）、壮年（35-45岁）、青年（18-35岁）：扣5"
        CorrectionEstimatePortrayalDetail zfhxPortrayal = MapUtil.get(detailMap, 2, CorrectionEstimatePortrayalDetail.class);
        StringBuilder zfhxmsOne = new StringBuilder("1、该矫正对象知法画像风险为");
        StringBuilder zfhxmsTwo = new StringBuilder("2、该矫正对象");
        StringBuilder zfhxmsThree = new StringBuilder("3、该矫正对象");
        int nkqzf = MapUtil.getInt(scoringModelDetailMap, "年龄", defaultIndexPoint);
        int xlqzf = MapUtil.getInt(scoringModelDetailMap, "学历", defaultIndexPoint);
        int qkqzf = MapUtil.getInt(scoringModelDetailMap, "是否有前科", defaultIndexPoint);
        int gfqzf = MapUtil.getInt(scoringModelDetailMap, "是否累犯惯犯", defaultIndexPoint);
        int jgcsqzf = MapUtil.getInt(scoringModelDetailMap, "对社区矫正监管措施的熟悉程度", defaultIndexPoint);
        int jswtqzf = MapUtil.getInt(scoringModelDetailMap, "社区矫正监管措施问题是否通过", defaultIndexPoint);
        int zfhxqzzf = nkqzf + xlqzf + qkqzf + gfqzf + jgcsqzf + jswtqzf;//知法画像权重总分
        int zfhxScore;//知法画像总等分
        int nkqdf = 0;
        int xlqdf = 0;
        int qkqdf = 0;
        int gfqdf = 0;
        int jgcsqdf = 0;
        int jswtqdf = 0;
        //年龄
        //"老年（60岁以上）、未成年：不扣分
        //中年（45-60岁）、壮年（35-45岁）、青年（18-35岁）：扣5"
        hxmxdtos = new ArrayList<>();
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("年龄");
        hxKfmxDTO.setQzf(nkqzf);
        if (age < 18) {
            //未成年
            portrayal.setAgeCondition("未成年");
            hxKfmxDTO.setZbz("未成年(18以下)");
            portrayal.setAgeCondition("未成年");
        } else if (age >= 18 && age < 35) {
            //青年（18-35岁）
            nkqdf = nkqzf - 5;
            portrayal.setAgeCondition("青年");
            hxKfmxDTO.setZbz("青年（18-35岁）");
            hxKfmxDTO.setKf(5);
        } else if (age >= 35 && age < 45) {
            //壮年（35-45岁）
            nkqdf = nkqzf - 5;
            portrayal.setAgeCondition("壮年");
            hxKfmxDTO.setZbz("壮年（35-45岁）");
            hxKfmxDTO.setKf(5);
        } else if (age >= 45 && age < 60) {
            //中年（45-60岁）.
            nkqdf = nkqzf - 5;
            portrayal.setAgeCondition("中年");
            hxKfmxDTO.setZbz("中年（45-60岁）");
            hxKfmxDTO.setKf(5);
        } else if (age >= 60) {
            //老年（60岁以上）
            portrayal.setAgeCondition("老年");
            hxKfmxDTO.setZbz("老年（60岁以上）");
        }
        hxmxdtos.add(hxKfmxDTO);
        //学历
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("学历");
        hxKfmxDTO.setQzf(xlqdf);
        hxKfmxDTO.setZbz(portrayal.getEducation());
        switch (portrayal.getEducation()) {
            case "中专和中技":
                hxKfmxDTO.setKf(3);
                xlqdf = xlqzf - 3;
                break;
            case "大专":
            case "本科":
            case "硕士":
            case "博士":
                xlqdf = xlqzf - 5;
                hxKfmxDTO.setKf(5);
                break;
        }
        hxmxdtos.add(hxKfmxDTO);
        //是否有前科
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("犯罪前科");
        hxKfmxDTO.setQzf(qkqzf);
        if (portrayal.getCriminality() == 1) {
            qkqdf = qkqzf - 10;
            hxKfmxDTO.setZbz("是");
            hxKfmxDTO.setKf(10);
            zfhxmsTwo.append("<h2>犯罪前科：有</h2>");
        } else if (portrayal.getCriminality() == 0) {
            zfhxmsTwo.append("<h2>犯罪前科：无</h2>");
            hxKfmxDTO.setZbz("否");
        }
        hxmxdtos.add(hxKfmxDTO);
        //是否累犯惯犯
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("累犯惯犯");
        hxKfmxDTO.setQzf(gfqzf);
        if (portrayal.getRecidivist() == 1) {
            gfqdf = gfqzf - 10;
            hxKfmxDTO.setZbz("是");
            hxKfmxDTO.setKf(10);
        } else if (portrayal.getRecidivist() == 0) {
            hxKfmxDTO.setZbz("否");
        }
        hxmxdtos.add(hxKfmxDTO);
        //对社区矫正监管措施的熟悉程度
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("法律意识");
        hxKfmxDTO.setQzf(jgcsqzf + jswtqzf);
        switch (portrayal.getJgcsFamiliarity()) {
            case 1:
                zfhxmsThree.append("<h2>对社区矫正监管措施的熟悉程度：熟悉</h2>");
                hxKfmxDTO.setZbz("1、监管措施的熟悉程度：熟悉");
                break;
            case 2:
                jgcsqdf = jgcsqzf - 10;
                zfhxmsThree.append("<h2>对社区矫正监管措施的熟悉程度：知道一点</h2>");
                hxKfmxDTO.setZbz("1、监管措施的熟悉程度：知道一点");
                break;
            case 3:
            case 0:
                jgcsqdf = jgcsqzf - 20;
                zfhxmsThree.append("<h2>对社区矫正监管措施的熟悉程度：不知道</h2>");
                hxKfmxDTO.setZbz("1、监管措施的熟悉程度：知道一点");
                break;
        }
        //社区矫正监管措施问题是否通过
        if (portrayal.getJgcsPass() == 1) {
            jswtqdf = jswtqzf - 20;
            hxKfmxDTO.setZbz(hxKfmxDTO.getZbz() + "   2、关于监管措施的问卷调查：通过");
        } else if (portrayal.getJgcsPass() == 0) {
            hxKfmxDTO.setZbz(hxKfmxDTO.getZbz() + "   2、关于监管措施的问卷调查：不通过");
        }
        hxKfmxDTO.setKf(jgcsqdf + jswtqzf - jgcsqdf - jswtqdf);
        hxmxdtos.add(hxKfmxDTO);
        zfhxScore = nkqdf + xlqdf + qkqdf + gfqdf + jgcsqdf + jswtqdf;
        portrayal.setZfhxScore(new BigDecimal(zfhxScore));
        if (zfhxPortrayal == null) {
            zfhxPortrayal = new CorrectionEstimatePortrayalDetail();
        }
        if (zfhxScore >= 40) {
            zfhxmsOne.append("<h2>低风险</h2>");
            zfhxPortrayal.setLevel("低风险");
        } else if (zfhxScore >= 35 && zfhxScore < 40) {
            zfhxmsOne.append("<h2>较低风险</h2>");
            zfhxPortrayal.setLevel("较低风险");
        } else if (zfhxScore >= 30 && zfhxScore < 35) {
            zfhxmsOne.append("<h2>中风险</h2>");
            zfhxPortrayal.setLevel("中风险");
        } else if (zfhxScore >= 20 && zfhxScore < 30) {
            zfhxmsOne.append("<h2>较高风险</h2>");
            zfhxPortrayal.setLevel("较高风险");
        } else if (zfhxScore >= 0 && zfhxScore < 20) {
            zfhxmsOne.append("<h2>高风险</h2>");
            zfhxPortrayal.setLevel("高风险");
        }

        zfhxPortrayal.setType(2);
        zfhxPortrayal.setPortrayalId(portrayal.getId());
        zfhxPortrayal.setHxms(JSON.toJSONString(Lists.newArrayList(zfhxmsOne.toString(), zfhxmsTwo.toString(), zfhxmsThree.toString())));
        zfhxPortrayal.setXlhxGjfx("该矫正对象入矫以来，知法画像从较低风险变为高风险，趋势恶化，建议重点关注。");
        zfhxPortrayal.setJy(JSON.toJSONString(Lists.newArrayList("1、进行社区矫正监管措施教育。", "   2、进行普法教育。")));
        zfhxPortrayal.setKfmx(JSON.toJSONString(hxmxdtos));
        zfhxPortrayal.setZf(String.valueOf(qzzf));
        zfhxPortrayal.setDf(String.valueOf(zfhxScore));
        zfhxPortrayal.setKf(String.valueOf(qzzf - zfhxScore));
        correctionEstimatePortrayalDetailService.saveOrUpdate(zfhxPortrayal);
        portrayal.setScoreTotal(new BigDecimal(rcjgqzzf + cfqzzf + xwdtqzf + xlhxqzf + jyhxqzzf + jbhxqzzf + zfhxqzzf + jthxqzzf));
        portrayal.setScoreEstimate(new BigDecimal(rcjgScore + cfScore + xwdtScore + xlhxScore + zfhxScore + jyhxScore + jthxScore + jbhxScore));
        return portrayal;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getScoreEstimateNew(CorrectionEstimatePortrayal portrayal, List<ScoringModelDetailManagePortrayal> scoringModelDetailManagePortrayals) {
        int defaultIndexPoint = 50;
        int age = portrayal.getAge();
        Map<String, Integer> scoringModelDetailMap = scoringModelDetailManagePortrayals.stream().collect(Collectors.toMap(item -> item.getDetailName(), item -> item.getIndexPoint()));
        CorrectionEstimatePortrayalParticulars portrayalParticulars = correctionEstimatePortrayalParticularsService.getByPortrayalId(portrayal.getId());
        if (portrayalParticulars == null) {
            portrayalParticulars = new CorrectionEstimatePortrayalParticulars();
        }
        //存储评估报告信息
        List<ReportDetailRiskDTO> reportDetailRiskDTOS = new ArrayList<>();
        ReportDetailRiskDTO riskDTO;
        List<String> conditions;
        int rjpgzf = MapUtil.getInt(scoringModelDetailMap, "入矫评估分", defaultIndexPoint);
        int xyzf = MapUtil.getInt(scoringModelDetailMap, "信用画像", defaultIndexPoint);
        int jfzf = MapUtil.getInt(scoringModelDetailMap, "加分项", defaultIndexPoint);
//--------------------------------------------------------------------------------------------------------------------------
        //日常监管得分计算规则：
        //1、降到严管扣50分
        //2、出现一次违规，扣20分，扣完为止
        int rcjgzf = MapUtil.getInt(scoringModelDetailMap, "日常监管", defaultIndexPoint);
        //int ygqzf = MapUtil.getInt(scoringModelDetailMap, "是否降到严管", defaultIndexPoint);
        int wgqzf = MapUtil.getInt(scoringModelDetailMap, "信息化监管违规次数", defaultIndexPoint);
        conditions = new ArrayList<>();
        // int rcjgqzzf = ygqzf + wgqzf;//监管权重总分
        int rcjgScore = 0;//日常监管得分
        int rcjgkf = 0;
        if (portrayal.getLevelDown() == 1) {
            //是否降到严管
            rcjgkf = rcjgkf + 50;
            conditions.add("等级评估降为严管");
        } else {

        }
        if (portrayal.getYqViolate() > 0) {
            conditions.add("信息化违规次数" + portrayal.getYqViolate() + "次");
        }
        rcjgkf = rcjgkf + portrayal.getYqViolate() * 20 >= wgqzf ? wgqzf : portrayal.getYqViolate();
        rcjgScore = rcjgzf - rcjgkf >= 0 ? rcjgzf - rcjgkf : 0;
        portrayal.setRcjgScore(new BigDecimal(rcjgScore));
        riskDTO = new ReportDetailRiskDTO("日常监管", new BigDecimal(rcjgkf), conditions);
        if (conditions.size() > 0) {
            reportDetailRiskDTOS.add(riskDTO);
        }
//--------------------------------------------------------------------------------------------------------------------------
        //处罚得分计算规则：
        //
        //受到一次训诫扣25分、受到一次警告扣75分、
        // 受到一次治安处罚扣150分、提请撤缓、提请撤销假释，扣250分，
        // 出现过一次逮捕扣200分，行政处罚，扣100，提请收监执行，扣300
        int cfzf = MapUtil.getInt(scoringModelDetailMap, "处罚", defaultIndexPoint);
        // int xjqzf = MapUtil.getInt(scoringModelDetailMap, "训诫次数", defaultIndexPoint);
        //int zgqzf = MapUtil.getInt(scoringModelDetailMap, "警告次数", defaultIndexPoint);
        // int zaqzf = MapUtil.getInt(scoringModelDetailMap, "治安处罚次数", defaultIndexPoint);
        // int xzqzf = MapUtil.getInt(scoringModelDetailMap, "行政处罚", defaultIndexPoint);
        //int txcxqzf = MapUtil.getInt(scoringModelDetailMap, "提请撤销假释", defaultIndexPoint);
        //int tqchqzf = MapUtil.getInt(scoringModelDetailMap, "提请撤缓", defaultIndexPoint);
        //int tqdbqzf = MapUtil.getInt(scoringModelDetailMap, "提请逮捕", defaultIndexPoint);
        //int tqsjqzf = MapUtil.getInt(scoringModelDetailMap, "提请收监执行", defaultIndexPoint);
        conditions = new ArrayList<>();
        //int cfqzzf = xjqzf + zgqzf + zaqzf + xzqzf + txcxqzf + tqchqzf + tqdbqzf + tqsjqzf;//处罚权重总分
        int cfkf = 0;
        int cfScore;//处罚得分
        cfkf = portrayal.getAdvise() * 25;//训诫
        cfkf = cfkf + portrayal.getWarn() * 75;//警告
        cfkf = cfkf + portrayal.getPublicSecurity() * 150;//治安处罚
        cfkf = cfkf + portrayal.getCancelParole() * 250;//提请撤销假释
        cfkf = cfkf + portrayal.getCancelProbation() * 250;//提请撤缓
        cfkf = cfkf + portrayal.getAskArrest() * 200;//提请逮捕
        cfkf = cfkf + portrayal.getCommittedToPrison() * 300;//提请收监执行
        cfScore = cfzf - cfkf <= 0 ? 0 : cfzf - cfkf;
        portrayal.setCfScore(new BigDecimal(cfScore));
        if (portrayal.getAdvise() > 0) {
            conditions.add(new StringBuilder("训诫").append(portrayal.getAdvise()).append("次").toString());
        }
        if (portrayal.getWarn() > 0) {
            conditions.add(new StringBuilder("警告").append(portrayal.getWarn()).append("次").toString());
        }
        if (portrayal.getPublicSecurity() > 0) {
            conditions.add(new StringBuilder("治安处罚").append(portrayal.getPublicSecurity()).append("次").toString());
        }
        if (portrayal.getCancelParole() > 0) {
            conditions.add(new StringBuilder("提请撤销假释").append(portrayal.getCancelParole()).append("次").toString());
        }
        if (portrayal.getCancelProbation() > 0) {
            conditions.add(new StringBuilder("提请撤缓").append(portrayal.getCancelProbation()).append("次").toString());
        }
        if (portrayal.getAskArrest() > 0) {
            conditions.add(new StringBuilder("提请逮捕").append(portrayal.getAskArrest()).append("次").toString());
        }
        if (portrayal.getCommittedToPrison() > 0) {
            conditions.add(new StringBuilder("提请收监执行").append(portrayal.getCommittedToPrison()).append("次").toString());
        }
        riskDTO = new ReportDetailRiskDTO("处罚", new BigDecimal(cfkf), conditions);
        if (conditions.size() > 0) {
            reportDetailRiskDTOS.add(riskDTO);
        }
//--------------------------------------------------------------------------------------------------------------------------
        //行为动态
        //1.非流动人口换算规则：出现一次扣10分，出现2次，扣分翻倍，即20分，3次扣40分，依次类推，扣完为止。
        //2.流动人口换算规则：出现一次扣15分，出现2次，扣分翻倍，即30分，依次类推，扣完为止。
        int xwdtzf = MapUtil.getInt(scoringModelDetailMap, "行为动态", defaultIndexPoint);
        conditions = new ArrayList<>();
        int xwdtkf = 0;
        int xwdtScore = 0;//行为动态得分
        int isLdrk = portrayal.getIsLdrk();
        if (isLdrk == 0 && portrayal.getNightOut() > 0) {
            //非流动人口
            xwdtkf = (int) (10 * Math.pow(2, portrayal.getNightOut() - 1));
        } else if (isLdrk == 1 && portrayal.getNightOut() > 0) {
            //流动人口
            xwdtkf = (int) (15 * Math.pow(2, portrayal.getNightOut() - 1));
        }
        xwdtScore = xwdtzf - xwdtkf <= 0 ? 0 : xwdtzf - xwdtkf;
        portrayal.setXwdtScore(new BigDecimal(xwdtScore));
        if (portrayal.getNightOut() > 0) {
            conditions.add(new StringBuilder("夜不归宿").append(portrayal.getNightOut()).append("次").toString());
        }
        riskDTO = new ReportDetailRiskDTO("行为动态", new BigDecimal(xwdtScore), conditions);
        if (conditions.size() > 0) {
            reportDetailRiskDTOS.add(riskDTO);
        }
//--------------------------------------------------------------------------------------------------------------------------
//    心理画像
        //出现红码：扣70，以下指标无需计算
        //出现橙码或黄码：扣35
        StringBuilder xlhxmsOne = new StringBuilder("1、该矫正对象心理画像风险为");
        StringBuilder xlhxmsTwo = new StringBuilder("2、该矫正对象心晴码显示");
        StringBuilder xlhxmsThree = new StringBuilder("3、该矫正对象认罪服法态度");
        StringBuilder xlhxmsFour = new StringBuilder("4、该矫正对象在心情日记中出现");
        StringBuilder xlhxmsFive = new StringBuilder("5、该矫正对象在心情日记中出现");
        int xlhxzf = MapUtil.getInt(scoringModelDetailMap, "心理画像", defaultIndexPoint);
        int xqmqzf = MapUtil.getInt(scoringModelDetailMap, "心晴码", defaultIndexPoint);
        int jsbqzf = MapUtil.getInt(scoringModelDetailMap, "有精神病史或精神病遗传史", defaultIndexPoint);
        int fmxqqzf = MapUtil.getInt(scoringModelDetailMap, "负面心情", defaultIndexPoint);
        int pjkfqzf = MapUtil.getInt(scoringModelDetailMap, "认罪服法态度", defaultIndexPoint);
        int fmcqzf = MapUtil.getInt(scoringModelDetailMap, "负面关键词预警", defaultIndexPoint);
        conditions = new ArrayList<>();
        // int xlhxqzf = xqmqzf + jsbqzf + fmxqqzf + pjkfqzf + fmcqzf;//心理画像权重总分
        int xlhxScore;//心理画像得分
        int xlhxkf = 0;
        List<PortrayalKfmxDTO> hxmxdtos = new ArrayList<>();
        Set<String> hxjySet = new HashSet<>();
        PortrayalKfmxDTO hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("心晴码");
        hxKfmxDTO.setQzf(xqmqzf);
        switch (portrayal.getHeartCode()) {
            case 0:
                xlhxmsTwo.append("<h2>无码</h2>");
                hxKfmxDTO.setZbz("无码");
                break;
            case 1:
                xlhxkf = xlhxkf + 70;
                xlhxmsTwo.append("<h2>红码</h2>");
                hxKfmxDTO.setZbz("红码");
                hxKfmxDTO.setKf(70);
                break;
            case 2:
                xlhxkf = xlhxkf + 35;
                xlhxmsTwo.append("<h2>橙码</h2>");
                hxKfmxDTO.setZbz("橙码");
                hxKfmxDTO.setKf(35);
                break;
            case 3:
                xlhxkf = xlhxkf + 35;
                xlhxmsTwo.append("<h2>黄码</h2>");
                hxKfmxDTO.setZbz("黄码");
                hxKfmxDTO.setKf(35);
                break;
            case 4:
                xlhxmsTwo.append("<h2>蓝码</h2>");
                hxKfmxDTO.setZbz("蓝码");
                break;
            case 5:
                xlhxmsTwo.append("<h2>绿码</h2>");
                hxKfmxDTO.setZbz("绿码");
                break;
        }
        //心晴码有扣分改善建议
        if (hxKfmxDTO.getKf() > 0) {
            hxjySet.add("心理专家介入");
        }
        hxmxdtos.add(hxKfmxDTO);
        conditions.add(new StringBuilder("心晴码为").append(hxKfmxDTO.getZbz()).toString());
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("有无精神病史或精神病遗传史");
        hxKfmxDTO.setQzf(jsbqzf);
        //有精神病史或精神病遗传史
        if (portrayal.getIsPsychosis() == 1) {
            xlhxkf = xlhxkf + 10;
            hxKfmxDTO.setZbz("有");
            conditions.add("有精神病史或精神病遗传史");
            hxKfmxDTO.setKf(10);
        } else if (portrayal.getIsPsychosis() == 0) {
            hxKfmxDTO.setZbz("无");
            conditions.add("没有有精神病史或精神病遗传史");
        }
        //有精神病史或精神病遗传史有扣分改善建议
        if (hxKfmxDTO.getKf() > 0) {
            hxjySet.add("心理专家介入");
        }
        hxmxdtos.add(hxKfmxDTO);
        //负面心情
        //"交心日记中“想发火”1次，扣10分
        //交心日记中“想发火”、“不顺心”3次以上（含），扣20分
        //交心日记中“想发火”、“不顺心”5次以上（含），扣30分"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("负面心情");
        hxKfmxDTO.setQzf(fmxqqzf);
        hxKfmxDTO.setZbz("交心日记中出现负面心情" + portrayal.getNegativeMood() + "次");
        xlhxmsFive.append("<h2>负面心情").append(portrayal.getNegativeMood()).append("次</h2>");
        if (portrayal.getNegativeMood() == 0) {
            hxKfmxDTO.setZbz("交心日记中未出现负面心情");
        } else if (portrayal.getNegativeMood() >= 1 && portrayal.getNegativeMood() < 3) {
            xlhxkf = xlhxkf + 10;
            hxKfmxDTO.setKf(10);
        } else if (portrayal.getNegativeMood() >= 3 && portrayal.getNegativeMood() < 5) {
            xlhxkf = xlhxkf + 20;
            hxKfmxDTO.setKf(20);

        } else if (portrayal.getNegativeMood() >= 5) {
            xlhxkf = xlhxkf + 30;
            hxKfmxDTO.setKf(30);
        }
        //负面心情有扣分改善建议
        if (hxKfmxDTO.getKf() == 10 || hxKfmxDTO.getKf() == 20) {
            hxjySet.add("个别谈话");
        } else if (hxKfmxDTO.getKf() == 30) {
            hxjySet.add("心理专家介入");
        }
        conditions.add(hxKfmxDTO.getZbz());
        hxmxdtos.add(hxKfmxDTO);
        //对本次法院最终判决结果的看法
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("认罪服法态度");
        hxKfmxDTO.setQzf(pjkfqzf);
        switch (portrayal.getWritteWritten()) {
            case 1:
                xlhxmsThree.append("很好，认为本次法院最终判决结果<h2>很轻</h2>");
                hxKfmxDTO.setZbz("对本次法院最终判决结果的看法：很轻");
                break;
            case 2:
                xlhxmsThree.append("较好，认为本次法院最终判决结果<h2>较轻</h2>");
                hxKfmxDTO.setZbz("对本次法院最终判决结果的看法：较轻");
                break;
            case 3:
                xlhxmsThree.append("比较好，认为本次法院最终判决结果<h2>适当</h2>");
                hxKfmxDTO.setZbz("对本次法院最终判决结果的看法：适当");
                break;
            case 4:
                xlhxmsThree.append("比较差，认为本次法院最终判决结果<h2>比较重</h2>");
                hxKfmxDTO.setZbz("对本次法院最终判决结果的看法：比较重");
                hxKfmxDTO.setKf(5);
                xlhxkf = xlhxkf + 5;
                break;
            case 5:
                xlhxmsThree.append("很差，认为本次法院最终判决结果<h2>太重了</h2>");
                hxKfmxDTO.setZbz("对本次法院最终判决结果的看法：太重了");
                hxKfmxDTO.setKf(10);
                xlhxkf = xlhxkf + 10;
                break;
            default:
                xlhxmsThree.append("很差，认为本次法院最终判决结果<h2>未知</h2>");
                hxKfmxDTO.setZbz("对本次法院最终判决结果的看法：未知");
                hxKfmxDTO.setKf(0);
        }
        //对本次法院最终判决结果的看法有扣分改善建议
        if (hxKfmxDTO.getKf() > 0) {
            hxjySet.add("个别谈话，进行普法教育");
        }
        conditions.add(hxKfmxDTO.getZbz());
        hxmxdtos.add(hxKfmxDTO);
        xlhxmsFour.append("<h2>负面关键词预警").append(portrayal.getNegativeWords()).append("次</h2>");
        //负面关键词预警
        //"交心日记中出现负面关键词预警：扣15；
        //出现超过3次以上（含）：扣30"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("负面关键词预警");
        hxKfmxDTO.setQzf(fmcqzf);
        hxKfmxDTO.setZbz("交心日记中出现负面关键词预警" + portrayal.getNegativeWords() + "次");
        if (portrayal.getNegativeWords() == 0) {
            hxKfmxDTO.setZbz("交心日记中未出现负面关键词预警");
            hxKfmxDTO.setKf(0);

        } else if (portrayal.getNegativeWords() >= 1 && portrayal.getNegativeWords() <= 3) {
            xlhxkf = xlhxkf + 15;
            hxKfmxDTO.setKf(15);
        } else if (portrayal.getNegativeMood() > 3) {
            xlhxkf = xlhxkf + 30;
            hxKfmxDTO.setKf(30);
        }
        //负面关键词预警有扣分改善建议
        if (hxKfmxDTO.getKf() == 15) {
            hxjySet.add("个别谈话");
        } else if (hxKfmxDTO.getKf() == 30) {
            hxjySet.add("心理专家介入");
        }
        conditions.add(hxKfmxDTO.getZbz());
        hxmxdtos.add(hxKfmxDTO);
        xlhxScore = xlhxzf - xlhxkf <= 0 ? 0 : xlhxzf - xlhxkf;
        portrayal.setXlhxScore(new BigDecimal(xlhxScore));
        if (xlhxScore >= 60) {
            xlhxmsOne.append("<h2>低风险</h2>");
            portrayalParticulars.setXlhxLevel("低风险");
        } else if (xlhxScore >= 50 && xlhxScore < 60) {
            xlhxmsOne.append("<h2>较低风险</h2>");
            portrayalParticulars.setXlhxLevel("较低风险");
        } else if (xlhxScore >= 30 && xlhxScore < 50) {
            xlhxmsOne.append("<h2>中风险</h2>");
            portrayalParticulars.setXlhxLevel("中风险");
        } else if (xlhxScore >= 20 && xlhxScore < 30) {
            xlhxmsOne.append("<h2>较高风险</h2>");
            portrayalParticulars.setXlhxLevel("较高风险");
        } else if (xlhxScore >= 0 && xlhxScore < 20) {
            xlhxmsOne.append("<h2>高风险</h2>");
            portrayalParticulars.setXlhxLevel("高风险");
        }
        portrayalParticulars.setPortrayalId(portrayal.getId());
        portrayalParticulars.setXlhxms(JSON.toJSONString(Lists.newArrayList(xlhxmsOne.toString(), xlhxmsTwo.toString(), xlhxmsThree.toString(), xlhxmsFour.toString(), xlhxmsFive.toString())));
        portrayalParticulars.setXlhxgjfx("该矫正对象入矫以来，心理画像从较低风险变为高风险，趋势恶化，建议重点关注。");
        portrayalParticulars.setXlhxjy(JSON.toJSONString(hxjySet));
        portrayalParticulars.setXlhxkfmx(JSON.toJSONString(hxmxdtos));
        portrayalParticulars.setXlhxZf(BigDecimal.valueOf(xlhxzf));
        portrayalParticulars.setXlhxKf(BigDecimal.valueOf(xlhxkf));
        portrayal.setXlhxScore(new BigDecimal(xlhxScore));
        riskDTO = new ReportDetailRiskDTO("心理画像", new BigDecimal(xlhxkf), conditions);
        reportDetailRiskDTOS.add(riskDTO);
//--------------------------------------------------------------------------------------------------------------------------
        //就业画像
        StringBuilder jyhxmsOne = new StringBuilder("1、该矫正对象就业画像风险为");
        StringBuilder jyhxmsTwo = new StringBuilder("2、该矫正对象是");
        StringBuilder jyhxmsThree = new StringBuilder("3、该矫正对象");
        int jyhxzf = MapUtil.getInt(scoringModelDetailMap, "就业画像", defaultIndexPoint);
        int jyztqzf = MapUtil.getInt(scoringModelDetailMap, "就业状态", defaultIndexPoint);
        int gzbdqzf = MapUtil.getInt(scoringModelDetailMap, "工作变动", defaultIndexPoint);
        int jylxqzf = MapUtil.getInt(scoringModelDetailMap, "就业类型", defaultIndexPoint);
        int zwlxqzf = MapUtil.getInt(scoringModelDetailMap, "职务类型", defaultIndexPoint);
        conditions = new ArrayList<>();
        //int jyhxqzzf = jyztqzf + gzbdqzf + jylxqzf + zwlxqzf;//就业画像权重总分
        int jyhxScore;//就业画像总得分
        int jyhxkf = 0;
        //"老年（60岁以上）、未成年：就业画像满分，不扣分 ，以下指标无需计算
        //中年（45-60岁）、壮年（35-45岁）、青年（18-35岁）：进入第二步"
        jyhxmsTwo.append(portrayal.getJobCondition());
        hxmxdtos = new ArrayList<>();
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxjySet = new HashSet<>();
        hxKfmxDTO.setZbx("年龄");
        if (age < 18) {
            //未成年
            jyhxmsTwo.append("未成年");
            hxKfmxDTO.setZbz("未成年");
        } else if (age >= 18 && age < 35) {
            //青年（18-35岁）
            jyhxmsTwo.append("青年");
            hxKfmxDTO.setZbz("青年");
        } else if (age >= 35 && age < 45) {
            //壮年（35-45岁）
            jyhxmsTwo.append("壮年");
            hxKfmxDTO.setZbz("壮年");
        } else if (age >= 45 && age < 60) {
            //中年（45-60岁）.
            jyhxmsTwo.append("中年");
            hxKfmxDTO.setZbz("中年");
        } else if (age >= 60) {
            //老年（60岁以上）
            jyhxmsTwo.append("老年");
            hxKfmxDTO.setZbz("老年");
        }
        conditions.add(new StringBuilder("年龄状况为").append(hxKfmxDTO.getZbz()).toString());
        hxmxdtos.add(hxKfmxDTO);
        jyhxmsTwo.append("一年内出现<h2>").append(portrayal.getJobMove()).append("次工作变动</h2>");
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("就业状态");
        hxKfmxDTO.setQzf(jyztqzf);
        if (age >= 18 && age < 60) {
            //就业状态
            switch (portrayal.getJobCondition()) {
                case "1":
                    hxKfmxDTO.setZbz("已就业");
                    break;
                case "2":
                    hxKfmxDTO.setZbz("无业");
                    jyhxkf = jyhxkf + 60;
                    hxKfmxDTO.setKf(60);
                    hxjySet.add("就业指导");
                    break;
                case "3":
                    hxKfmxDTO.setZbz("已退休");
                    break;
            }
        }
        conditions.add(new StringBuilder("就业状态为").append(hxKfmxDTO.getZbz()).toString());
        hxmxdtos.add(hxKfmxDTO);
        //工作变动
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("工作变动");
        hxKfmxDTO.setQzf(gzbdqzf);
        if (age >= 18 && age < 60) {
            if (portrayal.getJobMove() == 0) {
                hxKfmxDTO.setZbz("一年内出现没有工作变动");
            } else if (portrayal.getJobMove() == 1) {
                jyhxkf = jyhxkf + 5;
                hxKfmxDTO.setZbz("一年内出现1次工作变动");
                hxKfmxDTO.setKf(5);
            } else if (portrayal.getJobMove() == 2) {
                jyhxkf = jyhxkf + 10;
                hxKfmxDTO.setZbz("一年内出现2次工作变动");
                hxKfmxDTO.setKf(10);
            } else if (portrayal.getJobMove() >= 3) {
                jyhxkf = jyhxkf + 20;
                hxKfmxDTO.setZbz("一年内出现3次及以上工作变动");
                hxKfmxDTO.setKf(20);
            }
        }
        //工作变动有扣分的改善建议
        if (hxKfmxDTO.getKf() > 0) {
            hxjySet.add("个别谈话");
        }
        conditions.add(hxKfmxDTO.getZbz());
        hxmxdtos.add(hxKfmxDTO);
        //就业类型
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("就业类型");
        hxKfmxDTO.setQzf(jylxqzf);
        jyhxmsThree.append("<h2>");
        if (age >= 18 && age < 60) {
            switch (portrayal.getJobType()) {
                case 1:
                    hxKfmxDTO.setZbz("企事业单位");
                    jyhxmsThree.append("在企事业单位从事");
                    break;
                case 2:
                    hxKfmxDTO.setZbz("个体户");
                    jyhxmsThree.append("个体户");
                    break;
                case 3:
                    jyhxkf = jyhxkf + 20;
                    hxKfmxDTO.setZbz("自由职业");
                    jyhxmsThree.append("自由职业");
                    hxKfmxDTO.setKf(20);
                    break;
                case 4:
                    jyhxkf = jyhxkf + 20;
                    hxKfmxDTO.setZbz("兼职");
                    jyhxmsThree.append("兼职");
                    hxKfmxDTO.setKf(20);
                    break;
            }
        }
        //就业类型有扣分的改善建议
        if (hxKfmxDTO.getKf() > 0) {
            hxjySet.add("情况了解");
        }
        conditions.add(new StringBuilder("就业类型为").append(hxKfmxDTO.getZbz()).toString());
        hxmxdtos.add(hxKfmxDTO);
        //职务类型
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("职务类型");
        hxKfmxDTO.setQzf(zwlxqzf);
        if (age >= 18 && age < 60) {
            switch (portrayal.getPositionType()) {
                case 1:
                    jyhxkf = jyhxkf + 20;
                    hxKfmxDTO.setZbz("普通员工");
                    jyhxmsThree.append("普通员工的工作");
                    hxKfmxDTO.setKf(20);
                    break;
                case 2:
                    jyhxkf = jyhxkf + 10;
                    hxKfmxDTO.setZbz("专业人才");
                    jyhxmsThree.append("专业人才的工作");
                    hxKfmxDTO.setKf(10);
                    break;
                case 3:
                    jyhxkf = jyhxkf + 10;
                    hxKfmxDTO.setZbz("管理人员");
                    jyhxmsThree.append("管理人员的工作");
                    hxKfmxDTO.setKf(10);
                    break;
                case 4:
                    hxKfmxDTO.setZbz("老板");
                    jyhxmsThree.append("老板的工作");
                    break;
            }
        }
        //职务类型类型有扣分的改善建议
        if (hxKfmxDTO.getKf() > 0) {
            hxjySet.add("情况了解");
        }
        conditions.add(new StringBuilder("职务类型为").append(hxKfmxDTO.getZbz()).toString());
        hxmxdtos.add(hxKfmxDTO);
        jyhxScore = jyhxzf - jyhxkf <= 0 ? 0 : jyhxzf - jyhxkf;
        portrayal.setJyhxScore(new BigDecimal(jyhxScore));
        if (jyhxScore >= 50) {
            jyhxmsOne.append("<h2>低风险</h2>");
            portrayalParticulars.setJyhxLevel("低风险");
        } else if (jyhxScore >= 40 && jyhxScore < 50) {
            jyhxmsOne.append("<h2>较低风险</h2>");
            portrayalParticulars.setJyhxLevel("较低风险");
        } else if (jyhxScore >= 30 && jyhxScore < 40) {
            jyhxmsOne.append("<h2>中风险</h2>");
            portrayalParticulars.setJyhxLevel("中风险");
        } else if (jyhxScore >= 20 && jyhxScore < 30) {
            jyhxmsOne.append("<h2>较高风险</h2>");
            portrayalParticulars.setJyhxLevel("较高风险");
        } else if (jyhxScore >= 0 && jyhxScore < 20) {
            jyhxmsOne.append("<h2>高风险</h2>");
            portrayalParticulars.setJyhxLevel("高风险");
        }
        portrayalParticulars.setJyhxms(JSON.toJSONString(Lists.newArrayList(jyhxmsOne.toString(), jyhxmsTwo.toString(), jyhxmsThree.toString())));
        portrayalParticulars.setJyhxgjfx("该矫正对象入矫以来，就业画像从较低风险变为高风险，趋势恶化，建议重点关注。");
        portrayalParticulars.setJyhxjy(JSON.toJSONString(hxjySet));
        portrayalParticulars.setJyhxkfmx(JSON.toJSONString(hxmxdtos));
        portrayalParticulars.setJyhxZf(BigDecimal.valueOf(jyhxzf));
        portrayalParticulars.setJyhxKf(BigDecimal.valueOf(jyhxkf));
        portrayal.setJyhxScore(new BigDecimal(jyhxScore));
        riskDTO = new ReportDetailRiskDTO("就业画像", new BigDecimal(jyhxkf), conditions);
        reportDetailRiskDTOS.add(riskDTO);
//--------------------------------------------------------------------------------------------------------------------------
        //家庭画像
        StringBuilder jthxmsOne = new StringBuilder("1、该矫正对象家庭画像风险为");
        StringBuilder jthxmsTwo = new StringBuilder("2、该矫正对象");
        StringBuilder jthxmsThree = new StringBuilder("3、该矫正对象");
        StringBuilder jthxmsFour = new StringBuilder("4、该矫正对象");
        int jthxzf = MapUtil.getInt(scoringModelDetailMap, "家庭画像", defaultIndexPoint);
        int hyzkqzf = MapUtil.getInt(scoringModelDetailMap, "婚姻状况", defaultIndexPoint);
        int jtgxqzf = MapUtil.getInt(scoringModelDetailMap, "家庭关系", defaultIndexPoint);
        int jtmdqzf = MapUtil.getInt(scoringModelDetailMap, "家庭矛盾", defaultIndexPoint);
        int jbzgqzf = MapUtil.getInt(scoringModelDetailMap, "家人是否有重大疾病需要照顾", defaultIndexPoint);
        int jtbgqzf = MapUtil.getInt(scoringModelDetailMap, "家庭变故", defaultIndexPoint);
        int sfdbqzf = MapUtil.getInt(scoringModelDetailMap, "是否低保", defaultIndexPoint);
        int sftkqzf = MapUtil.getInt(scoringModelDetailMap, "是否特困", defaultIndexPoint);
        int jtsrqzf = MapUtil.getInt(scoringModelDetailMap, "家庭年收入", defaultIndexPoint);
        int jtfzqzf = MapUtil.getInt(scoringModelDetailMap, "家庭负债", defaultIndexPoint);
        conditions = new ArrayList<>();
        //int jthxqzzf = hyzkqzf + jtgxqzf + jtmdqzf + jbzgqzf + jtbgqzf + sfdbqzf + sftkqzf + jtsrqzf + jtfzqzf;//家庭画像权重总分
        int jthxScore;//家庭画像总得分
        int jthxkf = 0;
        hxmxdtos = new ArrayList<>();
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxjySet = new HashSet<>();
        jthxmsTwo.append("<h2>").append(portrayal.getHyzk()).append("</h2>");
        hxKfmxDTO.setZbx("婚姻状况");
        hxKfmxDTO.setQzf(hyzkqzf);
        hxKfmxDTO.setZbz(portrayal.getHyzk());
        if (portrayal.getHyzk().equals("已婚")) {
            //已婚或25周岁以下未婚：不扣分
        } else if (portrayal.getHyzk().equals("未婚")) {
            if (age <= 25) {
            } else {
                hxKfmxDTO.setKf(20);
                jthxkf = jthxkf + 20;
            }
            //丧偶、离异、大龄未婚（25周岁以上）：扣20
        } else {
            jthxkf = jthxkf + 20;
        }
        //婚姻状况有扣分的改善建议
        if (hxKfmxDTO.getKf() > 0) {
            hxjySet.add("情况了解");
        }
        conditions.add(new StringBuilder("婚姻状况为").append(hxKfmxDTO.getZbz()).toString());
        hxmxdtos.add(hxKfmxDTO);
        //家庭关系
        // "好、较好：不扣分
        //一般：扣3
        //较差、差：扣5"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("家庭关系");
        hxKfmxDTO.setQzf(jtgxqzf);
        jthxmsThree.append("<h2>");
        if (portrayal.getPositionType() == 1) {
            hxKfmxDTO.setZbz("好");
            jthxmsThree.append("家庭关系好、");
        } else if (portrayal.getPositionType() == 2) {
            hxKfmxDTO.setZbz("较好");
            jthxmsThree.append("家庭关系较好、");
        } else if (portrayal.getFamilyTies() == 3) {
            hxKfmxDTO.setZbz("一般");
            jthxmsThree.append("家庭关系一般、");
            jthxkf = jthxkf + 3;
            hxKfmxDTO.setKf(3);
        } else if (portrayal.getFamilyTies() == 4) {
            hxKfmxDTO.setZbz("较差");
            jthxmsThree.append("家庭关系较差、");
            jthxkf = jthxkf + 5;
            hxKfmxDTO.setKf(5);
        } else if (portrayal.getFamilyTies() == 5) {
            hxKfmxDTO.setZbz("差");
            jthxmsThree.append("家庭关系差、");
            jthxkf = jthxkf + 5;
        }
        jthxmsThree.append("最近与家里人");
        //家庭关系有扣分的改善建议
        if (hxKfmxDTO.getKf() > 0) {
            hxjySet.add("关系修复");
        }
        conditions.add(new StringBuilder("家庭关系").append(hxKfmxDTO.getZbz()).toString());
        hxmxdtos.add(hxKfmxDTO);
        //家庭矛盾
        //"最近是否和家里人发生过比较大的矛盾
        //有：扣5
        //无：不扣分"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("家庭矛盾");
        hxKfmxDTO.setQzf(jtmdqzf);
        hxKfmxDTO.setZbz("无");
        if (portrayal.getFamilyConflict() == 1) {
            jthxkf = jthxkf + 5;
            hxKfmxDTO.setZbz("有");
            hxKfmxDTO.setKf(5);
            //家庭矛盾有扣分的改善建议
            hxjySet.add("关系修复");
            jthxmsThree.append("发生过矛盾");
        } else {
            jthxmsThree.append("没有发生过矛盾");
        }
        jthxmsThree.append("</h2>");
        conditions.add(new StringBuilder(hxKfmxDTO.getZbz()).append("家庭矛盾").toString());
        hxmxdtos.add(hxKfmxDTO);
        //家庭变故
        //"是：扣5
        //否：不扣分"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("家庭变故");
        hxKfmxDTO.setQzf(jtbgqzf);
        hxKfmxDTO.setZbz("无");
        if (portrayal.getFamilyCalamity() == 1) {
            jthxkf = jthxkf + 5;
            hxKfmxDTO.setZbz("有");
            hxKfmxDTO.setKf(5);
            //家庭变故有扣分的改善建议
            hxjySet.add("个别谈话");
        }
        conditions.add(new StringBuilder(hxKfmxDTO.getZbz()).append("家庭变故").toString());
        hxmxdtos.add(hxKfmxDTO);
        //家人是否有重大疾病需要照顾
        //"家人是否有重大疾病需要照顾
        //是：扣5
        //否：不扣分"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("家庭健康");
        hxKfmxDTO.setQzf(jbzgqzf);
        hxKfmxDTO.setZbz("家人无重大疾病");
        if (portrayal.getFamilyIllness() == 1) {
            jthxkf = jthxkf + 5;
            hxKfmxDTO.setZbz("家人重有大疾病");
            hxKfmxDTO.setKf(5);
            //家人是否有重大疾病需要照顾有扣分的改善建议
            hxjySet.add("情况了解");
        }
        conditions.add(hxKfmxDTO.getZbz());
        hxmxdtos.add(hxKfmxDTO);
        //是否特困
        //"是：扣20，以下指标无需计算
        //否：不扣分"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("经济状况");
        hxKfmxDTO.setQzf(sfdbqzf + sftkqzf + jtsrqzf + jtfzqzf);
        int jjzkkf = 0;
        StringBuilder jjzkTempMs = new StringBuilder();
        if (portrayal.getPoverty() == 1) {
            jjzkTempMs.append("特困");
            jjzkkf = jjzkkf + 20;
            //是否特困有扣分的改善建议
            hxjySet.add("情况了解");
        }
        //是否低保
        //"是：扣20，以下指标无需计算
        //否：不扣分"
        if (portrayal.getInsured() == 1) {
            jjzkTempMs.append("  低保");
            jjzkkf = jjzkkf + 20;
            //是否低保有扣分的改善建议
            hxjySet.add("情况了解");
        }
        //家庭年收入
        //"无收入：扣20分，以下指标无需计算
        //8万以下：扣15分
        //8-15万：扣10分
        //15-20万：扣5分
        //20万以上：不扣分"
        switch (portrayal.getFamilyIncome()) {
            case 1:
                jjzkTempMs.append("  无家庭收入");
                jjzkkf = jjzkkf + 20;
                //家庭年收入有扣分的改善建议
                hxjySet.add("情况了解");
                break;
            case 2:
                jjzkTempMs.append("  家庭收入8万以下");
                jjzkkf = jjzkkf + 15;
                //家庭年收入有扣分的改善建议
                hxjySet.add("情况了解");
                break;
            case 3:
                jjzkTempMs.append("  家庭收入8-15万");
                jjzkkf = jjzkkf + 10;
                //家庭年收入有扣分的改善建议
                hxjySet.add("情况了解");
                break;
            case 4:
                jjzkTempMs.append("  家庭收入15-20万");
                jjzkkf = jjzkkf + 5;
                //家庭年收入有扣分的改善建议
                hxjySet.add("情况了解");
                break;
            case 5:
                jjzkTempMs.append("  家庭收入20万以上");
                break;
        }
        //家庭负债
        //"无：不扣分
        //有：扣10"
        if (portrayal.getFamilyLiabilities() == 1) {
            jjzkTempMs.append("  有家庭负债");
            jjzkkf = jjzkkf + 10;
            //家庭负债有扣分的改善建议
            hxjySet.add("情况了解");
        }
        jjzkkf = jjzkkf > hxKfmxDTO.getQzf() ? hxKfmxDTO.getQzf() : jjzkkf;
        jthxkf = jthxkf + jjzkkf;
        jthxmsFour.append("<h2>").append(jjzkTempMs).append("</h2>");
        hxKfmxDTO.setZbz(jjzkTempMs.toString());
        hxKfmxDTO.setKf(jjzkkf);
        conditions.add(hxKfmxDTO.getZbz());
        hxmxdtos.add(hxKfmxDTO);
        jthxScore = jthxzf - jthxkf <= 0 ? 0 : jthxzf - jthxkf;
        portrayal.setJthxScore(new BigDecimal(jthxScore));
        if (jthxScore >= 50) {
            jthxmsOne.append("<h2>低风险</h2>");
            portrayalParticulars.setJthxLevel("低风险");
        } else if (jthxScore >= 40 && jthxScore < 50) {
            jthxmsOne.append("<h2>较低风险</h2>");
            portrayalParticulars.setJthxLevel("较低风险");
        } else if (jthxScore >= 30 && jthxScore < 40) {
            jthxmsOne.append("<h2>中风险</h2>");
            portrayalParticulars.setJthxLevel("中风险");
        } else if (jthxScore >= 20 && jthxScore < 30) {
            jthxmsOne.append("<h2>较高风险</h2>");
            portrayalParticulars.setJthxLevel("较高风险");
        } else if (jthxScore >= 0 && jthxScore < 20) {
            jthxmsOne.append("<h2>高风险</h2>");
            portrayalParticulars.setJthxLevel("高风险");
        }
        portrayalParticulars.setJthxms(JSON.toJSONString(Lists.newArrayList(jthxmsOne.toString(), jthxmsTwo.toString(), jthxmsThree.toString(), jthxmsFour.toString())));
        portrayalParticulars.setJthxgjfx("该矫正对象入矫以来，家庭画像从较低风险变为高风险，趋势恶化，建议重点关注。");
        portrayalParticulars.setJthxjy(JSON.toJSONString(hxjySet));
        portrayalParticulars.setJthxkfmx(JSON.toJSONString(hxmxdtos));
        portrayalParticulars.setJthxZf(BigDecimal.valueOf(jthxzf));
        portrayalParticulars.setJthxKf(BigDecimal.valueOf(jthxkf));
        portrayal.setJthxScore(new BigDecimal(jthxScore));
        riskDTO = new ReportDetailRiskDTO("家庭画像", new BigDecimal(jthxkf), conditions);
        reportDetailRiskDTOS.add(riskDTO);
//--------------------------------------------------------------------------------------------------------------------------
        //个人基本画像
        StringBuilder jbhxmsOne = new StringBuilder("1、该矫正对象个人基本画像风险为");
        StringBuilder jbhxmsTwo = new StringBuilder("2、该矫正对象");
        StringBuilder jbhxmsThree = new StringBuilder("3、该矫正对象<h2>");
        StringBuilder jbhxmsFour = new StringBuilder("4、该矫正对象<h2>近期与人");
        int jbhxzf = MapUtil.getInt(scoringModelDetailMap, "个人基本画像", defaultIndexPoint);
        int grsrqzf = MapUtil.getInt(scoringModelDetailMap, "个人收入", defaultIndexPoint);
        int grfzqzf = MapUtil.getInt(scoringModelDetailMap, "个人负债", defaultIndexPoint);
        int sfcjqzf = MapUtil.getInt(scoringModelDetailMap, "是否残疾", defaultIndexPoint);
        int grjkqzf = MapUtil.getInt(scoringModelDetailMap, "个人健康", defaultIndexPoint);
        int mzqkqzf = MapUtil.getInt(scoringModelDetailMap, "门诊情况", defaultIndexPoint);
        int zyqkqzf = MapUtil.getInt(scoringModelDetailMap, "住院情况", defaultIndexPoint);
        int ymjzqzf = MapUtil.getInt(scoringModelDetailMap, "是否完成新冠疫苗接种", defaultIndexPoint);
        int jzqkqzf = MapUtil.getInt(scoringModelDetailMap, "居住情况", defaultIndexPoint);
        int ymdqzf = MapUtil.getInt(scoringModelDetailMap, "近期是否与人有矛盾", defaultIndexPoint);
        conditions = new ArrayList<>();
        //int jbhxqzzf = grsrqzf + grfzqzf + sfcjqzf + grjkqzf + mzqkqzf + ymjzqzf + zyqkqzf + jzqkqzf + ymdqzf; //个人基本画像权重总分
        int jbhxScore = 0;//个人基本画像得分
        int jbhxkf = 0;
        hxmxdtos = new ArrayList<>();
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxjySet = new HashSet<>();
        hxKfmxDTO.setZbx("经济状况");
        hxKfmxDTO.setQzf(grsrqzf + grfzqzf);
        StringBuilder grjjzkTemp = new StringBuilder();
        //个人收入
        //"未成年无收入：不扣分
        //已成年无收入：扣10分
        //5000以下：扣8
        //5000-8000：扣5
        //8000以上：不扣分 "

        //未成年无收入：不扣分
        if (portrayal.getPersonIncome() == 1) {
            if (age >= 18) {
                //已成年无收入：扣10分
                jbhxkf = jbhxkf + 10;
                //个人收入有扣分的改善建议
                hxjySet.add("情况了解");
            }
            grjjzkTemp.append("无收入");
        } else if (portrayal.getPersonIncome() == 2) {
            //5000以下：扣8
            jbhxkf = jbhxkf + 8;
            grjjzkTemp.append("5000以下");
            //个人收入有扣分的改善建议
            hxjySet.add("情况了解");
        } else if (portrayal.getPersonIncome() == 3) {
            //5000-8000：扣5
            jbhxkf = jbhxkf + 8;
            grjjzkTemp.append("5000-8000");
            //个人收入有扣分的改善建议
            hxjySet.add("情况了解");
        } else if (portrayal.getPersonIncome() == 4) {
            //8000以上：不扣分
            grjjzkTemp.append("8000以上");
        }
        //个人负债
        //"无：不扣分
        //有：扣10"
        if (portrayal.getPersonLiabilities() == 1) {
            //有：扣10"
            jbhxkf = jbhxkf + 10;
            grjjzkTemp.append("  有个人负债");
            jbhxmsTwo.append("<h2>有个人负债</h2>");
            //个人负债有扣分的改善建议
            hxjySet.add("情况了解");
        } else if (portrayal.getPersonLiabilities() == 0) {
            ////"无：不扣分
            grjjzkTemp.append("  无个人负债");
            jbhxmsTwo.append("<h2>无个人负债</h2>");
        }
        hxKfmxDTO.setZbz("个人收入" + grjjzkTemp.toString());
        hxKfmxDTO.setKf(jbhxkf);
        conditions.add(hxKfmxDTO.getZbz());
        hxmxdtos.add(hxKfmxDTO);

        //个人健康
        //"好、良好：不扣分
        //一般、较差： 扣5
        //差：扣10"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("健康状况");
        hxKfmxDTO.setQzf(sfcjqzf + grjkqzf + mzqkqzf + zyqkqzf);
        int jkzkkf = 0;
        StringBuilder grjkTemp = new StringBuilder("个人健康");
        switch (portrayal.getHealth()) {
            case 1:
                grjkTemp.append("好");
                break;
            case 2:
                grjkTemp.append("良好");
                break;
            case 3:
                jbhxkf = jbhxkf + 5;
                jkzkkf = jkzkkf + 5;
                grjkTemp.append("一般");
                //个人健康有扣分的改善建议
                hxjySet.add("情况了解");
                break;
            case 4:
                jbhxkf = jbhxkf + 5;
                jkzkkf = jkzkkf + 5;
                grjkTemp.append("较差");
                //个人健康有扣分的改善建议
                hxjySet.add("情况了解");
                break;
            case 5:
                jbhxkf = jbhxkf + 10;
                jkzkkf = jkzkkf + 10;
                grjkTemp.append("差");
                //个人健康有扣分的改善建议
                hxjySet.add("情况了解");
                break;
        }
        //门诊情况
        //"无：不扣分
        //有：扣5"
        if (portrayal.getOutpatientCondition() == 1) {
            jbhxkf = jbhxkf + 5;
            jkzkkf = jkzkkf + 5;
            grjkTemp.append("  近期有门诊记录");
            //门诊情况有扣分的改善建议
            hxjySet.add("情况了解");
        } else if (portrayal.getOutpatientCondition() == 0) {
            //"无：不扣分
        }
        //住院情况
        //"无：不扣分
        //有：扣10"
        if (portrayal.getHospitalization() == 1) {
            jbhxkf = jbhxkf + 10;
            jkzkkf = jkzkkf + 10;
            grjkTemp.append("  近期有住院");
            //住院情况有扣分的改善建议
            hxjySet.add("情况了解");
        } else if (portrayal.getHospitalization() == 0) {
            //"无：不扣分
        }
        //是否残疾
        //"否：不扣分
        //残疾三、四级（生活基本能自理）：扣10
        //残疾一、二级（生活不能自理）：扣15"
        if (portrayal.getDisability() == 0) {
            //"否：不扣分
        } else if (portrayal.getDisability() == 1) {
            //残疾三、四级（生活基本能自理）：扣10
            grjkTemp.append("  残疾三、四级（生活基本能自理");
            jbhxkf = jbhxkf + 10;
            jkzkkf = jkzkkf + 10;
            //是否残疾有扣分的改善建议
            hxjySet.add("情况了解");
        } else if (portrayal.getDisability() == 2) {
            //残疾一、二级（生活不能自理）：扣15"
            grjkTemp.append("  残疾一、二级（生活不能自理）");
            jbhxkf = jbhxkf + 15;
            jkzkkf = jkzkkf + 15;
            //是否残疾有扣分的改善建议
            hxjySet.add("情况了解");
        }
        hxKfmxDTO.setZbz(grjkTemp.toString());
        hxKfmxDTO.setKf(jkzkkf);
        jbhxmsThree.append(grjkTemp).append("</h2>");
        conditions.add(hxKfmxDTO.getZbz());
        hxmxdtos.add(hxKfmxDTO);
        //疫苗接种
        //"是否完成新冠疫苗接种：
        //否：扣5
        //是：不扣分"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("新冠疫苗接种");
        hxKfmxDTO.setQzf(ymjzqzf);
        if (portrayal.getXgybjz() == 1) {
            hxKfmxDTO.setZbz("已接种");
        } else if (portrayal.getXgybjz() == 0) {
            jbhxkf = jbhxkf + 5;
            hxKfmxDTO.setZbz("未接种");
            hxKfmxDTO.setKf(5);
            //疫苗接种有扣分的改善建议
            hxjySet.add("情况了解");
        }
        conditions.add(new StringBuilder(hxKfmxDTO.getZbx()).append(":").append(hxKfmxDTO.getZbz()).toString());
        hxmxdtos.add(hxKfmxDTO);
        //居住情况
        //"家人、朋友、男/女朋友同住：不扣分
        //独居：扣10"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("居住情况");
        hxKfmxDTO.setQzf(jzqkqzf);
        if (portrayal.getResidentialCondition() == 2) {
            jbhxkf = jbhxkf + 10;
            hxKfmxDTO.setZbz("独居");
            hxKfmxDTO.setKf(10);
            //居住情况有扣分的改善建议
            hxjySet.add("情况了解");
        } else if (portrayal.getResidentialCondition() == 1) {
            //"无：不扣分
            hxKfmxDTO.setZbz("非独居");
        }
        conditions.add(new StringBuilder(hxKfmxDTO.getZbx()).append(":").append(hxKfmxDTO.getZbz()).toString());
        hxmxdtos.add(hxKfmxDTO);
        //人际关系
        //"近期是否与人有矛盾：
        //是：扣10
        //否：不扣分"
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("人际关系");
        hxKfmxDTO.setQzf(ymdqzf);
        if (portrayal.getContradiction() == 1) {
            jbhxkf = jbhxkf + 10;
            hxKfmxDTO.setZbz("近期与人有矛盾");
            jbhxmsFour.append("有矛盾</h2>");
            //人际关系有扣分的改善建议
            hxjySet.add("情况了解");
        } else if (portrayal.getContradiction() == 0) {
            //"无：不扣分
            jbhxmsFour.append("无矛盾</h2>");
            hxKfmxDTO.setZbz("近期与人无矛盾");
        }
        conditions.add(new StringBuilder(hxKfmxDTO.getZbx()).append(":").append(hxKfmxDTO.getZbz()).toString());
        hxmxdtos.add(hxKfmxDTO);
        jbhxScore = jbhxzf - jbhxkf <= 0 ? 0 : jbhxzf - jbhxkf;
        portrayal.setJbhxScore(new BigDecimal(jbhxScore));
        if (jbhxScore >= 50) {
            jbhxmsOne.append("<h2>低风险</h2>");
            portrayalParticulars.setJbhxLevel("低风险");
        } else if (jbhxScore >= 40 && jbhxScore < 50) {
            jbhxmsOne.append("<h2>较低风险</h2>");
            portrayalParticulars.setJbhxLevel("较低风险");
        } else if (jbhxScore >= 30 && jbhxScore < 40) {
            jbhxmsOne.append("<h2>中风险</h2>");
            portrayalParticulars.setJbhxLevel("中风险");
        } else if (jbhxScore >= 20 && jbhxScore < 30) {
            jbhxmsOne.append("<h2>较高风险</h2>");
            portrayalParticulars.setJbhxLevel("较高风险");
        } else if (jbhxScore >= 0 && jbhxScore < 20) {
            jbhxmsOne.append("<h2>高风险</h2>");
            portrayalParticulars.setJbhxLevel("高风险");
        }
        portrayalParticulars.setJbhxms(JSON.toJSONString(Lists.newArrayList(jbhxmsOne.toString(), jbhxmsTwo.toString(), jbhxmsThree.toString(), jbhxmsFour.toString())));
        portrayalParticulars.setJbhxgjfx("该矫正对象入矫以来，个人基本画像从较低风险变为高风险，趋势恶化，建议重点关注。");
        portrayalParticulars.setJbhxjy(JSON.toJSONString(hxjySet));
        portrayalParticulars.setJbhxkfmx(JSON.toJSONString(hxmxdtos));
        portrayalParticulars.setJbhxZf(BigDecimal.valueOf(jbhxzf));
        portrayalParticulars.setJbhxKf(BigDecimal.valueOf(jbhxkf));
        portrayal.setJbhxScore(new BigDecimal(jbhxScore));
        riskDTO = new ReportDetailRiskDTO("个人基本画像", new BigDecimal(jbhxkf), conditions);
        reportDetailRiskDTOS.add(riskDTO);
//--------------------------------------------------------------------------------------------------------------------------
        //知法画像
        //"老年（60岁以上）、未成年：不扣分
        //中年（45-60岁）、壮年（35-45岁）、青年（18-35岁）：扣5"
        StringBuilder zfhxmsOne = new StringBuilder("1、该矫正对象知法画像风险为");
        StringBuilder zfhxmsTwo = new StringBuilder("2、该矫正对象");
        StringBuilder zfhxmsThree = new StringBuilder("3、该矫正对象");
        int zfhxzf = MapUtil.getInt(scoringModelDetailMap, "知法画像", defaultIndexPoint);
        int nkqzf = MapUtil.getInt(scoringModelDetailMap, "年龄(知法)", defaultIndexPoint);
        int xlqzf = MapUtil.getInt(scoringModelDetailMap, "学历", defaultIndexPoint);
        int qkqzf = MapUtil.getInt(scoringModelDetailMap, "犯罪前科", defaultIndexPoint);
        int gfqzf = MapUtil.getInt(scoringModelDetailMap, "累犯惯犯", defaultIndexPoint);
        int jgcsqzf = MapUtil.getInt(scoringModelDetailMap, "对社区矫正监管措施的熟悉程度", defaultIndexPoint);
        int jswtqzf = MapUtil.getInt(scoringModelDetailMap, "关于监管措施的问题", defaultIndexPoint);
        conditions = new ArrayList<>();
        //int zfhxqzzf = nkqzf + xlqzf + qkqzf + gfqzf + jgcsqzf + jswtqzf;//知法画像权重总分
        int zfhxScore;//知法画像总等分
        int zfhxkf = 0;
        //年龄
        //"老年（60岁以上）、未成年：不扣分
        //中年（45-60岁）、壮年（35-45岁）、青年（18-35岁）：扣5"
        hxmxdtos = new ArrayList<>();
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxjySet = new HashSet<>();
        hxKfmxDTO.setZbx("年龄");
        hxKfmxDTO.setQzf(nkqzf);
        if (age < 18) {
            //未成年
            portrayal.setAgeCondition("未成年");
            hxKfmxDTO.setZbz("未成年(18以下)");
            portrayal.setAgeCondition("未成年");
        } else if (age >= 18 && age < 35) {
            //青年（18-35岁）
            zfhxkf = zfhxkf + 5;
            portrayal.setAgeCondition("青年");
            hxKfmxDTO.setZbz("青年（18-35岁）");
            hxKfmxDTO.setKf(5);
        } else if (age >= 35 && age < 45) {
            //壮年（35-45岁）
            zfhxkf = zfhxkf + 5;
            portrayal.setAgeCondition("壮年");
            hxKfmxDTO.setZbz("壮年（35-45岁）");
            hxKfmxDTO.setKf(5);
        } else if (age >= 45 && age < 60) {
            //中年（45-60岁）.
            zfhxkf = zfhxkf + 5;
            portrayal.setAgeCondition("中年");
            hxKfmxDTO.setZbz("中年（45-60岁）");
            hxKfmxDTO.setKf(5);
        } else if (age >= 60) {
            //老年（60岁以上）
            portrayal.setAgeCondition("老年");
            hxKfmxDTO.setZbz("老年（60岁以上）");
        }
        conditions.add("年龄状况为" + hxKfmxDTO.getZbz());
        hxmxdtos.add(hxKfmxDTO);
        //学历
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("学历");
        hxKfmxDTO.setQzf(xlqzf);
        switch (portrayal.getEducation()) {
            case "1":
                zfhxkf = zfhxkf + 5;
                hxKfmxDTO.setKf(5);
                hxKfmxDTO.setZbz("本科及以上");
                break;
            case "2":
                zfhxkf = zfhxkf + 5;
                hxKfmxDTO.setKf(5);
                hxKfmxDTO.setZbz("大专");
                break;
            case "3":
                hxKfmxDTO.setKf(3);
                zfhxkf = zfhxkf + 3;
                hxKfmxDTO.setZbz("高中");
                break;
            case "4":
                hxKfmxDTO.setKf(3);
                zfhxkf = zfhxkf + 3;
                hxKfmxDTO.setZbz("中专和中技");
                break;
            case "5":
                hxKfmxDTO.setKf(3);
                zfhxkf = zfhxkf + 3;
                hxKfmxDTO.setZbz("初中");
                break;
            case "6":
                hxKfmxDTO.setKf(0);
                hxKfmxDTO.setZbz("小学");
                break;
            case "7":
                hxKfmxDTO.setKf(0);
                hxKfmxDTO.setZbz("文盲");
                break;

        }
        conditions.add("学历为" + hxKfmxDTO.getZbz());
        hxmxdtos.add(hxKfmxDTO);
        //是否有前科
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("犯罪前科");
        hxKfmxDTO.setQzf(qkqzf);
        if (portrayal.getCriminality() == 1) {
            zfhxkf = zfhxkf + 10;
            hxKfmxDTO.setZbz("有");
            hxKfmxDTO.setKf(10);
            zfhxmsTwo.append("<h2>犯罪前科：有</h2>");
            //是否有前科有扣分的改善教育
            hxjySet.add("普法教育");
        } else if (portrayal.getCriminality() == 0) {
            zfhxmsTwo.append("<h2>犯罪前科：无</h2>");
            hxKfmxDTO.setZbz("无");
        }
        conditions.add(new StringBuilder(hxKfmxDTO.getZbz()).append("犯罪前科").toString());
        hxmxdtos.add(hxKfmxDTO);
        //是否累犯惯犯
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("累犯惯犯");
        hxKfmxDTO.setQzf(gfqzf);
        if (portrayal.getRecidivist() == 1) {
            zfhxkf = zfhxkf + 10;
            hxKfmxDTO.setZbz("是");
            hxKfmxDTO.setKf(10);
            //是否累犯惯犯有扣分的改善教育
            hxjySet.add("普法教育");
        } else if (portrayal.getRecidivist() == 0) {
            hxKfmxDTO.setZbz("不是");
        }
        conditions.add(new StringBuilder(hxKfmxDTO.getZbz()).append("累犯惯犯").toString());
        hxmxdtos.add(hxKfmxDTO);
        //对社区矫正监管措施的熟悉程度
        hxKfmxDTO = new PortrayalKfmxDTO();
        hxKfmxDTO.setZbx("法律意识");
        hxKfmxDTO.setQzf(jgcsqzf + jswtqzf);
        int flyskf = 0;
        switch (portrayal.getJgcsFamiliarity()) {
            case 1:
                zfhxmsThree.append("<h2>对社区矫正监管措施的熟悉程度：熟悉</h2>");
                hxKfmxDTO.setZbz("1、监管措施的熟悉程度：熟悉");
                break;
            case 2:
                flyskf = flyskf + 10;
                zfhxmsThree.append("<h2>对社区矫正监管措施的熟悉程度：知道一点</h2>");
                hxKfmxDTO.setZbz("1、监管措施的熟悉程度：知道一点");
                break;
            case 3:
                flyskf = flyskf + 20;
                zfhxmsThree.append("<h2>对社区矫正监管措施的熟悉程度：不知道</h2>");
                hxKfmxDTO.setZbz("1、监管措施的熟悉程度：不知道");
                break;
        }
        //社区矫正监管措施问题是否通过
        if (portrayal.getJgcsPass() == 1) {
            hxKfmxDTO.setZbz(hxKfmxDTO.getZbz() + "      2、关于监管措施的问卷调查：通过");
        } else if (portrayal.getJgcsPass() == 0) {
            flyskf = flyskf + 20;
            hxKfmxDTO.setZbz(hxKfmxDTO.getZbz() + "      2、关于监管措施的问卷调查：不通过");
        }
        flyskf = flyskf >= 20 ? 20 : flyskf;
        zfhxkf = zfhxkf + flyskf;
        hxKfmxDTO.setKf(flyskf);
        //法律意识有扣分的改善建议
        if (hxKfmxDTO.getKf() > 0) {
            hxjySet.add("社区矫正监管措施普及教育");
        }
        conditions.add(new StringBuilder(hxKfmxDTO.getZbx()).append(":").append(hxKfmxDTO.getZbz()).toString());
        hxmxdtos.add(hxKfmxDTO);
        zfhxScore = zfhxzf - zfhxkf <= 0 ? 0 : zfhxzf - zfhxkf;
        portrayal.setZfhxScore(new BigDecimal(zfhxScore));
        if (zfhxScore >= 40) {
            zfhxmsOne.append("<h2>低风险</h2>");
            portrayalParticulars.setZfhxLevel("低风险");
        } else if (zfhxScore >= 35 && zfhxScore < 40) {
            zfhxmsOne.append("<h2>较低风险</h2>");
            portrayalParticulars.setZfhxLevel("较低风险");
        } else if (zfhxScore >= 30 && zfhxScore < 35) {
            zfhxmsOne.append("<h2>中风险</h2>");
            portrayalParticulars.setZfhxLevel("中风险");
        } else if (zfhxScore >= 20 && zfhxScore < 30) {
            zfhxmsOne.append("<h2>较高风险</h2>");
            portrayalParticulars.setZfhxLevel("较高风险");
        } else if (zfhxScore >= 0 && zfhxScore < 20) {
            zfhxmsOne.append("<h2>高风险</h2>");
            portrayalParticulars.setZfhxLevel("高风险");
        }
        portrayal.setZfhxScore(new BigDecimal(zfhxScore));
        portrayalParticulars.setZfhxms(JSON.toJSONString(Lists.newArrayList(zfhxmsOne.toString(), zfhxmsTwo.toString(), zfhxmsThree.toString())));
        portrayalParticulars.setZfhxgjfx("该矫正对象入矫以来，知法画像从较低风险变为高风险，趋势恶化，建议重点关注。");
        portrayalParticulars.setZfhxjy(JSON.toJSONString(Lists.newArrayList("1、进行社区矫正监管措施教育。", "   2、进行普法教育。")));
        portrayalParticulars.setZfhxkfmx(JSON.toJSONString(hxmxdtos));
        portrayalParticulars.setZfhxZf(BigDecimal.valueOf(zfhxzf));
        portrayalParticulars.setZfhxKf(BigDecimal.valueOf(zfhxkf));
        riskDTO = new ReportDetailRiskDTO("知法画像", new BigDecimal(zfhxkf), conditions);
        reportDetailRiskDTOS.add(riskDTO);
        portrayal.setScoreTotal(new BigDecimal(rjpgzf + xyzf + jfzf + rcjgzf + cfzf + xwdtzf + xlhxzf + jyhxzf + jbhxzf + zfhxzf + jthxzf));
        portrayal.setScoreEstimate(new BigDecimal(rjpgzf + xyzf + jfzf + rcjgScore + cfScore + xwdtScore + xlhxScore + zfhxScore + jyhxScore + jthxScore + jbhxScore));
        portrayal.setUpdateTime(new Date());
        this.updateById(portrayal);
        List<PortrayalGjqsVO> portrayalGjqsVOS = this.baseMapper.portrayalGjqs(portrayal.getId());
        List<String> xAxis = portrayalGjqsVOS.stream().map(vo -> vo.getMonthValue()).collect(Collectors.toList());
        List<BigDecimal> xlhxScoreYAxis = portrayalGjqsVOS.stream().map(vo -> vo.getXlhxScore()).collect(Collectors.toList());
        List<BigDecimal> zfhxScoreYAxis = portrayalGjqsVOS.stream().map(vo -> vo.getZfhxScore()).collect(Collectors.toList());
        List<BigDecimal> jyhxScoreYAxis = portrayalGjqsVOS.stream().map(vo -> vo.getJyhxScore()).collect(Collectors.toList());
        List<BigDecimal> jthxScoreYAxis = portrayalGjqsVOS.stream().map(vo -> vo.getJthxScore()).collect(Collectors.toList());
        List<BigDecimal> xyhxScoreYAxis = portrayalGjqsVOS.stream().map(vo -> vo.getXyhxScore()).collect(Collectors.toList());
        List<BigDecimal> jbhxScoreYAxis = portrayalGjqsVOS.stream().map(vo -> vo.getJbhxScore()).collect(Collectors.toList());
        portrayalParticulars.setGjqsXaxis(JSON.toJSONString(xAxis));
        portrayalParticulars.setXlhxGjqs(JSON.toJSONString(xlhxScoreYAxis));
        portrayalParticulars.setZfhxGjqs(JSON.toJSONString(zfhxScoreYAxis));
        portrayalParticulars.setJyhxGjqs(JSON.toJSONString(jyhxScoreYAxis));
        portrayalParticulars.setJthxGjqs(JSON.toJSONString(jthxScoreYAxis));
        portrayalParticulars.setXyhxGjqs(JSON.toJSONString(xyhxScoreYAxis));
        portrayalParticulars.setJbhxGjqs(JSON.toJSONString(jbhxScoreYAxis));
        correctionEstimatePortrayalParticularsService.saveOrUpdate(portrayalParticulars);
        //更新评估报告
        CorrectionEstimateReport report = new CorrectionEstimateReport();
        report.setSqjzryId(portrayal.getSqjzryId());
        report.setEstimateMonth(portrayal.getEstimateMonth());
        report.setScoreEstimate(portrayal.getScoreEstimate());
        report.setRisks(JSON.toJSONString(reportDetailRiskDTOS));
        reportService.updateCorrectionEstimateReport(report);
    }

    /**
     * 根据选项查询分数
     *
     * @param scoringModelId
     * @param detailName
     * @return
     */
    public int getScoreModel(String scoringModelId, String detailName) {
        return scoringModelDetailManagePortrayalService.getPoint(scoringModelId, detailName);
    }


    /**
     * 获取总分 日常监管 + 处罚 + 行为趋势 + 心理状态
     *
     * @param scoringModelId 模型id
     * @return
     */
    @Override
    public BigDecimal getTotal(String scoringModelId) {
        return new BigDecimal(
                getScoreModel(scoringModelId, "1530066180762685441") +
                        getScoreModel(scoringModelId, "1533357235671048194") +
                        getScoreModel(scoringModelId, "1530066249226309633") +
                        getScoreModel(scoringModelId, "1530066279328829441")
        );
    }

    @Override
    public CorrectionEstimatePortrayal newestByRyid(String ryid) {
        LambdaQueryWrapper<CorrectionEstimatePortrayal> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CorrectionEstimatePortrayal::getDelFlag, 0);
        lambdaQueryWrapper.eq(CorrectionEstimatePortrayal::getSqjzryId, ryid);
        lambdaQueryWrapper.orderByDesc(CorrectionEstimatePortrayal::getEstimateMonth);
        lambdaQueryWrapper.last("limit 1");
        return getOne(lambdaQueryWrapper);

    }

    @Override
    public CorrectionEstimatePortrayal calculateScore(CorrectionEstimatePortrayal correctionEstimatePortrayal) {
        //监管得分
        //日常监管得分计算规则：
        //
        //1、降到严管扣50分
        //
        //2、出现一次违规，扣20分，扣完为止
        correctionEstimatePortrayal.getLevelDown();
        //处罚得分
        //行为动态得分
        //心理画像得分
        //知法画像得分
        //就业画像得分
        //家庭画像等分
        //信用画像得分
        //个人基本画像得分
        //奖励得分
        return null;
    }

    public static void main(String[] args) {
        System.out.println(IdcardUtil.getAgeByIdCard("330283199012154736"));
    }
}
