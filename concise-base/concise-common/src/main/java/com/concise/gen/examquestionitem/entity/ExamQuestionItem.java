package com.concise.gen.examquestionitem.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.concise.gen.examquestion.entity.ExamQuestion;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.List;

/**
 * 问题选项
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:11
 */
@Data
@TableName("exam_question_item")
public class ExamQuestionItem {

    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 父选项
     */
    private String pid;
    /**
     * 关联exam_question id
     */
    private String questionId;

    /**
     * 选项内容
     */
    private String content;

    /**
     * 排序字段
     */
    private int sort;

    /**
     * 选项
     */
    private String item;

    /**
     * 子级题目
     */
    @TableField(exist = false)
    private List<ExamQuestion> examQuestion;

    /**
     * 是否被选中
     */
    @TableField(exist = false)
    private boolean checked;

}
