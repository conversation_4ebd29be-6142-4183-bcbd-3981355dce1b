<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.correctionestimateportrayaldetail.mapper.CorrectionEstimatePortrayalDetailMapper">

    <select id="riskAssessment"
            resultType="com.concise.gen.correctionestimateportrayaldetail.entity.vo.RiskAssessmentVo">
        select xlhx_level xlhxLevel,
               zfhx_level zfhxLevel,
               jyhx_level jyhxLevel,
               jthx_level jthxLevel,
               xyhx_level xyhxLevel,
               jbhx_level jbhxLevel,
               count(*) count
        from correction_estimate_portrayal_particulars
        GROUP BY xlhx_level, zfhx_level, jyhx_level, jthx_level, xyhx_level, jbhx_level
    </select>
    <select id="riskStatistics"
            resultType="com.concise.gen.correctionestimateportrayaldetail.entity.vo.RiskStatisticsVo">
        select level riskLevel, count(*) count
        from correction_estimate_portrayal_detail
        where `level`=#{riskLevel}
    </select>
</mapper>
