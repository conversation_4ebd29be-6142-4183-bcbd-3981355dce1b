package com.concise.gen.portraitImage.correctionassesmembers.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 评估管理--人员机构信息(只用于评估未开始前的查看)
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:41
 */
@Data
@TableName("correction_asses_members")
public class CorrectionAssesMembers {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 评估管理ID
     */
    @ApiModelProperty(value = "评估管理ID")
    private String baseId;

    /**
     * 类型：0：机构 1: 人员
     */
    @ApiModelProperty(value = "类型：0：机构 1: 人员")
    private Integer type;

    /**
     * 人员或机构id
     */
    @ApiModelProperty(value = "人员或机构id")
    private String memberId;

    /**
     * 人员或机构名称
     */
    @ApiModelProperty(value = "人员名称")
    private String name;

}
