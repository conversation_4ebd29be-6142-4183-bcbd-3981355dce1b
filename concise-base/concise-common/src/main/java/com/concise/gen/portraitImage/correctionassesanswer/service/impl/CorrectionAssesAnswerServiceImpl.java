package com.concise.gen.portraitImage.correctionassesanswer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionobjectinformation.entity.ScreenModel;
import com.concise.gen.portraitImage.correctionassesanswer.entity.CorrectionAssesAnswer;
import com.concise.gen.portraitImage.correctionassesanswer.enums.CorrectionAssesAnswerExceptionEnum;
import com.concise.gen.portraitImage.correctionassesanswer.mapper.CorrectionAssesAnswerMapper;
import com.concise.gen.portraitImage.correctionassesanswer.param.CorrectionAssesAnswerParam;
import com.concise.gen.portraitImage.correctionassesanswer.service.CorrectionAssesAnswerService;
import com.concise.gen.portraitImage.correctionassespersondtl.entity.CorrectionAssesPersonDtlModel;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * 评估管理--评估人员评估明细答案service接口实现类
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:45
 */
@Service
public class CorrectionAssesAnswerServiceImpl extends ServiceImpl<CorrectionAssesAnswerMapper, CorrectionAssesAnswer> implements CorrectionAssesAnswerService {

    @Override
    public PageResult<CorrectionAssesAnswer> page(CorrectionAssesAnswerParam correctionAssesAnswerParam) {
        QueryWrapper<CorrectionAssesAnswer> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionAssesAnswerParam)) {

            // 根据评估人员评估明细id(correction_asses_person_dtl.ID) 查询
            if (ObjectUtil.isNotEmpty(correctionAssesAnswerParam.getDtlId())) {
                queryWrapper.lambda().eq(CorrectionAssesAnswer::getDtlId, correctionAssesAnswerParam.getDtlId());
            }
            // 根据问题id 查询
            if (ObjectUtil.isNotEmpty(correctionAssesAnswerParam.getQuestionId())) {
                queryWrapper.lambda().eq(CorrectionAssesAnswer::getQuestionId, correctionAssesAnswerParam.getQuestionId());
            }
            // 根据答案ids 查询
            if (ObjectUtil.isNotEmpty(correctionAssesAnswerParam.getAnswerIds())) {
                queryWrapper.lambda().eq(CorrectionAssesAnswer::getAnswerIds, correctionAssesAnswerParam.getAnswerIds());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionAssesAnswer> list(CorrectionAssesAnswerParam correctionAssesAnswerParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionAssesAnswerParam correctionAssesAnswerParam) {
        CorrectionAssesAnswer correctionAssesAnswer = new CorrectionAssesAnswer();
        BeanUtil.copyProperties(correctionAssesAnswerParam, correctionAssesAnswer);
        this.save(correctionAssesAnswer);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionAssesAnswerParam correctionAssesAnswerParam) {
        this.removeById(correctionAssesAnswerParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionAssesAnswerParam correctionAssesAnswerParam) {
        CorrectionAssesAnswer correctionAssesAnswer = this.queryCorrectionAssesAnswer(correctionAssesAnswerParam);
        BeanUtil.copyProperties(correctionAssesAnswerParam, correctionAssesAnswer);
        this.updateById(correctionAssesAnswer);
    }

    @Override
    public CorrectionAssesAnswer detail(CorrectionAssesAnswerParam correctionAssesAnswerParam) {
        return this.queryCorrectionAssesAnswer(correctionAssesAnswerParam);
    }

    /**
     * 获取评估管理--评估人员评估明细答案
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:45
     */
    private CorrectionAssesAnswer queryCorrectionAssesAnswer(CorrectionAssesAnswerParam correctionAssesAnswerParam) {
        CorrectionAssesAnswer correctionAssesAnswer = this.getById(correctionAssesAnswerParam.getId());
        if (ObjectUtil.isNull(correctionAssesAnswer)) {
            throw new ServiceException(CorrectionAssesAnswerExceptionEnum.NOT_EXIST);
        }
        return correctionAssesAnswer;
    }

    @Override
    public List<ScreenModel> law(Set<String> userIds) {
        return this.baseMapper.law(userIds);
    }

    @Override
    public List<ScreenModel> workingCondition(Set<String> userIds) {
        return this.baseMapper.workingCondition(userIds);
    }

    @Override
    public List<ScreenModel> workingType(Set<String> userIds) {
        return this.baseMapper.workingType(userIds);

    }

    @Override
    public List<ScreenModel> identityAndOccupation(Set<String> userIds) {
        return this.baseMapper.identityAndOccupation(userIds);
    }

    @Override
    public List<ScreenModel> sourceOfIncome(Set<String> userIds) {
        return this.baseMapper.sourceOfIncome(userIds);
    }

    @Override
    public List<ScreenModel> householdIncome(Set<String> userIds) {
        return this.baseMapper.householdIncome(userIds);

    }

    @Override
    public List<ScreenModel> householdDebt(Set<String> userIds) {
        return this.baseMapper.householdDebt(userIds);

    }

    @Override
    public List<ScreenModel> familyConflicts(Set<String> userIds) {
        return this.baseMapper.familyConflicts(userIds);

    }

    @Override
    public List<CorrectionAssesPersonDtlModel> latestRevenueList(Set<String> userIds) {
        return this.baseMapper.latestRevenueList(userIds);
    }

    @Override
    public List<CorrectionAssesPersonDtlModel> liabilityList(Set<String> userIds) {
        return this.baseMapper.liabilityList(userIds);
    }
}
