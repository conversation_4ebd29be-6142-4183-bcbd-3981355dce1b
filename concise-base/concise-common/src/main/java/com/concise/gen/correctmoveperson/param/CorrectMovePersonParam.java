package com.concise.gen.correctmoveperson.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 流动人口参数类
 *
 * <AUTHOR>
 * @date 2022-05-24 16:51:04
*/
@Data
public class CorrectMovePersonParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 矫正对象id
     */
    @NotBlank(message = "矫正对象id不能为空，请检查sqjzryId参数", groups = {add.class, edit.class})
    private String sqjzryId;

    /**
     * 登记日期
     */
    @NotBlank(message = "登记日期不能为空，请检查djrq参数", groups = {add.class, edit.class})
    private String djrq;

    /**
     * 登记状态名称
     */
    @NotBlank(message = "登记状态名称不能为空，请检查djztmc参数", groups = {add.class, edit.class})
    private String djztmc;

    /**
     * 到期日期
     */
    @NotBlank(message = "到期日期不能为空，请检查dqrq参数", groups = {add.class, edit.class})
    private String dqrq;

    /**
     * 出租人公民身份号码
     */
    @NotBlank(message = "出租人公民身份号码不能为空，请检查fzsfzh参数", groups = {add.class, edit.class})
    private String fzsfzh;

    /**
     * 出租人姓名
     */
    @NotBlank(message = "出租人姓名不能为空，请检查fzxm参数", groups = {add.class, edit.class})
    private String fzxm;

    /**
     * 街路巷
     */
    @NotBlank(message = "街路巷不能为空，请检查jlx参数", groups = {add.class, edit.class})
    private String jlx;

    /**
     * 民族
     */
    @NotBlank(message = "民族不能为空，请检查mz参数", groups = {add.class, edit.class})
    private String mz;

    /**
     * 派出所
     */
    @NotBlank(message = "派出所不能为空，请检查pcs参数", groups = {add.class, edit.class})
    private String pcs;

    /**
     * 派出所中文
     */
    private String pcsdm;

    /**
     * 县（市、区）
     */
    @NotBlank(message = "县（市、区）不能为空，请检查qx参数", groups = {add.class, edit.class})
    private String qx;

    /**
     * 县（市、区）中文
     */
    private String qxdm;

    /**
     * 公民身份号码
     */
    @NotBlank(message = "公民身份号码不能为空，请检查sfzh参数", groups = {add.class, edit.class})
    private String sfzh;

    /**
     * 数据归集日期
     */
    @NotBlank(message = "数据归集日期不能为空，请检查tongTime参数", groups = {add.class, edit.class})
    private String tongTime;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空，请检查xb参数", groups = {add.class, edit.class})
    private String xb;

    /**
     * 性别中文名
     */
    private String xbdm;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 居住地址
     */
    @NotBlank(message = "居住地址不能为空，请检查zzdz参数", groups = {add.class, edit.class})
    private String zzdz;

    /**
     * 居住证号
     */
    @NotBlank(message = "居住证号不能为空，请检查zzzh参数", groups = {add.class, edit.class})
    private String zzzh;

    /**
     * 登记日期_begin
     */
    private String djrq_begin;

    /**
     * 登记日期_end
     */
    private String djrq_end;

    /**
     * 矫正机构ID
     */
    private String jzjg;
}
