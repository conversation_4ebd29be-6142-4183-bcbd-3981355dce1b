package com.concise.gen.correctioncorrectplan.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
* 矫正方案参数类
 *
 * <AUTHOR>
 * @date 2022-03-07 14:27:46
*/
@Data
public class CorrectionCorrectPlanParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * 矫正对象id
     */
    private String sqjzryId;

    /**
     * 姓名
     */
    private String sqjzryName;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 矫正机构ID
     */
    private String jzjg;

    /**
     * 矫正机构名称
     */
    private String jzjgName;

    /**
     * 主要犯罪事实
     */
    private String criminalBehavior;

    /**
     * 现实表现
     */
    private String actuallyExpression;

    /**
     * 社会反应及心理测试情况
     */
    private String societyPshchologicalTest;

    /**
     * 矫正意见
     */
    private String correctIdea;

    /**
     * 矫正方案措施
     */
    private String correctPlan;

    /**
     * 监管措施
     */
    private String correctMeasure;

    /**
     * 方案调整时间
     */
    private Date adjustTime;

    /**
     * 方案调整原因，字典值：TZYY
     */
    private String adjustReason;

    /**
     * 方案发挥成效; 1: 良好 2：一般 3：不佳
     */
    private Integer exertEffect;

    /**
     * 社矫对象表现; 1: 良好 2：一般 3：不佳
     */
    private Integer expression;

    /**
     * 上次实施效果评估
     */
    private String exertEstimate;

    /**
     * 方案状态，0：待完善 1：已完善
     */
    private Integer planStatus;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 生成日期_begin
     */
    private String createTime_begin;

    /**
     * 生成日期_end
     */
    private String createTime_end;

    /**
     * 方案调整日期_begin
     */
    private String adjustTime_begin;

    /**
     * 方案调整日期_end
     */
    private String adjustTime_end;

    /**
     * 方案调整次数
     */
    private int planNum;

    /**
     * 矫正阶段 1：入矫初期 2：矫正中期 3：矫正末期
     */
    private int phase;

    /**
     * 查询标志位： 0 查在矫  1： 查在矫以外的
     */
    private int tag;
}
