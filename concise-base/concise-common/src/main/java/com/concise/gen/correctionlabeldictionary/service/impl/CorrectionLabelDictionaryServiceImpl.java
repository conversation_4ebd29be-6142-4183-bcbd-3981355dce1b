package com.concise.gen.correctionlabeldictionary.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionabilitylable.entity.CorrectionAbilityLable;
import com.concise.gen.correctionlabeldictionary.entity.CorrectionLabelDictionary;
import com.concise.gen.correctionlabeldictionary.enums.CorrectionLabelDictionaryExceptionEnum;
import com.concise.gen.correctionlabeldictionary.mapper.CorrectionLabelDictionaryMapper;
import com.concise.gen.correctionlabeldictionary.param.CorrectionLabelDictionaryParam;
import com.concise.gen.correctionlabeldictionary.service.CorrectionLabelDictionaryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 标签关联字典值表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-03-08 22:24:16
 */
@Service
public class CorrectionLabelDictionaryServiceImpl extends ServiceImpl<CorrectionLabelDictionaryMapper, CorrectionLabelDictionary> implements CorrectionLabelDictionaryService {

    @Override
    public PageResult<CorrectionLabelDictionary> page(CorrectionLabelDictionaryParam correctionLabelDictionaryParam) {
        QueryWrapper<CorrectionLabelDictionary> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionLabelDictionaryParam)) {

            // 根据标签ID 查询
            if (ObjectUtil.isNotEmpty(correctionLabelDictionaryParam.getLabelId())) {
                queryWrapper.lambda().eq(CorrectionLabelDictionary::getLabelId, correctionLabelDictionaryParam.getLabelId());
            }
            // 根据字典code 查询
            if (ObjectUtil.isNotEmpty(correctionLabelDictionaryParam.getDictionaryCode())) {
                queryWrapper.lambda().eq(CorrectionLabelDictionary::getDictionaryCode, correctionLabelDictionaryParam.getDictionaryCode());
            }
            // 根据字典name 查询
            if (ObjectUtil.isNotEmpty(correctionLabelDictionaryParam.getDictionaryName())) {
                queryWrapper.lambda().eq(CorrectionLabelDictionary::getDictionaryName, correctionLabelDictionaryParam.getDictionaryName());
            }
            // 根据字典类别name 查询
            if (ObjectUtil.isNotEmpty(correctionLabelDictionaryParam.getDictionaryTypeName())) {
                queryWrapper.lambda().eq(CorrectionLabelDictionary::getDictionaryTypeName, correctionLabelDictionaryParam.getDictionaryTypeName());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionLabelDictionary> list(CorrectionLabelDictionaryParam correctionLabelDictionaryParam) {
        QueryWrapper<CorrectionLabelDictionary> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionLabelDictionaryParam)) {

            // 根据标签ID 查询
            if (ObjectUtil.isNotEmpty(correctionLabelDictionaryParam.getLabelId())) {
                queryWrapper.lambda().eq(CorrectionLabelDictionary::getLabelId, correctionLabelDictionaryParam.getLabelId());
            }
            // 根据字典code 查询
            if (ObjectUtil.isNotEmpty(correctionLabelDictionaryParam.getDictionaryCode())) {
                queryWrapper.lambda().eq(CorrectionLabelDictionary::getDictionaryCode, correctionLabelDictionaryParam.getDictionaryCode());
            }
            // 根据字典name 查询
            if (ObjectUtil.isNotEmpty(correctionLabelDictionaryParam.getDictionaryName())) {
                queryWrapper.lambda().eq(CorrectionLabelDictionary::getDictionaryName, correctionLabelDictionaryParam.getDictionaryName());
            }
            // 根据字典类别name 查询
            if (ObjectUtil.isNotEmpty(correctionLabelDictionaryParam.getDictionaryTypeName())) {
                queryWrapper.lambda().eq(CorrectionLabelDictionary::getDictionaryTypeName, correctionLabelDictionaryParam.getDictionaryTypeName());
            }
        }
        return this.list(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(CorrectionLabelDictionaryParam correctionLabelDictionaryParam) {
        CorrectionLabelDictionary correctionLabelDictionary = this.queryCorrectionLabelDictionary(correctionLabelDictionaryParam);
        BeanUtil.copyProperties(correctionLabelDictionaryParam, correctionLabelDictionary);
        this.save(correctionLabelDictionary);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionLabelDictionaryParam correctionLabelDictionaryParam) {
        this.removeById(correctionLabelDictionaryParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionLabelDictionaryParam correctionLabelDictionaryParam) {
        CorrectionLabelDictionary correctionLabelDictionary = this.queryCorrectionLabelDictionary(correctionLabelDictionaryParam);
        BeanUtil.copyProperties(correctionLabelDictionaryParam, correctionLabelDictionary);
        this.updateById(correctionLabelDictionary);
    }

    @Override
    public CorrectionLabelDictionary detail(CorrectionLabelDictionaryParam correctionLabelDictionaryParam) {
        return this.queryCorrectionLabelDictionary(correctionLabelDictionaryParam);
    }

    /**
     * 获取标签关联字典值表
     *
     * <AUTHOR>
     * @date 2022-03-08 22:24:16
     */
    private CorrectionLabelDictionary queryCorrectionLabelDictionary(CorrectionLabelDictionaryParam correctionLabelDictionaryParam) {
        CorrectionLabelDictionary correctionLabelDictionary = this.getById(correctionLabelDictionaryParam.getId());
        if (ObjectUtil.isNull(correctionLabelDictionary)) {
            throw new ServiceException(CorrectionLabelDictionaryExceptionEnum.NOT_EXIST);
        }
        return correctionLabelDictionary;
    }
}
