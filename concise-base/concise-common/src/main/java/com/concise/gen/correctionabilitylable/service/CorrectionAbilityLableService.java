package com.concise.gen.correctionabilitylable.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionabilitylable.entity.CorrectionAbilityLable;
import com.concise.gen.correctionabilitylable.param.CorrectionAbilityLableParam;
import java.util.List;

/**
 * 能力管理、标签关联表service接口
 *
 * <AUTHOR>
 * @date 2022-03-03 11:00:37
 */
public interface CorrectionAbilityLableService extends IService<CorrectionAbilityLable> {

    /**
     * 查询能力管理、标签关联表
     *
     * <AUTHOR>
     * @date 2022-03-03 11:00:37
     */
    PageResult<CorrectionAbilityLable> page(CorrectionAbilityLableParam correctionAbilityLableParam);

    /**
     * 能力管理、标签关联表列表
     *
     * <AUTHOR>
     * @date 2022-03-03 11:00:37
     */
    List<CorrectionAbilityLable> list(CorrectionAbilityLableParam correctionAbilityLableParam);

    /**
     * 添加能力管理、标签关联表
     *
     * <AUTHOR>
     * @date 2022-03-03 11:00:37
     */
    void add(CorrectionAbilityLableParam correctionAbilityLableParam);

    /**
     * 删除能力管理、标签关联表
     *
     * <AUTHOR>
     * @date 2022-03-03 11:00:37
     */
    void delete(CorrectionAbilityLableParam correctionAbilityLableParam);

    /**
     * 编辑能力管理、标签关联表
     *
     * <AUTHOR>
     * @date 2022-03-03 11:00:37
     */
    void edit(CorrectionAbilityLableParam correctionAbilityLableParam);

    /**
     * 查看能力管理、标签关联表
     *
     * <AUTHOR>
     * @date 2022-03-03 11:00:37
     */
     CorrectionAbilityLable detail(CorrectionAbilityLableParam correctionAbilityLableParam);
}
