package com.concise.gen.correctionabilitymanage.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionabilitylable.entity.CorrectionAbilityLable;
import com.concise.gen.correctionabilitylable.service.CorrectionAbilityLableService;
import com.concise.gen.correctionabilitymanage.entity.CorrectionAbilityManage;
import com.concise.gen.correctionabilitymanage.enums.CorrectionAbilityManageExceptionEnum;
import com.concise.gen.correctionabilitymanage.mapper.CorrectionAbilityManageMapper;
import com.concise.gen.correctionabilitymanage.param.CorrectionAbilityManageParam;
import com.concise.gen.correctionabilitymanage.service.CorrectionAbilityManageService;
import com.concise.gen.correctionlabelmanage.entity.CorrectionLabelManage;
import com.concise.gen.correctionlabelmanage.mapper.CorrectionLabelManageMapper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * 能力管理service接口实现类
 *
 * <AUTHOR>
 * @date 2022-03-03 10:57:44
 */
@Service
public class CorrectionAbilityManageServiceImpl extends ServiceImpl<CorrectionAbilityManageMapper, CorrectionAbilityManage> implements CorrectionAbilityManageService {

    @Resource
    private CorrectionLabelManageMapper correctionLabelManageMapper;

    @Resource
    private CorrectionAbilityLableService correctionAbilityLableService;

    @Override
    public PageResult<CorrectionAbilityManage> page(CorrectionAbilityManageParam correctionAbilityManageParam) {
        QueryWrapper<CorrectionAbilityManage> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionAbilityManageParam)) {

            // 根据日常监管措施 查询
            if (ObjectUtil.isNotEmpty(correctionAbilityManageParam.getDailySupervisionStep())) {
                queryWrapper.lambda().like(CorrectionAbilityManage::getDailySupervisionStep, correctionAbilityManageParam.getDailySupervisionStep());
            }
            // 根据教育帮扶措施 查询
            if (ObjectUtil.isNotEmpty(correctionAbilityManageParam.getEducationalAssistanceStep())) {
                queryWrapper.lambda().like(CorrectionAbilityManage::getEducationalAssistanceStep, correctionAbilityManageParam.getEducationalAssistanceStep());
            }
            // 根据心理矫正措施 查询
            if (ObjectUtil.isNotEmpty(correctionAbilityManageParam.getPsychologicalCorrectionStep())) {
                queryWrapper.lambda().like(CorrectionAbilityManage::getPsychologicalCorrectionStep, correctionAbilityManageParam.getPsychologicalCorrectionStep());
            }

        }
        queryWrapper.lambda().eq(CorrectionAbilityManage::getDelFlag, 0);
        queryWrapper.lambda().isNull(CorrectionAbilityManage::getPid);
        // 根据标签查询
        List<String> labelIdsList = null;
        if (ObjectUtil.isNotEmpty(correctionAbilityManageParam.getLabelIds())) {
            labelIdsList = Arrays.asList(correctionAbilityManageParam.getLabelIds().split(","));
        }
        Page<CorrectionAbilityManage> page = this.baseMapper.page(PageFactory.defaultPage(), queryWrapper, labelIdsList);
        List<CorrectionAbilityManage> records = page.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            for (CorrectionAbilityManage record : records) {
                record.setChildrenList(this.list(new QueryWrapper<CorrectionAbilityManage>().lambda().eq(CorrectionAbilityManage::getPid, record.getId())));
            }
        }
        return new PageResult<>(page);
    }

    @Override
    public List<CorrectionAbilityManage> list(CorrectionAbilityManageParam correctionAbilityManageParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionAbilityManageParam correctionAbilityManageParam) {
        if (null == correctionAbilityManageParam.getCorrectPhase()) {
            correctionAbilityManageParam.setCorrectPhase(0);
        }
        List<CorrectionAbilityManageParam> plans = correctionAbilityManageParam.getPlans();
        if (CollectionUtil.isEmpty(plans)) {
            throw new ServiceException(CorrectionAbilityManageExceptionEnum.PLAN_NOT_EXIST);
        }
        CorrectionAbilityManage correctionAbilityManage = new CorrectionAbilityManage();
        BeanUtil.copyProperties(correctionAbilityManageParam, correctionAbilityManage);
        correctionAbilityManage.setCreateTime(DateUtil.date());
        correctionAbilityManage.setEducationalAssistanceStep(plans.get(0).getEducationalAssistanceStep());
        boolean bool = this.save(correctionAbilityManage);
        // 能力保存成功则保存关联标签信息
        if (bool) {
            String labelIds = correctionAbilityManage.getLabelIds();
            if (ObjectUtil.isNotEmpty(labelIds)) {
                List<CorrectionAbilityLable> list = new ArrayList<>();
                CorrectionAbilityLable cay = null;
                for (String labelId : labelIds.split(",")) {
                    cay = new CorrectionAbilityLable();
                    cay.setAbilityId(correctionAbilityManage.getId());
                    cay.setLableId(labelId);
                    list.add(cay);
                }
                correctionAbilityLableService.saveBatch(list);
            }
        }

        if (plans.size() > 1) {
            //将除第一个方案外的其他子方案保存
            for (int i = 0; i < plans.size(); i++) {
                if (i != 0) {
                    CorrectionAbilityManageParam manageParam = plans.get(i);
                    CorrectionAbilityManage child = new CorrectionAbilityManage();
                    child.setPid(correctionAbilityManage.getId());
                    child.setEducationalAssistanceStep(manageParam.getEducationalAssistanceStep());
                    child.setLabelIds(manageParam.getLabelIds());
                    child.setCreateTime(DateUtil.date());
                    child.setCorrectPhase(0);
                    boolean save = this.save(child);
                    if (save) {
                        String labelIds = child.getLabelIds();
                        if (ObjectUtil.isNotEmpty(labelIds)) {
                            List<CorrectionAbilityLable> list = new ArrayList<>();
                            CorrectionAbilityLable cay = null;
                            for (String labelId : labelIds.split(",")) {
                                cay = new CorrectionAbilityLable();
                                cay.setAbilityId(child.getId());
                                cay.setLableId(labelId);
                                list.add(cay);
                            }
                            correctionAbilityLableService.saveBatch(list);
                        }
                    }

                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionAbilityManageParam correctionAbilityManageParam) {
        CorrectionAbilityManage correctionAbilityManage = new CorrectionAbilityManage();
        correctionAbilityManage.setId(correctionAbilityManageParam.getId());
        correctionAbilityManage.setDelFlag(1);
        this.updateById(correctionAbilityManage);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionAbilityManageParam correctionAbilityManageParam) {
        CorrectionAbilityManage correctionAbilityManage = this.queryCorrectionAbilityManage(correctionAbilityManageParam);
        List<CorrectionAbilityManageParam> plans = correctionAbilityManageParam.getPlans();
        if (CollectionUtil.isEmpty(plans)) {
            throw new ServiceException(CorrectionAbilityManageExceptionEnum.PLAN_NOT_EXIST);
        }
        BeanUtil.copyProperties(correctionAbilityManageParam, correctionAbilityManage);
        correctionAbilityManage.setEducationalAssistanceStep(plans.get(0).getEducationalAssistanceStep());
        boolean bool = this.updateById(correctionAbilityManage);
        if (bool) {
            // 能力修改成功，则保存关联标签信息（删除现有的，再保存）
            // 删除原有标签
            correctionAbilityLableService.remove(new QueryWrapper<CorrectionAbilityLable>().lambda()
                    .eq(CorrectionAbilityLable::getAbilityId, correctionAbilityManage.getId()));
            // 保存
            String labelIds = correctionAbilityManage.getLabelIds();
            if (ObjectUtil.isNotEmpty(labelIds)) {
                List<CorrectionAbilityLable> list = new ArrayList<>();
                CorrectionAbilityLable cay = null;
                for (String labelId : labelIds.split(",")) {
                    cay = new CorrectionAbilityLable();
                    cay.setAbilityId(correctionAbilityManage.getId());
                    cay.setLableId(labelId);
                    list.add(cay);
                }
                correctionAbilityLableService.saveBatch(list);
            }
        }
        if (plans.size() > 1) {
            //查询原有的子方案，对比新的，看看有没有被删除的需要删一下
            List<CorrectionAbilityManage> childrenList = this.list(new QueryWrapper<CorrectionAbilityManage>().lambda()
                    .eq(CorrectionAbilityManage::getPid, correctionAbilityManage.getId()));
            for (CorrectionAbilityManage children : childrenList) {
                boolean flag = true;
                for (CorrectionAbilityManageParam plan : plans) {
                    if (children.getId().equals(plan.getId())) {
                        flag = false;
                        break;
                    }
                }
                if (flag) {
                    //删除
                    this.removeById(children.getId());
                    correctionAbilityLableService.remove(new QueryWrapper<CorrectionAbilityLable>().lambda()
                            .eq(CorrectionAbilityLable::getAbilityId, children.getId()));
                }
            }
            //将除第一个方案外的其他子方案新增或者编辑，或者删除
            for (int i = 0; i < plans.size(); i++) {
                if (i != 0) {
                    //没有id的要新增，有id的要编辑
                    CorrectionAbilityManageParam plan = plans.get(i);
                    if (ObjectUtil.isEmpty(plan.getId())) {
                        //新增
                        CorrectionAbilityManage child = new CorrectionAbilityManage();
                        child.setPid(correctionAbilityManage.getId());
                        child.setEducationalAssistanceStep(plan.getEducationalAssistanceStep());
                        child.setLabelIds(plan.getLabelIds());
                        child.setCreateTime(DateUtil.date());
                        child.setCorrectPhase(0);
                        boolean save = this.save(child);
                        if (save) {
                            String labelIds = plan.getLabelIds();
                            if (ObjectUtil.isNotEmpty(labelIds)) {
                                List<CorrectionAbilityLable> list = new ArrayList<>();
                                CorrectionAbilityLable cay = null;
                                for (String labelId : labelIds.split(",")) {
                                    cay = new CorrectionAbilityLable();
                                    cay.setAbilityId(child.getId());
                                    cay.setLableId(labelId);
                                    list.add(cay);
                                }
                                correctionAbilityLableService.saveBatch(list);
                            }
                        }
                    } else {
                        //编辑
                        CorrectionAbilityManage child = this.getById(plan.getId());
                        child.setEducationalAssistanceStep(plan.getEducationalAssistanceStep());
                        child.setLabelIds(plan.getLabelIds());
                        boolean boolChild = this.updateById(child);
                        if (boolChild) {
                            String labelIds = plan.getLabelIds();
                            //删除原有标签
                            correctionAbilityLableService.remove(new QueryWrapper<CorrectionAbilityLable>().lambda()
                                    .eq(CorrectionAbilityLable::getAbilityId, child.getId()));
                            if (ObjectUtil.isNotEmpty(labelIds)) {
                                List<CorrectionAbilityLable> list = new ArrayList<>();
                                CorrectionAbilityLable cay = null;
                                for (String labelId : labelIds.split(",")) {
                                    cay = new CorrectionAbilityLable();
                                    cay.setAbilityId(child.getId());
                                    cay.setLableId(labelId);
                                    list.add(cay);
                                }
                                correctionAbilityLableService.saveBatch(list);
                            }

                        }
                    }


                }

            }
        } else {
            //只剩1个的时候，删除多余的子方案
            List<CorrectionAbilityManage> childrenList = this.list(new QueryWrapper<CorrectionAbilityManage>().lambda()
                    .eq(CorrectionAbilityManage::getPid, correctionAbilityManage.getId()));
            for (CorrectionAbilityManage children : childrenList) {
                this.removeById(children.getId());
                correctionAbilityLableService.remove(new QueryWrapper<CorrectionAbilityLable>().lambda()
                        .eq(CorrectionAbilityLable::getAbilityId, children.getId()));
            }
        }
    }

    @Override
    public CorrectionAbilityManage detail(CorrectionAbilityManageParam correctionAbilityManageParam) {
        return this.queryCorrectionAbilityManage(correctionAbilityManageParam);
    }

    /**
     * 获取能力管理
     *
     * <AUTHOR>
     * @date 2022-03-03 10:57:44
     */
    private CorrectionAbilityManage queryCorrectionAbilityManage(CorrectionAbilityManageParam correctionAbilityManageParam) {
        CorrectionAbilityManage correctionAbilityManage = this.getById(correctionAbilityManageParam.getId());
        if (ObjectUtil.isNull(correctionAbilityManage)) {
            throw new ServiceException(CorrectionAbilityManageExceptionEnum.NOT_EXIST);
        }
        //查询能力关联的标签list
        List<CorrectionLabelManage> labelList = correctionLabelManageMapper.findByAbilityId(correctionAbilityManageParam.getId());
        correctionAbilityManage.setLableList(labelList);
        //查询子标签
        List<CorrectionAbilityManage> children = this.list(new LambdaQueryWrapper<CorrectionAbilityManage>().eq(CorrectionAbilityManage::getPid, correctionAbilityManageParam.getId()));
        if (ObjectUtil.isNotEmpty(children)) {
            for (CorrectionAbilityManage child : children) {
                List<CorrectionLabelManage> labelList2 = correctionLabelManageMapper.findByAbilityId(child.getId());
                child.setLableList(labelList2);
            }
        }
        correctionAbilityManage.setChildrenList(children);
        return correctionAbilityManage;
    }
}
