package com.concise.gen.correctionplaninfomeasureeducation. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.correctionplaninfomeasureeducation. param.CorrectionPlanInfoMeasureEducationParam;
import com.concise.gen.correctionplaninfomeasureeducation. service.CorrectionPlanInfoMeasureEducationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 矫正方案2.0监管措施_教育帮扶每月课件分类学习记录控制器
 *
 * <AUTHOR>
 * @date 2024-11-21 16:55:24
 */
@Api(tags = "矫正方案2.0监管措施_教育帮扶每月课件分类学习记录")
@RestController
public class CorrectionPlanInfoMeasureEducationController {

    @Resource
    private CorrectionPlanInfoMeasureEducationService correctionPlanInfoMeasureEducationService;

    /**
     * 查询矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
     *
     * <AUTHOR>
     * @date 2024-11-21 16:55:24
     */
    @Permission
    @GetMapping("/correctionPlanInfoMeasureEducation/page")
    @ApiOperation("矫正方案2.0监管措施_教育帮扶每月课件分类学习记录_分页查询")
    @BusinessLog(title = "矫正方案2.0监管措施_教育帮扶每月课件分类学习记录_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam) {
        return new SuccessResponseData(correctionPlanInfoMeasureEducationService.page(correctionPlanInfoMeasureEducationParam));
    }

    /**
     * 添加矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
     *
     * <AUTHOR>
     * @date 2024-11-21 16:55:24
     */
    @Permission
    @PostMapping("/correctionPlanInfoMeasureEducation/add")
    @ApiOperation("矫正方案2.0监管措施_教育帮扶每月课件分类学习记录_增加")
    @BusinessLog(title = "矫正方案2.0监管措施_教育帮扶每月课件分类学习记录_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(CorrectionPlanInfoMeasureEducationParam.add.class) CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam) {
        correctionPlanInfoMeasureEducationService.add(correctionPlanInfoMeasureEducationParam);
        return new SuccessResponseData();
    }

    /**
     * 删除矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
     *
     * <AUTHOR>
     * @date 2024-11-21 16:55:24
     */
    @Permission
    @PostMapping("/correctionPlanInfoMeasureEducation/delete")
    @ApiOperation("矫正方案2.0监管措施_教育帮扶每月课件分类学习记录_删除")
    @BusinessLog(title = "矫正方案2.0监管措施_教育帮扶每月课件分类学习记录_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(CorrectionPlanInfoMeasureEducationParam.delete.class) CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam) {
        correctionPlanInfoMeasureEducationService.delete(correctionPlanInfoMeasureEducationParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
     *
     * <AUTHOR>
     * @date 2024-11-21 16:55:24
     */
    @Permission
    @PostMapping("/correctionPlanInfoMeasureEducation/edit")
    @ApiOperation("矫正方案2.0监管措施_教育帮扶每月课件分类学习记录_编辑")
    @BusinessLog(title = "矫正方案2.0监管措施_教育帮扶每月课件分类学习记录_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(CorrectionPlanInfoMeasureEducationParam.edit.class) CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam) {
        correctionPlanInfoMeasureEducationService.edit(correctionPlanInfoMeasureEducationParam);
        return new SuccessResponseData();
    }

    /**
     * 查看矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
     *
     * <AUTHOR>
     * @date 2024-11-21 16:55:24
     */
    @Permission
    @GetMapping("/correctionPlanInfoMeasureEducation/detail")
    @ApiOperation("矫正方案2.0监管措施_教育帮扶每月课件分类学习记录_查看")
    @BusinessLog(title = "矫正方案2.0监管措施_教育帮扶每月课件分类学习记录_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(CorrectionPlanInfoMeasureEducationParam.detail.class) CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam) {
        return new SuccessResponseData(correctionPlanInfoMeasureEducationService.detail(correctionPlanInfoMeasureEducationParam));
    }

    /**
     * 矫正方案2.0监管措施_教育帮扶每月课件分类学习记录列表
     *
     * <AUTHOR>
     * @date 2024-11-21 16:55:24
     */
    @Permission
    @GetMapping("/correctionPlanInfoMeasureEducation/list")
    @ApiOperation("矫正方案2.0监管措施_教育帮扶每月课件分类学习记录_列表")
    @BusinessLog(title = "矫正方案2.0监管措施_教育帮扶每月课件分类学习记录_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam) {
        return new SuccessResponseData(correctionPlanInfoMeasureEducationService.list(correctionPlanInfoMeasureEducationParam));
    }

}
