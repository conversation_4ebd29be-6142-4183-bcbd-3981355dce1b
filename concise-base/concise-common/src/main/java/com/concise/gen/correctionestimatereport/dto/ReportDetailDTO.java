package com.concise.gen.correctionestimatereport.dto;


import com.concise.gen.correctionestimatereport.entity.CorrectionEstimateReport;
import lombok.Data;

import java.util.List;

@Data
public class ReportDetailDTO {

    /**
     * 基本信息
     */
    private CorrectionEstimateReport baseInfo;

    /**
     * 本月风险
     */
    private List<ReportDetailRiskDTO> currentRisks;

    /**
     * 本月风险等级
     */
    private String currentLevel;

    /**
     * 风险趋势
     */
    private TrendDTO risksTrend;

    /**
     * 上月风险
     */
    private List<ReportDetailRiskDTO> lastRisks;

    /**
     * 本月风险等级
     */
    private String lastLevel;

    /**
     * 历史风险
     */
    private List<ReportDetailRiskDTO> historyRisks;
}
