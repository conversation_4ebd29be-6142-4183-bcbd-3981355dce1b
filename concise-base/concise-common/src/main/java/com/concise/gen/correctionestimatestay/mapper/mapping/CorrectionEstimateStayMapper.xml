<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.correctionestimatestay.mapper.CorrectionEstimateStayMapper">

    <select id="getCorrectionObjectId" parameterType="String" resultType="String">
        SELECT id FROM ccgf0.ccgf_correction_object_basic WHERE correction_aid = #{sqjzryId} and del_flag = 0 and status = 200
    </select>

    <select id="getNums" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM ccgf0.ccgf_abnormal_handle WHERE correction_object_id = #{correctionObjectId} AND date_format(alert_time, '%Y%m') = #{estimateMonth} and handle_result &lt; 205
        UNION ALL
        SELECT COUNT(*) FROM sqjzzxsjk0.correction_xj WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT count(*) FROM sqjzzxsjk0.correction_warning WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT count(*) FROM sqjzzxsjk0.correction_penalty WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT count(*) FROM sqjzzxsjk0.correction_arrest  WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT count(*) FROM sqjzzxsjk0.correction_probation WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT count(*) FROM sqjzzxsjk0.correction_parole WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT count(*) FROM sqjzzxsjk0.correction_prison WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT COUNT(*) FROM ccgf0.correction_analyse_workplace WHERE correction_object_id = #{correctionObjectId} AND time = #{estimateMonth}
        UNION ALL
        SELECT COUNT(*) FROM ccgf0.correction_night_out WHERE correction_object_id = #{correctionObjectId} AND date_format(time, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT COUNT(*) FROM sqjzzxsjk0.correction_praise WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT COUNT(*) FROM sqjzzxsjk0.correction_commutation WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
    </select>

    <select id="getNumsAll" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM ccgf0.ccgf_abnormal_handle WHERE correction_object_id = #{correctionObjectId} and handle_result &lt; 205
        UNION ALL
        SELECT COUNT(*) FROM sqjzzxsjk0.correction_xj WHERE pid = #{pid}
        UNION ALL
        SELECT count(*) FROM sqjzzxsjk0.correction_warning WHERE pid = #{pid}
        UNION ALL
        SELECT count(*) FROM sqjzzxsjk0.correction_penalty WHERE pid = #{pid}
        UNION ALL
        SELECT count(*) FROM sqjzzxsjk0.correction_arrest  WHERE pid = #{pid}
        UNION ALL
        SELECT count(*) FROM sqjzzxsjk0.correction_probation WHERE pid = #{pid}
        UNION ALL
        SELECT count(*) FROM sqjzzxsjk0.correction_parole WHERE pid = #{pid}
        UNION ALL
        SELECT count(*) FROM sqjzzxsjk0.correction_prison WHERE pid = #{pid}
        UNION ALL
        SELECT COUNT(*) FROM ccgf0.correction_analyse_workplace WHERE correction_object_id = #{correctionObjectId}
        UNION ALL
        SELECT COUNT(*) FROM ccgf0.correction_night_out WHERE correction_object_id = #{correctionObjectId}
        UNION ALL
        SELECT COUNT(*) FROM sqjzzxsjk0.correction_praise WHERE pid = #{pid}
        UNION ALL
        SELECT COUNT(*) FROM sqjzzxsjk0.correction_commutation WHERE pid = #{pid}
    </select>

    <select id="getMoonCode" resultType="String">
        SELECT mood_code FROM sqjzzxsjk0.correct_mood_code WHERE third_id = #{thirdId} ORDER BY update_time DESC LIMIT 1
    </select>

    <select id="getGljb" resultType="String">
        SELECT gljb FROM sqjzzxsjk0.correction_level  WHERE pid = #{pid} AND del_flag = 0 AND date_format(bdrq, '%Y%m') = #{estimateMonth} and gljb = '1' LIMIT 1
    </select>
</mapper>
