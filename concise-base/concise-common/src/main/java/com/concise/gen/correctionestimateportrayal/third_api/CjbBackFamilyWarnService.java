package com.concise.gen.correctionestimateportrayal.third_api;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.concise.gen.correctionestimateportrayal.third_api.param.CjbBackFamilyWarn;
import com.qcloud.cos.utils.Md5Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 家庭矛盾推送
 */

@Service
@Slf4j
public class CjbBackFamilyWarnService {


    @Value("${family_warn_ush.address}")
    private String requestAddress;


    public String familyWarnPush(CjbBackFamilyWarn cjbBackFamilyWarn) {
        String requestUrl = new StringBuilder(requestAddress).append("correct-boot/eduOpen/cjbBackFamilyWarn/push").toString();
        String appKey = "43mTsgqxXRxc";
        String requestSecret = "MEaizckihtnFxA85AuYDiDKERp2H8b6z";
        String requestTime = String.valueOf(DateUtil.current());
        // String url = requestUri.append("?appKey=").append(appKey).append("&sign=").append(sign) .append("&requestTime=") .append(requestTime).toString();
        String sign = Md5Utils.md5Hex(appKey + requestTime + requestSecret);
        Map<String, String> map = new HashMap<>();
        map.put("key", appKey);
        map.put("sign", sign);
        map.put("timeStamp", requestTime);
        HttpRequest request = new HttpRequest(requestUrl);
        request.body(JSON.toJSONString(cjbBackFamilyWarn), "application/json");
        request.method(Method.POST);
        request.addHeaders(map);
        log.info("request headers{}", JSON.toJSONString(map));
        log.info("request body{}", JSON.toJSONString(cjbBackFamilyWarn));
        try {
            String body = request.execute().body();
            return body;
        } catch (Exception e) {
            return null;
        }

    }
}
