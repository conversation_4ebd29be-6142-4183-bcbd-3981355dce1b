package com.concise.gen.correctionestimatereportdetail.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
* 评估报告详情参数类
 *
 * <AUTHOR>
 * @date 2023-01-10 15:55:35
*/
@Data
public class CorrectionEstimateReportDetailParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 评估报告id
     */
    @NotBlank(message = "评估报告id不能为空，请检查reportId参数", groups = {add.class, edit.class})
    private String reportId;

    /**
     * 指标
     */
    @NotNull(message = "指标不能为空，请检查index参数", groups = {add.class, edit.class})
    private Integer index;

    /**
     * 指标名称
     */
    @NotBlank(message = "指标名称不能为空，请检查indexName参数", groups = {add.class, edit.class})
    private String indexName;

    /**
     * 当前分数
     */
    @NotNull(message = "当前分数不能为空，请检查currentScore参数", groups = {add.class, edit.class})
    private BigDecimal score;

    /**
     * 情况描述
     */
    @NotBlank(message = "情况描述不能为空，请检查condition参数", groups = {add.class, edit.class})
    private String condition;

}
