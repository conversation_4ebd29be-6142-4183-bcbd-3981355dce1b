package com.concise.gen.portraitImage.correctionquestion.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.portraitImage.correctionquestionitem.entity.CorrectionQuestionItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
* 量表配置--试题信息表参数类
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:35
*/
@Data
public class CorrectionQuestionParam extends BaseParam {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 量表id
     */
    @ApiModelProperty(value = "量表id")
    private String scaleBaseId;

    /**
     * 问题名称
     */
    @ApiModelProperty(value = "问题名称")
    private String question;

    /**
     * 问题类型（0：单选 1：多选）
     */
    @ApiModelProperty(value = "问题类型（0：单选 1：多选）")
    private Integer questionType;

    /**
     * 问题序号
     */
    @ApiModelProperty(value = "问题序号")
    private Integer orderIndex;

    /**
     * 关联的选项id
     */
    @ApiModelProperty(value = "关联的选项id")
    private String relevanceItemId;

    /**
     * 是否删除（0：未删除，1删除）
     */
    @ApiModelProperty(value = "是否删除（0：未删除，1删除）")
    private Integer delFlag;

    /**
     * 问题选项
     */
    @ApiModelProperty(value = "问题选项")
    private List<CorrectionQuestionItem> questionItemList;

    /**
     * 选中的选项ids
     */
    @ApiModelProperty(value = "选中的选项ids")
    private String chooseItemIds;

}
