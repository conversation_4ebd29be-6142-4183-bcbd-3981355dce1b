package com.concise.gen.portraitImage.correctionassesmembers.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.portraitImage.correctionassesmembers.entity.CorrectionAssesMembers;
import com.concise.gen.portraitImage.correctionassesmembers.enums.CorrectionAssesMembersExceptionEnum;
import com.concise.gen.portraitImage.correctionassesmembers.mapper.CorrectionAssesMembersMapper;
import com.concise.gen.portraitImage.correctionassesmembers.param.CorrectionAssesMembersParam;
import com.concise.gen.portraitImage.correctionassesmembers.service.CorrectionAssesMembersService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 评估管理--人员机构信息(只用于评估未开始前的查看)service接口实现类
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:41
 */
@Service
public class CorrectionAssesMembersServiceImpl extends ServiceImpl<CorrectionAssesMembersMapper, CorrectionAssesMembers> implements CorrectionAssesMembersService {

    @Override
    public PageResult<CorrectionAssesMembers> page(CorrectionAssesMembersParam correctionAssesMembersParam) {
        QueryWrapper<CorrectionAssesMembers> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionAssesMembersParam)) {

            // 根据评估管理ID 查询
            if (ObjectUtil.isNotEmpty(correctionAssesMembersParam.getBaseId())) {
                queryWrapper.lambda().eq(CorrectionAssesMembers::getBaseId, correctionAssesMembersParam.getBaseId());
            }
            // 根据类型：0：机构 1: 人员 查询
            if (ObjectUtil.isNotEmpty(correctionAssesMembersParam.getType())) {
                queryWrapper.lambda().eq(CorrectionAssesMembers::getType, correctionAssesMembersParam.getType());
            }
            // 根据人员或机构id 查询
            if (ObjectUtil.isNotEmpty(correctionAssesMembersParam.getMemberId())) {
                queryWrapper.lambda().eq(CorrectionAssesMembers::getMemberId, correctionAssesMembersParam.getMemberId());
            }
            // 根据人员或机构名称 查询
            if (ObjectUtil.isNotEmpty(correctionAssesMembersParam.getName())) {
                queryWrapper.lambda().eq(CorrectionAssesMembers::getName, correctionAssesMembersParam.getName());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionAssesMembers> list(CorrectionAssesMembersParam correctionAssesMembersParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionAssesMembersParam correctionAssesMembersParam) {
        CorrectionAssesMembers correctionAssesMembers = new CorrectionAssesMembers();
        BeanUtil.copyProperties(correctionAssesMembersParam, correctionAssesMembers);
        this.save(correctionAssesMembers);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionAssesMembersParam correctionAssesMembersParam) {
        this.removeById(correctionAssesMembersParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionAssesMembersParam correctionAssesMembersParam) {
        CorrectionAssesMembers correctionAssesMembers = this.queryCorrectionAssesMembers(correctionAssesMembersParam);
        BeanUtil.copyProperties(correctionAssesMembersParam, correctionAssesMembers);
        this.updateById(correctionAssesMembers);
    }

    @Override
    public CorrectionAssesMembers detail(CorrectionAssesMembersParam correctionAssesMembersParam) {
        return this.queryCorrectionAssesMembers(correctionAssesMembersParam);
    }

    /**
     * 获取评估管理--人员机构信息(只用于评估未开始前的查看)
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:41
     */
    private CorrectionAssesMembers queryCorrectionAssesMembers(CorrectionAssesMembersParam correctionAssesMembersParam) {
        CorrectionAssesMembers correctionAssesMembers = this.getById(correctionAssesMembersParam.getId());
        if (ObjectUtil.isNull(correctionAssesMembers)) {
            throw new ServiceException(CorrectionAssesMembersExceptionEnum.NOT_EXIST);
        }
        return correctionAssesMembers;
    }
}
