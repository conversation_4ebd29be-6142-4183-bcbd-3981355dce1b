package com.concise.gen.correctionlabelcorrpsn.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 标签关联矫正对象参数类
 *
 * <AUTHOR>
 * @date 2022-03-04 14:58:19
*/
@Data
public class CorrectionLabelCorrpsnParam extends BaseParam {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 标签ID
     */
    private String labelId;

    /**
     * 矫正对象ID
     */
    private String sqjzryId;

    /**
     * 矫正对象身份证号
     */
    private String sfzh;

    /**
     * 矫正对象IDs
     */
    private String[] sqjzryIds;
    /**
     * 标签IDs
     */
    private String[] labelIds;

}
