package com.concise.gen.indexmanage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.indexmanage.entity.IndexManage;
import com.concise.gen.indexmanage.enums.IndexManageExceptionEnum;
import com.concise.gen.indexmanage.mapper.IndexManageMapper;
import com.concise.gen.indexmanage.param.IndexManageParam;
import com.concise.gen.indexmanage.service.IndexManageService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 指标管理信息表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-05-12 10:02:19
 */
@Service
public class IndexManageServiceImpl extends ServiceImpl<IndexManageMapper, IndexManage> implements IndexManageService {

    @Override
    public PageResult<IndexManage> page(IndexManageParam indexManageParam) {
        QueryWrapper<IndexManage> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(indexManageParam)) {

            // 根据指标分类 查询
            if (ObjectUtil.isNotEmpty(indexManageParam.getIndexType())) {
                queryWrapper.lambda().like(IndexManage::getIndexType, indexManageParam.getIndexType());
            }
            // 根据删除状态 查询
            if (ObjectUtil.isNotEmpty(indexManageParam.getDelFlag())) {
                queryWrapper.lambda().eq(IndexManage::getDelFlag, indexManageParam.getDelFlag());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<IndexManage> list(IndexManageParam indexManageParam) {
        QueryWrapper<IndexManage> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(indexManageParam)) {

            // 根据指标分类 查询
            if (ObjectUtil.isNotEmpty(indexManageParam.getIndexType())) {
                queryWrapper.lambda().like(IndexManage::getIndexType, indexManageParam.getIndexType());
            }
        }

        queryWrapper.lambda().eq(IndexManage::getDelFlag, "0");
        return this.list(queryWrapper);
    }

    @Override
    public void add(IndexManageParam indexManageParam) {
        IndexManage indexManage = new IndexManage();
        BeanUtil.copyProperties(indexManageParam, indexManage);
        this.save(indexManage);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(IndexManageParam indexManageParam) {
        IndexManage indexManage = new IndexManage();
        indexManage.setId(indexManageParam.getId());
        indexManage.setDelFlag("1");
        this.updateById(indexManage);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(IndexManageParam indexManageParam) {
        IndexManage indexManage = this.queryIndexManage(indexManageParam);
        BeanUtil.copyProperties(indexManageParam, indexManage);
        this.updateById(indexManage);
    }

    @Override
    public IndexManage detail(IndexManageParam indexManageParam) {
        return this.queryIndexManage(indexManageParam);
    }

    /**
     * 获取指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    private IndexManage queryIndexManage(IndexManageParam indexManageParam) {
        IndexManage indexManage = this.getById(indexManageParam.getId());
        if (ObjectUtil.isNull(indexManage)) {
            throw new ServiceException(IndexManageExceptionEnum.NOT_EXIST);
        }
        return indexManage;
    }
}
