package com.concise.gen.correctionrulesandregulations.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.param.SysFileInfoParam;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionrulesandregulations.entity.CorrectionRulesAndRegulations;
import com.concise.gen.correctionrulesandregulations.enums.CorrectionRulesAndRegulationsExceptionEnum;
import com.concise.gen.correctionrulesandregulations.mapper.CorrectionRulesAndRegulationsMapper;
import com.concise.gen.correctionrulesandregulations.param.CorrectionRulesAndRegulationsParam;
import com.concise.gen.correctionrulesandregulations.service.CorrectionRulesAndRegulationsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 规章制度service接口实现类
 *
 * <AUTHOR>
 * @date 2022-03-10 17:45:09
 */
@Service
public class CorrectionRulesAndRegulationsServiceImpl extends ServiceImpl<CorrectionRulesAndRegulationsMapper, CorrectionRulesAndRegulations> implements CorrectionRulesAndRegulationsService {

    @Autowired
    private SysFileInfoService sysFileInfoService;

    @Override
    public PageResult<CorrectionRulesAndRegulations> page(CorrectionRulesAndRegulationsParam correctionRulesAndRegulationsParam) {
        QueryWrapper<CorrectionRulesAndRegulations> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionRulesAndRegulationsParam)) {

            // 根据标题 查询
            if (ObjectUtil.isNotEmpty(correctionRulesAndRegulationsParam.getTitle())) {
                queryWrapper.lambda().like(CorrectionRulesAndRegulations::getTitle, correctionRulesAndRegulationsParam.getTitle());
            }
            // 根据发布单位 查询
            if (ObjectUtil.isNotEmpty(correctionRulesAndRegulationsParam.getReleaseDeptName())) {
                queryWrapper.lambda().like(CorrectionRulesAndRegulations::getReleaseDeptName, correctionRulesAndRegulationsParam.getReleaseDeptName());
            }
            // 根据发布部门 查询
            if (ObjectUtil.isNotEmpty(correctionRulesAndRegulationsParam.getDeptName())) {
                queryWrapper.lambda().like(CorrectionRulesAndRegulations::getDeptName, correctionRulesAndRegulationsParam.getDeptName());
            }
            // 根据类型（0_法律法规,1_技术规范,2_工作通知,3_标准规范) 查询
            if (ObjectUtil.isNotEmpty(correctionRulesAndRegulationsParam.getReleaseType())) {
                queryWrapper.lambda().eq(CorrectionRulesAndRegulations::getReleaseType, correctionRulesAndRegulationsParam.getReleaseType());
            }
        }
        queryWrapper.lambda().eq(CorrectionRulesAndRegulations::getDelFlag, 0);
        queryWrapper.orderByDesc("create_time");
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionRulesAndRegulations> list(CorrectionRulesAndRegulationsParam correctionRulesAndRegulationsParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionRulesAndRegulationsParam correctionRulesAndRegulationsParam) {
        CorrectionRulesAndRegulations correctionRulesAndRegulations = new CorrectionRulesAndRegulations();
        BeanUtil.copyProperties(correctionRulesAndRegulationsParam, correctionRulesAndRegulations);
        this.save(correctionRulesAndRegulations);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionRulesAndRegulationsParam correctionRulesAndRegulationsParam) {
        CorrectionRulesAndRegulations correctionRulesAndRegulations = new CorrectionRulesAndRegulations();
        BeanUtil.copyProperties(correctionRulesAndRegulationsParam, correctionRulesAndRegulations);
        correctionRulesAndRegulations.setDelFlag(1);
        this.updateById(correctionRulesAndRegulations);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionRulesAndRegulationsParam correctionRulesAndRegulationsParam) {
        CorrectionRulesAndRegulations correctionRulesAndRegulations = this.queryCorrectionRulesAndRegulations(correctionRulesAndRegulationsParam);
        BeanUtil.copyProperties(correctionRulesAndRegulationsParam, correctionRulesAndRegulations);
        this.updateById(correctionRulesAndRegulations);
    }

    @Override
    public CorrectionRulesAndRegulations detail(CorrectionRulesAndRegulationsParam correctionRulesAndRegulationsParam) {
        return this.queryCorrectionRulesAndRegulations(correctionRulesAndRegulationsParam);
    }

    /**
     * 获取规章制度
     *
     * <AUTHOR>
     * @date 2022-03-10 17:45:09
     */
    private CorrectionRulesAndRegulations queryCorrectionRulesAndRegulations(CorrectionRulesAndRegulationsParam correctionRulesAndRegulationsParam) {
        CorrectionRulesAndRegulations correctionRulesAndRegulations = this.getById(correctionRulesAndRegulationsParam.getId());
        if (ObjectUtil.isNull(correctionRulesAndRegulations)) {
            throw new ServiceException(CorrectionRulesAndRegulationsExceptionEnum.NOT_EXIST);
        }
        //查找附件，返回
        List<SysFileInfoParam> fileInfoList= new ArrayList<SysFileInfoParam>();
        SysFileInfoParam p = null;
        for (String fileId : correctionRulesAndRegulations.getFileId().split(",")) {
            SysFileInfo file = sysFileInfoService.getById(fileId);
            if (null != file) {
                p = new SysFileInfoParam();
                p.setUid(String.valueOf(file.getId()));
                p.setName(file.getFileOriginName());
                p.setUrl(file.getFilePath());
                fileInfoList.add(p);
            }
        }
        correctionRulesAndRegulations.setFileList(fileInfoList);
        return correctionRulesAndRegulations;
    }


}
