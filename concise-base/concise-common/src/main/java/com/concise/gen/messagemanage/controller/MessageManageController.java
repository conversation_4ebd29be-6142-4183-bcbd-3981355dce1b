package com.concise.gen.messagemanage. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.messagemanage. param.MessageManageParam;
import com.concise.gen.messagemanage. service.MessageManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * MessageManage控制器
 *
 * <AUTHOR>
 * @date 2023-01-04 14:10:20
 */
@Api(tags = "MessageManage")
@RestController
public class MessageManageController {

    @Resource
    private MessageManageService messageManageService;

    /**
     * 查询MessageManage
     *
     * <AUTHOR>
     * @date 2023-01-04 14:10:20
     */
    @Permission
    @GetMapping("/messageManage/page")
    @ApiOperation("MessageManage_分页查询")
    @BusinessLog(title = "MessageManage_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(MessageManageParam messageManageParam) {
        return new SuccessResponseData(messageManageService.page(messageManageParam));
    }

    /**
     * 添加MessageManage
     *
     * <AUTHOR>
     * @date 2023-01-04 14:10:20
     */
    @Permission
    @PostMapping("/messageManage/add")
    @ApiOperation("MessageManage_增加")
    @BusinessLog(title = "MessageManage_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(MessageManageParam.add.class) MessageManageParam messageManageParam) {
        messageManageService.add(messageManageParam);
        return new SuccessResponseData();
    }

    /**
     * 删除MessageManage
     *
     * <AUTHOR>
     * @date 2023-01-04 14:10:20
     */
    @Permission
    @PostMapping("/messageManage/delete")
    @ApiOperation("MessageManage_删除")
    @BusinessLog(title = "MessageManage_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(MessageManageParam.delete.class) MessageManageParam messageManageParam) {
        messageManageService.delete(messageManageParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑MessageManage
     *
     * <AUTHOR>
     * @date 2023-01-04 14:10:20
     */
    @Permission
    @PostMapping("/messageManage/edit")
    @ApiOperation("MessageManage_编辑")
    @BusinessLog(title = "MessageManage_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(MessageManageParam.edit.class) MessageManageParam messageManageParam) {
        messageManageService.edit(messageManageParam);
        return new SuccessResponseData();
    }

    /**
     * 查看MessageManage
     *
     * <AUTHOR>
     * @date 2023-01-04 14:10:20
     */
    @Permission
    @GetMapping("/messageManage/detail")
    @ApiOperation("MessageManage_查看")
    @BusinessLog(title = "MessageManage_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(MessageManageParam.detail.class) MessageManageParam messageManageParam) {
        return new SuccessResponseData(messageManageService.detail(messageManageParam));
    }

    /**
     * MessageManage列表
     *
     * <AUTHOR>
     * @date 2023-01-04 14:10:20
     */
    @Permission
    @GetMapping("/messageManage/list")
    @ApiOperation("MessageManage_列表")
    @BusinessLog(title = "MessageManage_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(MessageManageParam messageManageParam) {
        return new SuccessResponseData(messageManageService.list(messageManageParam));
    }

}
