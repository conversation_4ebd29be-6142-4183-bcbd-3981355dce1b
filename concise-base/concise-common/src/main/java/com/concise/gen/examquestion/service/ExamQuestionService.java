package com.concise.gen.examquestion.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.pojo.response.ResponseData;
import com.concise.gen.examquestion.entity.ExamQuestion;
import com.concise.gen.examquestion.param.ExamQuestionParam;
import com.concise.gen.examquestionitem.entity.ExamQuestionItem;

import java.io.File;
import java.util.List;

/**
 * 考试题目service接口
 *
 * <AUTHOR>
 * @date 2023-01-03 15:52:58
 */
public interface ExamQuestionService extends IService<ExamQuestion> {

    /**
     * 查询考试题目
     *
     * <AUTHOR>
     * @date 2023-01-03 15:52:58
     */
    PageResult<ExamQuestion> page(ExamQuestionParam examQuestionParam);

    /**
     * 考试题目列表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:52:58
     */
    List<ExamQuestion> list(ExamQuestionParam examQuestionParam);

    /**
     * 添加考试题目
     *
     * <AUTHOR>
     * @date 2023-01-03 15:52:58
     */
    void add(ExamQuestion examQuestion);

    /**
     * 删除考试题目
     *
     * <AUTHOR>
     * @date 2023-01-03 15:52:58
     */
    void delete(ExamQuestionParam examQuestionParam);

    /**
     * 编辑考试题目
     *
     * <AUTHOR>
     * @date 2023-01-03 15:52:58
     */
    void edit(ExamQuestion examQuestion,String name);

    /**
     * 查看考试题目
     *
     * <AUTHOR>
     * @date 2023-01-03 15:52:58
     */
    ExamQuestion detail(ExamQuestionParam examQuestionParam);

    /**
     * 导入题库
     *
     * @param file
     * @return
     */
    ResponseData importExcel(File file,String name);

    /**
     * 根据条件获取题目树
     * @param examQuestionParam
     * @return
     */
    List<ExamQuestion> questionTree(ExamQuestionParam examQuestionParam);

    /**
     * 获取试题详情
     * @param examQuestionParam
     * @return
     */
    ExamQuestion getDetails(ExamQuestionParam examQuestionParam);

    /**
     * 根据问题构建子集
     * @param examQuestion
     * @param list
     */
    void buildChildren(ExamQuestion examQuestion, List<ExamQuestionItem> list);

    /**
     * 量表管理使用，根据条件查询题目树
     * @return
     */
    List<ExamQuestion> examPaperTree(ExamQuestionParam examQuestionParam);
}
