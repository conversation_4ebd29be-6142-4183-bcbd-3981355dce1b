package com.concise.gen.portraitImage.correctionassesperson.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.portraitImage.correctionassesperson.entity.CorrectionAssesPerson;
import com.concise.gen.portraitImage.correctionassesperson.enums.CorrectionAssesPersonExceptionEnum;
import com.concise.gen.portraitImage.correctionassesperson.mapper.CorrectionAssesPersonMapper;
import com.concise.gen.portraitImage.correctionassesperson.param.CorrectionAssesPersonParam;
import com.concise.gen.portraitImage.correctionassesperson.service.CorrectionAssesPersonService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * 评估管理--评估人员信息service接口实现类
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:42
 */
@Service
public class CorrectionAssesPersonServiceImpl extends ServiceImpl<CorrectionAssesPersonMapper, CorrectionAssesPerson> implements CorrectionAssesPersonService {

    @Override
    public PageResult<CorrectionAssesPerson> page(CorrectionAssesPersonParam correctionAssesPersonParam, Set<String> org) {
        String status = "";
        if (ObjectUtil.isNotEmpty(correctionAssesPersonParam.getStatus())) {
            status = correctionAssesPersonParam.getStatus() + "";
        }
        return new PageResult<>(this.baseMapper.page(PageFactory.defaultPage(), status, correctionAssesPersonParam.getBaseId(), org,
                correctionAssesPersonParam.getJzjb(), correctionAssesPersonParam.getSqjzryName()));
    }

    @Override
    public List<CorrectionAssesPerson> list(CorrectionAssesPersonParam correctionAssesPersonParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionAssesPersonParam correctionAssesPersonParam) {
        CorrectionAssesPerson correctionAssesPerson = new CorrectionAssesPerson();
        BeanUtil.copyProperties(correctionAssesPersonParam, correctionAssesPerson);
        this.save(correctionAssesPerson);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionAssesPersonParam correctionAssesPersonParam) {
        this.removeById(correctionAssesPersonParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionAssesPersonParam correctionAssesPersonParam) {
        CorrectionAssesPerson correctionAssesPerson = this.queryCorrectionAssesPerson(correctionAssesPersonParam);
        BeanUtil.copyProperties(correctionAssesPersonParam, correctionAssesPerson);
        this.updateById(correctionAssesPerson);
    }

    @Override
    public CorrectionAssesPerson detail(CorrectionAssesPersonParam correctionAssesPersonParam) {
        return this.queryCorrectionAssesPerson(correctionAssesPersonParam);
    }

    /**
     * 获取评估管理--评估人员信息
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:42
     */
    private CorrectionAssesPerson queryCorrectionAssesPerson(CorrectionAssesPersonParam correctionAssesPersonParam) {
        CorrectionAssesPerson correctionAssesPerson = this.getById(correctionAssesPersonParam.getId());
        if (ObjectUtil.isNull(correctionAssesPerson)) {
            throw new ServiceException(CorrectionAssesPersonExceptionEnum.NOT_EXIST);
        }
        return correctionAssesPerson;
    }
}
