package com.concise.gen.indexmanageportrayal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.indexmanageportrayal.entity.IndexManagePortrayal;
import com.concise.gen.indexmanageportrayal.param.IndexManagePortrayalParam;

import java.util.List;

/**
 * 指标管理信息表service接口
 *
 * <AUTHOR>
 * @date 2022-05-12 10:02:19
 */
public interface IndexManagePortrayalService extends IService<IndexManagePortrayal> {

    /**
     * 查询指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    PageResult<IndexManagePortrayal> page(IndexManagePortrayalParam indexManagePortrayalParam);

    /**
     * 指标管理信息表列表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    List<IndexManagePortrayal> list(IndexManagePortrayalParam indexManagePortrayalParam);

    /**
     * 添加指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    void add(IndexManagePortrayalParam indexManagePortrayalParam);

    /**
     * 删除指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    void delete(IndexManagePortrayalParam indexManagePortrayalParam);

    /**
     * 编辑指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    void edit(IndexManagePortrayalParam indexManagePortrayalParam);

    /**
     * 查看指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    IndexManagePortrayal detail(IndexManagePortrayalParam indexManagePortrayalParam);
}
