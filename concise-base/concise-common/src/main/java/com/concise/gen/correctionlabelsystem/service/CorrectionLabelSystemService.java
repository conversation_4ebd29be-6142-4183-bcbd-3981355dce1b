package com.concise.gen.correctionlabelsystem.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionlabelsystem.entity.CorrectionLabelSystem;
import com.concise.gen.correctionlabelsystem.param.CorrectionLabelSystemParam;
import java.util.List;

/**
 * 标签所属应用service接口
 *
 * <AUTHOR>
 * @date 2022-08-22 09:04:42
 */
public interface CorrectionLabelSystemService extends IService<CorrectionLabelSystem> {

    /**
     * 查询标签所属应用
     *
     * <AUTHOR>
     * @date 2022-08-22 09:04:42
     */
    PageResult<CorrectionLabelSystem> page(CorrectionLabelSystemParam correctionLabelSystemParam);

    /**
     * 标签所属应用列表
     *
     * <AUTHOR>
     * @date 2022-08-22 09:04:42
     */
    List<CorrectionLabelSystem> list(CorrectionLabelSystemParam correctionLabelSystemParam);

    /**
     * 添加标签所属应用
     *
     * <AUTHOR>
     * @date 2022-08-22 09:04:42
     */
    void add(CorrectionLabelSystemParam correctionLabelSystemParam);

    /**
     * 删除标签所属应用
     *
     * <AUTHOR>
     * @date 2022-08-22 09:04:42
     */
    void delete(CorrectionLabelSystemParam correctionLabelSystemParam);

    /**
     * 编辑标签所属应用
     *
     * <AUTHOR>
     * @date 2022-08-22 09:04:42
     */
    void edit(CorrectionLabelSystemParam correctionLabelSystemParam);

    /**
     * 查看标签所属应用
     *
     * <AUTHOR>
     * @date 2022-08-22 09:04:42
     */
     CorrectionLabelSystem detail(CorrectionLabelSystemParam correctionLabelSystemParam);
}
