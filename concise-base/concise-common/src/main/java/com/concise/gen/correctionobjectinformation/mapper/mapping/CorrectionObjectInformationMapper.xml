<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.correctionobjectinformation.mapper.CorrectionObjectInformationMapper">

    <select id="getCorrection"
            resultType="com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation">
        SELECT id,
               xm,
               sfzh,
               jzjg,
               jzjg_name,
               sqjzjsrq
        FROM correction_object_information
        where zhuangtai = 200
    </select>

    <select id="getBySfzh" resultType="com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation">
        SELECT id,
               xm,
               sfzh as sfzh_mw,
               jzjg,
               jzjg_name
        FROM correction_object_information
        where zhuangtai = 200
          and sfzh = #{sfzh}
    </select>
    <select id="getCorrectionMultifield"
            resultType="com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation">
        SELECT id,
               xm,
               sfzh,
               jzjg,
               jzjg_name,
               sqjzjsrq
        FROM correction_object_information
        where zhuangtai = 200

    </select>
    <select id="getCorrectionMultifieldNoEncryption"
            resultType="com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformationNoEncryption">
        SELECT id,
               xm,
               xb,
               xb_name,
               sfcn,
               sfcn_name,
               sfzh,
               jzjg,
               jzjg_name,
               sqjzksrq,
               sqjzjsrq,
               whcd,
               whcd_name,
               hyzk_name,
               jyjxqk,
               jyjxqk_name,
               sfyqk,
               sflf,
               jtzm,
               jtzm_name,
               jzlb,
               jzlb_name,
               gdjzdszds_name,
               gdjzdszxq_name,
               gdjzdmx,
               sfwd,
               sfws,
               sfyss,
               grlxdh,
               sfcn,
               sfcn_name,
               mz,
               mz_name,
               zyfzss
        FROM correction_object_information
        where zhuangtai = 200

    </select>
    <select id="selectByIdNoEncryption"
            resultType="com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformationNoEncryption">
        select * from   sqjzzxsjk0.correction_object_information where id=#{id}
    </select>

    <select id="findCorrectionObjectInformation"
            resultType="com.concise.gen.correctionlabelcorrpsn.entity.CorrectionLabelCorrpsn">
        SELECT
        id AS sqjzry_id,
        sfzh AS sfzh
        FROM
        correction_object_information
        WHERE
        ${fieldName} IN
        <foreach collection="dictNames" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="findCorrectionBan"
            resultType="com.concise.gen.correctionlabelcorrpsn.entity.CorrectionLabelCorrpsn">
        SELECT
        correction_object_information.id AS sqjzry_id,
        correction_object_information.sfzh AS sfzh
        FROM
        correction_ban
        LEFT JOIN correction_object_information ON correction_ban.pid = correction_object_information.id
        WHERE
        correction_ban.${fieldName} IN
        <foreach collection="dictNames" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="findCorrectionCrime"
            resultType="com.concise.gen.correctionlabelcorrpsn.entity.CorrectionLabelCorrpsn">
        SELECT
        correction_object_information.id AS sqjzry_id,
        correction_object_information.sfzh AS sfzh
        FROM
        correction_criminal_record
        LEFT JOIN correction_object_information ON correction_criminal_record.pid = correction_object_information.id
        WHERE
        correction_criminal_record.${fieldName} IN
        <foreach collection="dictNames" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="findCorrectionRisk"
            resultType="com.concise.gen.correctionlabelcorrpsn.entity.CorrectionLabelCorrpsn">
        SELECT
        correction_risk_level.sqjzry_id AS sqjzry_id,
        sfzh AS sfzh
        FROM
        correction_risk_level
        WHERE
        ${fieldName} IN
        <foreach collection="dictNames" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="findExam" resultType="com.concise.gen.correctionlabelcorrpsn.entity.CorrectionLabelCorrpsn">
        SELECT
            correction_asses_person_dtl.sqjzry_id AS sqjzry_id,
            correction_object_information.sfzh as sfzh
        FROM
            correction_asses_answer
                LEFT JOIN correction_asses_person_dtl ON correction_asses_answer.dtl_id = correction_asses_person_dtl.id
                LEFT JOIN correction_object_information ON correction_asses_person_dtl.sqjzry_id = correction_object_information.id
        WHERE
            correction_asses_answer.answer_ids IN
        <foreach collection="dictNames" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="moodCode" resultType="com.concise.gen.correctionobjectinformation.entity.ScreenModel">
        SELECT
        COUNT(correct_mood_code.mood_code) AS amount,
        mood_code as title
        FROM
        correct_mood_code
        INNER JOIN ( SELECT MAX( update_time ) AS update_time, correct_mood_code.third_id AS third_id FROM correct_mood_code GROUP BY third_id ) child ON correct_mood_code.third_id = child.third_id
        AND correct_mood_code.update_time = child.update_time
        where correct_mood_code.third_id in
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY correct_mood_code.mood_code
        ORDER BY correct_mood_code.update_time DESC
    </select>
    <select id="getJiejiaoriqiById" resultType="java.util.Date">
        SELECT
            zhongzhiriqi
        FROM
            correction_terminate
        WHERE
            id=#{id}
    </select>

</mapper>
