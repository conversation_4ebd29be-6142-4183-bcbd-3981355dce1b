package com.concise.gen.exampaperquestion. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.exampaperquestion. param.ExamPaperQuestionParam;
import com.concise.gen.exampaperquestion. service.ExamPaperQuestionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 量表题目内容控制器
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:05
 */
@Api(tags = "量表题目内容")
@RestController
public class ExamPaperQuestionController {

    @Resource
    private ExamPaperQuestionService examPaperQuestionService;

    /**
     * 查询量表题目内容
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:05
     */
    @Permission
    @GetMapping("/examPaperQuestion/page")
    @ApiOperation("量表题目内容_分页查询")
    @BusinessLog(title = "量表题目内容_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(ExamPaperQuestionParam examPaperQuestionParam) {
        return new SuccessResponseData(examPaperQuestionService.page(examPaperQuestionParam));
    }

    /**
     * 添加量表题目内容
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:05
     */
    @Permission
    @PostMapping("/examPaperQuestion/add")
    @ApiOperation("量表题目内容_增加")
    @BusinessLog(title = "量表题目内容_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(ExamPaperQuestionParam.add.class) ExamPaperQuestionParam examPaperQuestionParam) {
        examPaperQuestionService.add(examPaperQuestionParam);
        return new SuccessResponseData();
    }

    /**
     * 删除量表题目内容
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:05
     */
    @Permission
    @PostMapping("/examPaperQuestion/delete")
    @ApiOperation("量表题目内容_删除")
    @BusinessLog(title = "量表题目内容_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(ExamPaperQuestionParam.delete.class) ExamPaperQuestionParam examPaperQuestionParam) {
        examPaperQuestionService.delete(examPaperQuestionParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑量表题目内容
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:05
     */
    @Permission
    @PostMapping("/examPaperQuestion/edit")
    @ApiOperation("量表题目内容_编辑")
    @BusinessLog(title = "量表题目内容_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(ExamPaperQuestionParam.edit.class) ExamPaperQuestionParam examPaperQuestionParam) {
        examPaperQuestionService.edit(examPaperQuestionParam);
        return new SuccessResponseData();
    }

    /**
     * 查看量表题目内容
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:05
     */
    @Permission
    @GetMapping("/examPaperQuestion/detail")
    @ApiOperation("量表题目内容_查看")
    @BusinessLog(title = "量表题目内容_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(ExamPaperQuestionParam.detail.class) ExamPaperQuestionParam examPaperQuestionParam) {
        return new SuccessResponseData(examPaperQuestionService.detail(examPaperQuestionParam));
    }

    /**
     * 量表题目内容列表
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:05
     */
    @Permission
    @GetMapping("/examPaperQuestion/list")
    @ApiOperation("量表题目内容_列表")
    @BusinessLog(title = "量表题目内容_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(ExamPaperQuestionParam examPaperQuestionParam) {
        return new SuccessResponseData(examPaperQuestionService.list(examPaperQuestionParam));
    }

}
