package com.concise.gen.correctionestimateportrayal.entity.vo;


import lombok.Data;

import java.math.BigDecimal;

@Data
public class PortrayalGjqsVO {

    /**
     * 月份
     */
    private String monthValue;

    /**
     * 心理画像得分
     */
    private BigDecimal xlhxScore;

    /**
     * 知法画像得分
     */
    private BigDecimal zfhxScore;

    /**
     * 就业画像得分
     */
    private BigDecimal jyhxScore;

    /**
     * 家庭画像得分
     */
    private BigDecimal jthxScore;

    /**
     * 信用画像得分
     */
    private BigDecimal xyhxScore;

    /**
     * 个人基本画像得分
     */
    private BigDecimal jbhxScore;
}
