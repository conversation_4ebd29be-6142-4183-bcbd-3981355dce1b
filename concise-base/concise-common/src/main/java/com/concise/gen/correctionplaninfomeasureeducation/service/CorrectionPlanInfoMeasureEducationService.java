package com.concise.gen.correctionplaninfomeasureeducation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionplaninfomeasureeducation.entity.CorrectionPlanInfoMeasureEducation;
import com.concise.gen.correctionplaninfomeasureeducation.param.CorrectionPlanInfoMeasureEducationParam;
import java.util.List;

/**
 * 矫正方案2.0监管措施_教育帮扶每月课件分类学习记录service接口
 *
 * <AUTHOR>
 * @date 2024-11-21 16:55:24
 */
public interface CorrectionPlanInfoMeasureEducationService extends IService<CorrectionPlanInfoMeasureEducation> {

    /**
     * 查询矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
     *
     * <AUTHOR>
     * @date 2024-11-21 16:55:24
     */
    PageResult<CorrectionPlanInfoMeasureEducation> page(CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam);

    /**
     * 矫正方案2.0监管措施_教育帮扶每月课件分类学习记录列表
     *
     * <AUTHOR>
     * @date 2024-11-21 16:55:24
     */
    List<CorrectionPlanInfoMeasureEducation> list(CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam);

    /**
     * 添加矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
     *
     * <AUTHOR>
     * @date 2024-11-21 16:55:24
     */
    void add(CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam);

    /**
     * 删除矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
     *
     * <AUTHOR>
     * @date 2024-11-21 16:55:24
     */
    void delete(CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam);

    /**
     * 编辑矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
     *
     * <AUTHOR>
     * @date 2024-11-21 16:55:24
     */
    void edit(CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam);

    /**
     * 查看矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
     *
     * <AUTHOR>
     * @date 2024-11-21 16:55:24
     */
    CorrectionPlanInfoMeasureEducation detail(CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam);
}
