package com.concise.gen.correctionportrayalassignment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionportrayalassignment.entity.CorrectionPortrayalAssignment;
import com.concise.gen.correctionportrayalassignment.param.CorrectionPortrayalAssignmentParam;
import java.util.List;

/**
 * 画像首页待处理任务service接口
 *
 * <AUTHOR>
 * @date 2023-01-09 09:14:33
 */
public interface CorrectionPortrayalAssignmentService extends IService<CorrectionPortrayalAssignment> {

    /**
     * 查询画像首页待处理任务
     *
     * <AUTHOR>
     * @date 2023-01-09 09:14:33
     */
    PageResult<CorrectionPortrayalAssignment> page(CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam);

    /**
     * 画像首页待处理任务列表
     *
     * <AUTHOR>
     * @date 2023-01-09 09:14:33
     */
    List<CorrectionPortrayalAssignment> list(CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam);

    /**
     * 添加画像首页待处理任务
     *
     * <AUTHOR>
     * @date 2023-01-09 09:14:33
     */
    void add(CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam);

    /**
     * 删除画像首页待处理任务
     *
     * <AUTHOR>
     * @date 2023-01-09 09:14:33
     */
    void delete(CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam);

    /**
     * 编辑画像首页待处理任务
     *
     * <AUTHOR>
     * @date 2023-01-09 09:14:33
     */
    void edit(CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam);

    /**
     * 查看画像首页待处理任务
     *
     * <AUTHOR>
     * @date 2023-01-09 09:14:33
     */
     CorrectionPortrayalAssignment detail(CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam);
}
