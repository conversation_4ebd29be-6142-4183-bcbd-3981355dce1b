package com.concise.gen.correctionestimateportrayaldetail.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimateportrayaldetail.entity.CorrectionEstimatePortrayalDetail;
import com.concise.gen.correctionestimateportrayaldetail.enums.CorrectionEstimatePortrayalDetailExceptionEnum;
import com.concise.gen.correctionestimateportrayaldetail.mapper.CorrectionEstimatePortrayalDetailMapper;
import com.concise.gen.correctionestimateportrayaldetail.param.CorrectionEstimatePortrayalDetailParam;
import com.concise.gen.correctionestimateportrayaldetail.service.CorrectionEstimatePortrayalDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 画像明细service接口实现类
 *
 * <AUTHOR>
 * @date 2022-11-30 19:58:01
 */
@Service
public class CorrectionEstimatePortrayalDetailServiceImpl extends ServiceImpl<CorrectionEstimatePortrayalDetailMapper, CorrectionEstimatePortrayalDetail> implements CorrectionEstimatePortrayalDetailService {

    @Override
    public PageResult<CorrectionEstimatePortrayalDetail> page(CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam) {
        QueryWrapper<CorrectionEstimatePortrayalDetail> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionEstimatePortrayalDetailParam)) {

            // 根据画像类型 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalDetailParam.getType())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalDetail::getType, correctionEstimatePortrayalDetailParam.getType());
            }
            // 根据画像id 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalDetailParam.getPortrayalId())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalDetail::getPortrayalId, correctionEstimatePortrayalDetailParam.getPortrayalId());
            }
            // 根据画像描述 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalDetailParam.getHxms())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalDetail::getHxms, correctionEstimatePortrayalDetailParam.getHxms());
            }
            // 根据轨迹趋势 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalDetailParam.getGjqs())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalDetail::getGjqs, correctionEstimatePortrayalDetailParam.getGjqs());
            }
            // 根据轨迹风险 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalDetailParam.getXlhxGjfx())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalDetail::getXlhxGjfx, correctionEstimatePortrayalDetailParam.getXlhxGjfx());
            }
            // 根据改善建议 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalDetailParam.getJy())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalDetail::getJy, correctionEstimatePortrayalDetailParam.getJy());
            }
            // 根据扣分明细 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalDetailParam.getKfmx())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalDetail::getKfmx, correctionEstimatePortrayalDetailParam.getKfmx());
            }
            // 根据权重总分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalDetailParam.getZf())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalDetail::getZf, correctionEstimatePortrayalDetailParam.getZf());
            }
            // 根据扣分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalDetailParam.getKf())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalDetail::getKf, correctionEstimatePortrayalDetailParam.getKf());
            }
            // 根据得分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalDetailParam.getDf())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalDetail::getDf, correctionEstimatePortrayalDetailParam.getDf());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionEstimatePortrayalDetail> list(CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam) {
        CorrectionEstimatePortrayalDetail correctionEstimatePortrayalDetail = new CorrectionEstimatePortrayalDetail();
        BeanUtil.copyProperties(correctionEstimatePortrayalDetailParam, correctionEstimatePortrayalDetail);
        this.save(correctionEstimatePortrayalDetail);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam) {
        this.removeById(correctionEstimatePortrayalDetailParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam) {
        CorrectionEstimatePortrayalDetail correctionEstimatePortrayalDetail = this.queryCorrectionEstimatePortrayalDetail(correctionEstimatePortrayalDetailParam);
        BeanUtil.copyProperties(correctionEstimatePortrayalDetailParam, correctionEstimatePortrayalDetail);
        this.updateById(correctionEstimatePortrayalDetail);
    }

    @Override
    public CorrectionEstimatePortrayalDetail detail(CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam) {
        return this.queryCorrectionEstimatePortrayalDetail(correctionEstimatePortrayalDetailParam);
    }

    /**
     * 获取画像明细
     *
     * <AUTHOR>
     * @date 2022-11-30 19:58:01
     */
    private CorrectionEstimatePortrayalDetail queryCorrectionEstimatePortrayalDetail(CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam) {
        //CorrectionEstimatePortrayalDetail correctionEstimatePortrayalDetail = this.getById(correctionEstimatePortrayalDetailParam.getId());
        LambdaQueryWrapper<CorrectionEstimatePortrayalDetail> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CorrectionEstimatePortrayalDetail::getPortrayalId,correctionEstimatePortrayalDetailParam.getPortrayalId());
        lambdaQueryWrapper.eq(CorrectionEstimatePortrayalDetail::getType,correctionEstimatePortrayalDetailParam.getType());
        CorrectionEstimatePortrayalDetail  correctionEstimatePortrayalDetail = this.getOne(lambdaQueryWrapper);
        if (ObjectUtil.isNull(correctionEstimatePortrayalDetail)) {
            throw new ServiceException(CorrectionEstimatePortrayalDetailExceptionEnum.NOT_EXIST);
        }
        return correctionEstimatePortrayalDetail;
    }

    @Override
    public List<CorrectionEstimatePortrayalDetail> listByPortrayalId(String portrayalId, Integer type) {
        LambdaQueryWrapper<CorrectionEstimatePortrayalDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CorrectionEstimatePortrayalDetail::getPortrayalId, portrayalId);
        lambdaQueryWrapper.eq(type != null, CorrectionEstimatePortrayalDetail::getType, type);
        lambdaQueryWrapper.orderByAsc(CorrectionEstimatePortrayalDetail::getType);
        return list(lambdaQueryWrapper);
    }
}
