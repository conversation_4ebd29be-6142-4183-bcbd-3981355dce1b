package com.concise.gen.examquestionuser.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 用户答题表
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:27
 */
@Data
@TableName("exam_question_user")
public class ExamQuestionUser {

    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     *
     */
    private String userId;

    /**
     *
     */
    private String manageId;

    /**
     *
     */
    private String questionId;

    /**
     *
     */
    private String answer;

    /**
     * 分数
     */
    private Double score;

}
