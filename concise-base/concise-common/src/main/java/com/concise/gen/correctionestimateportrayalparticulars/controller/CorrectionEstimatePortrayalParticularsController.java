package com.concise.gen.correctionestimateportrayalparticulars.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.correctionestimateportrayalparticulars.param.CorrectionEstimatePortrayalParticularsParam;
import com.concise.gen.correctionestimateportrayalparticulars.service.CorrectionEstimatePortrayalParticularsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 心理画像明细（新）控制器
 *
 * <AUTHOR>
 * @date 2023-01-14 10:38:47
 */
@Api(tags = "心理画像明细（新）")
@RestController
public class CorrectionEstimatePortrayalParticularsController {

    @Resource
    private CorrectionEstimatePortrayalParticularsService correctionEstimatePortrayalParticularsService;

    /**
     * 查询心理画像明细（新）
     *
     * <AUTHOR>
     * @date 2023-01-14 10:38:47
     */
    @Permission
    @GetMapping("/correctionEstimatePortrayalParticulars/page")
    @ApiOperation("心理画像明细（新）_分页查询")
    @BusinessLog(title = "心理画像明细（新）_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam) {
        return new SuccessResponseData(correctionEstimatePortrayalParticularsService.page(correctionEstimatePortrayalParticularsParam));
    }

    /**
     * 添加心理画像明细（新）
     *
     * <AUTHOR>
     * @date 2023-01-14 10:38:47
     */
    @Permission
    @PostMapping("/correctionEstimatePortrayalParticulars/add")
    @ApiOperation("心理画像明细（新）_增加")
    @BusinessLog(title = "心理画像明细（新）_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(CorrectionEstimatePortrayalParticularsParam.add.class) CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam) {
        correctionEstimatePortrayalParticularsService.add(correctionEstimatePortrayalParticularsParam);
        return new SuccessResponseData();
    }

    /**
     * 删除心理画像明细（新）
     *
     * <AUTHOR>
     * @date 2023-01-14 10:38:47
     */
    @Permission
    @PostMapping("/correctionEstimatePortrayalParticulars/delete")
    @ApiOperation("心理画像明细（新）_删除")
    @BusinessLog(title = "心理画像明细（新）_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(CorrectionEstimatePortrayalParticularsParam.delete.class) CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam) {
        correctionEstimatePortrayalParticularsService.delete(correctionEstimatePortrayalParticularsParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑心理画像明细（新）
     *
     * <AUTHOR>
     * @date 2023-01-14 10:38:47
     */
    @Permission
    @PostMapping("/correctionEstimatePortrayalParticulars/edit")
    @ApiOperation("心理画像明细（新）_编辑")
    @BusinessLog(title = "心理画像明细（新）_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(CorrectionEstimatePortrayalParticularsParam.edit.class) CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam) {
        correctionEstimatePortrayalParticularsService.edit(correctionEstimatePortrayalParticularsParam);
        return new SuccessResponseData();
    }

    /**
     * 查看心理画像明细（新）
     *
     * <AUTHOR>
     * @date 2023-01-14 10:38:47
     */
    @Permission
    @GetMapping("/correctionEstimatePortrayalParticulars/detail")
    @ApiOperation("心理画像明细（新）_查看")
    @BusinessLog(title = "心理画像明细（新）_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam) {
        return new SuccessResponseData(correctionEstimatePortrayalParticularsService.detail(correctionEstimatePortrayalParticularsParam));
    }

    /**
     * 心理画像明细（新）列表
     *
     * <AUTHOR>
     * @date 2023-01-14 10:38:47
     */
    @Permission
    @GetMapping("/correctionEstimatePortrayalParticulars/list")
    @ApiOperation("心理画像明细（新）_列表")
    @BusinessLog(title = "心理画像明细（新）_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam) {
        return new SuccessResponseData(correctionEstimatePortrayalParticularsService.list(correctionEstimatePortrayalParticularsParam));
    }

}
