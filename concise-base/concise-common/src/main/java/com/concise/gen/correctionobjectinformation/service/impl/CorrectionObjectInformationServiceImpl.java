package com.concise.gen.correctionobjectinformation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.consts.LabelConstant;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.DateTimeUtil;
import com.concise.gen.correctioncorrectplan.entity.CorrectionCorrectPlan;
import com.concise.gen.correctioncorrectplan.service.CorrectionCorrectPlanService;
import com.concise.gen.correctionlabelcorrpsn.service.CorrectionLabelCorrpsnService;
import com.concise.gen.correctionlabeldictionary.mapper.CorrectionLabelDictionaryMapper;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.correctionobjectinformation.entity.ScreenModel;
import com.concise.gen.correctionobjectinformation.enums.CorrectionObjectInformationExceptionEnum;
import com.concise.gen.correctionobjectinformation.mapper.CorrectionObjectInformationMapper;
import com.concise.gen.correctionobjectinformation.param.CorrectionObjectInformationParam;
import com.concise.gen.correctionobjectinformation.service.CorrectionObjectInformationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 矫正对象信息表-吴兴service接口实现类
 *
 * <AUTHOR>
 * @date 2021-12-07 14:19:57
 */
@Service
public class CorrectionObjectInformationServiceImpl extends ServiceImpl<CorrectionObjectInformationMapper, CorrectionObjectInformation> implements CorrectionObjectInformationService {

    @Resource
    private CorrectionCorrectPlanService correctionCorrectPlanService;

    @Resource
    private CorrectionLabelCorrpsnService correctionLabelCorrpsnService;

    @Resource
    private CorrectionLabelDictionaryMapper correctionLabelDictionaryMapper;

    @Override
    @DS("slave")
    public PageResult<CorrectionObjectInformation> page(CorrectionObjectInformationParam correctionObjectInformationParam, Set<String> org) {
        QueryWrapper<CorrectionObjectInformation> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionObjectInformationParam)) {

            // 根据矫正机构id 查询
            queryWrapper.lambda().in(CorrectionObjectInformation::getJzjg, org);

            // 根据矫正类别 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getJzlb())) {
                queryWrapper.lambda().eq(CorrectionObjectInformation::getJzlb, correctionObjectInformationParam.getJzlb());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getXm())) {
                queryWrapper.lambda().like(CorrectionObjectInformation::getXm, correctionObjectInformationParam.getXm());
            }
            // 根据状态 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getZhuangtai())) {
                queryWrapper.lambda().eq(CorrectionObjectInformation::getZhuangtai, correctionObjectInformationParam.getZhuangtai());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getRujiaoriqi_begin())) {
                queryWrapper.lambda().ge(CorrectionObjectInformation::getRujiaoriqi, correctionObjectInformationParam.getRujiaoriqi_begin());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getRujiaoriqi_end())) {
                queryWrapper.lambda().le(CorrectionObjectInformation::getRujiaoriqi, correctionObjectInformationParam.getRujiaoriqi_end());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSqjzryIds())) {
                queryWrapper.lambda().in(CorrectionObjectInformation::getId, correctionObjectInformationParam.getSqjzryIds());
            }
            queryWrapper.orderByDesc("rujiaoriqi");

            if (0 == correctionObjectInformationParam.getTag()) {
                // 查在矫
                queryWrapper.lambda().eq(CorrectionObjectInformation::getZhuangtai, "200");
            } else {
                // 查在矫以外的
                queryWrapper.lambda().ne(CorrectionObjectInformation::getZhuangtai, "200");
            }

        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    @DS("slave")
    public List<CorrectionObjectInformation> list(CorrectionObjectInformationParam correctionObjectInformationParam, Set<String> org) {
        QueryWrapper<CorrectionObjectInformation> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionObjectInformationParam)) {

            // 根据矫正机构id 查询
            queryWrapper.lambda().in(CorrectionObjectInformation::getJzjg, org);

            // 根据矫正类别 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getJzlb())) {
                queryWrapper.lambda().eq(CorrectionObjectInformation::getJzlb, correctionObjectInformationParam.getJzlb());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getXm())) {
                queryWrapper.lambda().like(CorrectionObjectInformation::getXm, correctionObjectInformationParam.getXm());
            }
            // 根据状态 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getZhuangtai())) {
                queryWrapper.lambda().eq(CorrectionObjectInformation::getZhuangtai, correctionObjectInformationParam.getZhuangtai());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getRujiaoriqi_begin())) {
                queryWrapper.lambda().ge(CorrectionObjectInformation::getRujiaoriqi, correctionObjectInformationParam.getRujiaoriqi_begin());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getRujiaoriqi_end())) {
                queryWrapper.lambda().le(CorrectionObjectInformation::getRujiaoriqi, correctionObjectInformationParam.getRujiaoriqi_end());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSqjzryIds())) {
                queryWrapper.lambda().in(CorrectionObjectInformation::getId, correctionObjectInformationParam.getSqjzryIds());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getLastModifiedTime())) {
                queryWrapper.lambda().ge(CorrectionObjectInformation::getLastModifiedTime, correctionObjectInformationParam.getLastModifiedTime());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSqjzjsrq_begin())) {
                queryWrapper.lambda().ge(CorrectionObjectInformation::getSqjzjsrq, correctionObjectInformationParam.getSqjzjsrq_begin());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSqjzjsrq_end())) {
                queryWrapper.lambda().le(CorrectionObjectInformation::getSqjzjsrq, correctionObjectInformationParam.getSqjzjsrq_end());
            }
            queryWrapper.orderByDesc("rujiaoriqi");

            if (0 == correctionObjectInformationParam.getTag()) {
                // 查在矫
                queryWrapper.lambda().ge(CorrectionObjectInformation::getZhuangtai, "200");
            } else {
                // 查在矫以外的
                queryWrapper.lambda().ne(CorrectionObjectInformation::getZhuangtai, "200");
            }
        }
        return this.list(queryWrapper);
    }

    @Override
    public void add(CorrectionObjectInformationParam correctionObjectInformationParam) {
        CorrectionObjectInformation correctionObjectInformation = new CorrectionObjectInformation();
        BeanUtil.copyProperties(correctionObjectInformationParam, correctionObjectInformation);
        this.save(correctionObjectInformation);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionObjectInformationParam correctionObjectInformationParam) {
        this.removeById(correctionObjectInformationParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionObjectInformationParam correctionObjectInformationParam) {
        CorrectionObjectInformation correctionObjectInformation = this.queryCorrectionObjectInformation(correctionObjectInformationParam);
        BeanUtil.copyProperties(correctionObjectInformationParam, correctionObjectInformation);
        this.updateById(correctionObjectInformation);
    }

    @Override
    @DS("slave")
    public CorrectionObjectInformation detail(CorrectionObjectInformationParam correctionObjectInformationParam) {
        return this.queryCorrectionObjectInformation(correctionObjectInformationParam);
    }

    @Override
    @DS("slave")
    public void release(String id) {
        CorrectionObjectInformation correctionObjectInformation = this.getById(id);
        if (ObjectUtil.isNotEmpty(correctionObjectInformation)) {
            correctionObjectInformation.setZhuangtai("1");
            this.updateById(correctionObjectInformation);
        }

    }

    /**
     * 获取矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-12-07 14:19:57
     */
    @DS("slave")
    private CorrectionObjectInformation queryCorrectionObjectInformation(CorrectionObjectInformationParam correctionObjectInformationParam) {
        CorrectionObjectInformation correctionObjectInformation = this.getById(correctionObjectInformationParam.getId());
        if (ObjectUtil.isNull(correctionObjectInformation)) {
            throw new ServiceException(CorrectionObjectInformationExceptionEnum.NOT_EXIST);
        }
        return correctionObjectInformation;
    }

    /**
     * 查询前80分钟有更新的人员，绑定标签并保存至矫正方案表
     * （因为是多数据源故未加事务，后续看实际运行再考虑矫正人员的存储问题）
     */
    @Override
    public void initToCorrectPlan(Set<String> org) {
        // 查询最近80分钟有更新且入矫时间是最近10天的矫正对象集合
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        CorrectionObjectInformationParam cp = new CorrectionObjectInformationParam();
        cp.setZhuangtai("200");
        cp.setRujiaoriqi_begin(sf.format(DateTimeUtil.getDay(-10)));
        cp.setLastModifiedTime(DateTimeUtil.beforeMinuteNowDate(-150));
        List<CorrectionObjectInformation> list = this.list(cp, org);
        // 矫正期别
        int tag;
        for (CorrectionObjectInformation cf : list) {
            try {
                tag = getCorrectPhase(cf.getRujiaoriqi(), cf.getSqjzjsrq());
                // 绑定标签
                //根据字典值绑定
                Set<String> dictionaryCode = new HashSet<>();
                dictionaryCode.add("xb" + cf.getXb());
                dictionaryCode.add("jzlb" + cf.getJzlb());
                dictionaryCode.add("hyzk" + cf.getHyzk());
                dictionaryCode.add("whcd" + cf.getWhcd());
                dictionaryCode.add("jzjb" + cf.getJzjb());
                dictionaryCode.add("zm" + cf.getJtzm());
                List<String> labelIds = correctionLabelDictionaryMapper.getLabelIds(dictionaryCode);
                //绑定犯罪时年龄标签
                String ageLabelId = returnLabelIdByDate(DateUtil.format(cf.getCsrq(), "yyyy-MM-dd"), cf.getYpxqkssj());
                if (StringUtils.isNotBlank(ageLabelId)) {
                    labelIds.add(ageLabelId);
                }
                // 添加矫正阶段标签
                String lId = LabelConstant.PHASE.get(tag);
                if (ObjectUtil.isNotEmpty(lId)) {
                    labelIds.add(lId);
                }
                if (labelIds.size() > 0) {
                    correctionLabelCorrpsnService.saveInfo(cf.getId(), labelIds, cf.getSfzh());
                }
                // 未生成方案的且不是矫正末期的才生成方案， 反之则只更新标签
                if (tag != 3 && correctionCorrectPlanService.getNum(cf.getId()) < 1) {
                    // 插入矫正方案表
                    CorrectionCorrectPlan plan = new CorrectionCorrectPlan();
                    plan.setSqjzryId(cf.getId());
                    plan.setSqjzryName(cf.getXm());
                    plan.setSfzh(cf.getSfzh());
                    plan.setJzjg(cf.getJzjg());
                    plan.setJzjgName(cf.getJzjgName());
                    plan.setCriminalBehavior(cf.getZyfzss());
                    // 生成矫正方案措施
                    plan.setCorrectPlan(correctionCorrectPlanService.createPlan(cf.getId(), "1"));
                    // 矫正中期及末期都设置为已完善
                    plan.setPlanStatus(tag > 1 ? 1 : 0);
                    // DelFlag = 1 表示是有已进入在矫调整的数据，将入矫制定的数据设为1
                    plan.setDelFlag(tag > 1 ? 1 : 0);
                    correctionCorrectPlanService.save(plan);
                    if (1 == plan.getPlanStatus()) {
                        // 如果是以完善的， 则还需生成一条入矫调整数据
                        plan.setId(null);
                        plan.setPlanStatus(1);
                        plan.setCorrectPlan(correctionCorrectPlanService.createPlan(cf.getId(), "2"));
                        plan.setDelFlag(0);
                        correctionCorrectPlanService.save(plan);
                    }

                }
            } catch (Exception e) {
                log.error("initToCorrectPlan Error, " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 计算矫正对象矫正阶段 （1：入矫初期 2：矫正中期 3：矫正末期）
     *
     * @param sqjzryId
     * @return
     */
    @Override
    @DS("slave")
    public int getCorrectPhase(String sqjzryId) {
        int phase = 0;
        CorrectionObjectInformation sqjzry = getById(sqjzryId);
        if (null != sqjzry) {
            Date rjsj = sqjzry.getRujiaoriqi();
            Date sqjzjsrq = sqjzry.getSqjzjsrq();
            if (null != rjsj && null != sqjzjsrq) {
                int rj = DateTimeUtil.getDayNum(rjsj, new Date());
                if (rj < 31) {
                    //当前时间与入矫时间相差30天则算入矫初期
                    return 1;
                }
                int jzjs = DateTimeUtil.getDayNum(new Date(), sqjzjsrq);
                if (jzjs < 31) {
                    //当前时间与矫正结束时间相差30天则算矫正末期
                    return 3;
                } else {
                    //矫正中期
                    return 2;
                }

            }
        }
        return phase;
    }

    /**
     * （1：入矫初期 2：矫正中期 3：矫正末期）
     *
     * @param rjsj
     * @param sqjzjsrq
     * @return
     */
    public int getCorrectPhase(Date rjsj, Date sqjzjsrq) {
        int phase = 0;
        if (null != rjsj && null != sqjzjsrq) {
            int rj = DateTimeUtil.getDayNum(rjsj, new Date());
            if (rj < 31) {
                //当前时间与入矫时间相差30天则算入矫初期
                return 1;
            }
            int jzjs = DateTimeUtil.getDayNum(new Date(), sqjzjsrq);
            if (jzjs < 31) {
                //当前时间与矫正结束时间相差30天则算矫正末期
                return 3;
            } else {
                //矫正中期
                return 2;
            }

        }
        return phase;
    }

    /**
     * 每天凌晨查询矫正结束时间还剩30天的矫正对象， 将数据初始化到解矫总结模块
     *
     * @return
     */
    @Override
    public void createSummaryData(Set<String> org) {
        try {
            SimpleDateFormat sfDay = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
            Date date = DateTimeUtil.getDay(30);
            CorrectionObjectInformationParam cp = new CorrectionObjectInformationParam();
            cp.setSqjzjsrq_begin(sf.parse(sfDay.format(date) + "000000"));
            cp.setSqjzjsrq_end(sf.parse(sfDay.format(date) + "235959"));
            List<CorrectionObjectInformation> list = this.list(cp, org);
            for (CorrectionObjectInformation cf : list) {
                // 插入矫正方案表
                CorrectionCorrectPlan plan = new CorrectionCorrectPlan();
                plan.setSqjzryId(cf.getId());
                plan.setSqjzryName(cf.getXm());
                plan.setSfzh(cf.getSfzh());
                plan.setJzjg(cf.getJzjg());
                plan.setJzjgName(cf.getJzjgName());
                plan.setCriminalBehavior(cf.getZyfzss());
                // 生成矫正方案措施
                plan.setCorrectPlan(correctionCorrectPlanService.createPlan(cf.getId(), "3"));
                // 解矫总结
                plan.setPlanStatus(2);
                // DelFlag = 2 表示解矫总结
                plan.setDelFlag(0);
                correctionCorrectPlanService.save(plan);
            }
        } catch (Exception e) {
            log.error("createSummaryData Error," + e.getMessage());
        }
    }

    @Override
    public PageResult<CorrectionObjectInformation> page(List<String> label, Set<String> org) {
        LambdaQueryWrapper<CorrectionObjectInformation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //查询指定矫正机构内的数据
        if (ObjectUtil.isNotNull(org) && org.size() > 0) {
            lambdaQueryWrapper.in(CorrectionObjectInformation::getJzjg, org);
        }
        //根据婚姻状况查询
        for (String s : label) {
            lambdaQueryWrapper.eq(CorrectionObjectInformation::getHyzkName, s)
                    //根据矫正类别查询
                    .or().eq(CorrectionObjectInformation::getJzlbName, s)
                    //根据是否成年查询
                    .or().eq(CorrectionObjectInformation::getSfcnName, s);
        }
        lambdaQueryWrapper.orderByDesc(CorrectionObjectInformation::getRujiaoriqi);
        return new PageResult<>(this.page(PageFactory.defaultPage(), lambdaQueryWrapper));
    }

    @Override
    public void initLabelPsn() {
        QueryWrapper<CorrectionObjectInformation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(CorrectionObjectInformation::getZhuangtai, "200");
        queryWrapper.select("id, sfzh, csrq, rujiaoriqi, sqjzjsrq, xb, jzlb, hyzk, whcd, jzjb, jtzm, ypxqkssj");
        List<CorrectionObjectInformation> list = this.list(queryWrapper);
        int tag;
        for (CorrectionObjectInformation cf : list) {
            try {
                tag = getCorrectPhase(cf.getRujiaoriqi(), cf.getSqjzjsrq());
                // 绑定标签
                //根据字典值绑定
                Set<String> dictionaryCode = new HashSet<>();
                dictionaryCode.add("xb" + cf.getXb());
                dictionaryCode.add("jzlb" + cf.getJzlb());
                dictionaryCode.add("hyzk" + cf.getHyzk());
                dictionaryCode.add("whcd" + cf.getWhcd());
                dictionaryCode.add("jzjb" + cf.getJzjb());
                dictionaryCode.add("zm" + cf.getJtzm());
                List<String> labelIds = correctionLabelDictionaryMapper.getLabelIds(dictionaryCode);
                //绑定犯罪时年龄标签
                String ageLabelId = returnLabelIdByDate(DateUtil.format(cf.getCsrq(), "yyyy-MM-dd"), cf.getYpxqkssj());
                if (StringUtils.isNotBlank(ageLabelId)) {
                    labelIds.add(ageLabelId);
                }
                // 添加矫正阶段标签
                String lId = LabelConstant.PHASE.get(tag);
                if (ObjectUtil.isNotEmpty(lId)) {
                    labelIds.add(lId);
                }
                if (labelIds.size() > 0) {
                    correctionLabelCorrpsnService.saveInfo(cf.getId(), labelIds, cf.getSfzh());
                }
            } catch (Exception e) {
                log.error("initLabelPsn Error, " + e.getMessage());
            }
        }
    }

    @Override
    public void initLabelPsnByPerson(CorrectionObjectInformation cf) {
        int tag;
        try {
            tag = getCorrectPhase(cf.getRujiaoriqi(), cf.getSqjzjsrq());
            // 绑定标签
            //根据字典值绑定
            Set<String> dictionaryCode = new HashSet<>();
            dictionaryCode.add("xb" + cf.getXb());
            dictionaryCode.add("jzlb" + cf.getJzlb());
            dictionaryCode.add("hyzk" + cf.getHyzk());
            dictionaryCode.add("whcd" + cf.getWhcd());
            dictionaryCode.add("jzjb" + cf.getJzjb());
            dictionaryCode.add("zm" + cf.getJtzm());
            List<String> labelIds = correctionLabelDictionaryMapper.getLabelIds(dictionaryCode);
            //绑定犯罪时年龄标签
            String ageLabelId = returnLabelIdByDate(DateUtil.format(cf.getCsrq(), "yyyy-MM-dd"), cf.getYpxqkssj());
            if (StringUtils.isNotBlank(ageLabelId)) {
                labelIds.add(ageLabelId);
            }
            // 添加矫正阶段标签
            String lId = LabelConstant.PHASE.get(tag);
            if (ObjectUtil.isNotEmpty(lId)) {
                labelIds.add(lId);
            }
            if (labelIds.size() > 0) {
                correctionLabelCorrpsnService.saveInfo(cf.getId(), labelIds, cf.getSfzh());
            }
        } catch (Exception e) {
            log.error("initLabelPsn Error, " + e.getMessage());
        }
    }

    /**
     * 根据日期计算犯罪时年龄
     *
     * @param birthday  出生日期
     * @param startDate 原判刑期开始时间
     * @return
     */
    public static String returnLabelIdByDate(String birthday, String startDate) {
        String id = "";
        if (StringUtils.isNotBlank(birthday) && StringUtils.isNotBlank(startDate)) {
            Long localBirthday = DateUtil.parse(birthday, "yyyy-MM-dd").getTime();
            Long localStartDate = DateUtil.parse(startDate, "yyyy-MM-dd").getTime();
            Long now = System.currentTimeMillis();
            Long ageMills = (now - localBirthday) - (now - localStartDate);
            BigDecimal millisecondsPerYear = new BigDecimal(365.2425 * 24 * 60 * 60 * 1000);
            BigDecimal DecimalAge = new BigDecimal(ageMills).divide(millisecondsPerYear, 1, BigDecimal.ROUND_HALF_UP);
            Float age = DecimalAge.floatValue();
            if (age < 18) {
                id = "12345604";
            } else if (age >= 18 && age < 35) {
                id = "12345603";
            } else if (age >= 35 && age < 45) {
                id = "12345602";
            } else if (age >= 45 && age < 60) {
                id = "12345601";
            } else if (age >= 60) {
                id = "12345600";
            }
        }
        return id;
    }

    @Override
    public List<ScreenModel> moodCode(Set<String> userIds) {
        return this.baseMapper.moodCode(userIds);
    }

    @DS("slave")
    @Override
    public Date getJiejiaoriqiById(String id) {
        return this.baseMapper.getJiejiaoriqiById(id);
    }
}
