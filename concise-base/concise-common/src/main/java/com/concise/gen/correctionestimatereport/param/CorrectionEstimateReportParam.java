package com.concise.gen.correctionestimatereport.param;

import com.concise.common.pojo.base.param.BaseParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 评估报告参数类
 *
 * <AUTHOR>
 * @date 2023-01-09 10:57:38
 */
@Data
@ApiModel("评估报告参数类")
public class CorrectionEstimateReportParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 矫正对象id
     */
    @NotBlank(message = "矫正对象id不能为空，请检查sqjzryId参数", groups = {add.class, edit.class})
    private String sqjzryId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查sqjzryName参数", groups = {add.class, edit.class})
    private String sqjzryName;

    /**
     * 性别
     */
    @NotBlank(message = " 性别不能为空，请检查xb参数", groups = {add.class, edit.class})
    private String xb;

    /**
     * 性别中文值
     */
    @NotBlank(message = "性别中文值不能为空，请检查xbName参数", groups = {add.class, edit.class})
    private String xbName;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空，请检查sfzh参数", groups = {add.class, edit.class})
    private String sfzh;

    /**
     * 年龄
     */
    @NotNull(message = "年龄不能为空，请检查age参数", groups = {add.class, edit.class})
    private Integer age;

    /**
     * 是否成年
     */
    @NotBlank(message = "是否成年不能为空，请检查sfcn参数", groups = {add.class, edit.class})
    private String sfcn;

    /**
     * 婚姻状况
     */
    @NotBlank(message = "婚姻状况不能为空，请检查hyzk参数", groups = {add.class, edit.class})
    private String hyzk;

    /**
     * 婚姻状况中文值
     */
    @NotBlank(message = "婚姻状况中文值不能为空，请检查hyzkName参数", groups = {add.class, edit.class})
    private String hyzkName;

    /**
     * 文化程度
     */
    @NotBlank(message = "文化程度不能为空，请检查whcd参数", groups = {add.class, edit.class})
    private String whcd;

    /**
     * 文化程度中文值
     */
    @NotBlank(message = "文化程度中文值不能为空，请检查whcdName参数", groups = {add.class, edit.class})
    private String whcdName;

    /**
     * 矫正机构ID
     */
    @NotBlank(message = "矫正机构ID不能为空，请检查jzjg参数", groups = {add.class, edit.class})
    private String jzjg;

    /**
     * 矫正机构名称
     */
    @NotBlank(message = "矫正机构名称不能为空，请检查jzjgName参数", groups = {add.class, edit.class})
    private String jzjgName;

    /**
     * 入娇评估分
     */
    @NotNull(message = "入娇评估分不能为空，请检查enterScore参数", groups = {add.class, edit.class})
    private BigDecimal enterScore;

    /**
     * 上月评估分
     */
    @NotNull(message = "上月评估分不能为空，请检查lastMonthScore参数", groups = {add.class, edit.class})
    private BigDecimal lastMonthScore;

    /**
     * 本月评估分
     */
    @NotNull(message = "本月评估分不能为空，请检查scoreEstimate参数", groups = {add.class, edit.class})
    private BigDecimal scoreEstimate;

    /**
     * 评估月份
     */
    @NotNull(message = "评估月份不能为空，请检查estimateMonth参数", groups = {add.class, edit.class})
    private Integer estimateMonth;

    /**
     * 累犯惯犯
     */
    @NotBlank(message = "累犯惯犯不能为空，请检查oldLag参数", groups = {add.class, edit.class})
    private String oldLag;

    /**
     * 违法犯罪案由
     */
    @NotBlank(message = "违法犯罪案由不能为空，请检查crimeReason参数", groups = {add.class, edit.class})
    private String crimeReason;

    /**
     * 社区矫正类别
     */
    @NotBlank(message = "社区矫正类别不能为空，请检查correctType参数", groups = {add.class, edit.class})
    private String correctType;

    /**
     * 是否数罪并罚
     */
    @NotBlank(message = "是否数罪并罚不能为空，请检查moreCrime参数", groups = {add.class, edit.class})
    private String moreCrime;

    /**
     * 是否共同犯罪
     */
    @NotBlank(message = "是否共同犯罪不能为空，请检查togetherCrime参数", groups = {add.class, edit.class})
    private String togetherCrime;

    /**
     * 是否五独
     */
    @NotBlank(message = "是否五独不能为空，请检查fivePoisons参数", groups = {add.class, edit.class})
    @ApiModelProperty("是否五独")
    private String fivePoisons;

    /**
     * 是否五涉
     */
    @NotBlank(message = "是否五涉不能为空，请检查fiveInvolvement参数", groups = {add.class, edit.class})
    @ApiModelProperty("是否五涉")
    private String fiveInvolvement;

    /**
     * 是否四史
     */
    @NotBlank(message = "是否四史不能为空，请检查fourFamous参数", groups = {add.class, edit.class})
    @ApiModelProperty("是否四史")
    private String fourFamous;

    /**
     * 矫正类别
     */
    @NotBlank(message = "矫正类别不能为空，请检查jzlb参数", groups = {add.class, edit.class})
    private String jzlb;

    /**
     * 矫正类别中文值
     */
    @NotBlank(message = "矫正类别中文值不能为空，请检查jzlbName参数", groups = {add.class, edit.class})
    @ApiModelProperty("矫正类别中文值")
    private String jzlbName;

    /**
     * 是否成年中文值
     */
    @NotBlank(message = "是否成年中文值不能为空，请检查sfcnName参数", groups = {add.class, edit.class})
    @ApiModelProperty("是否成年中文值")
    private String sfcnName;

    /**
     * 就业就学情况
     */
    @NotBlank(message = "就业就学情况不能为空，请检查jyjxqk参数", groups = {add.class, edit.class})
    private String jyjxqk;

    /**
     * 就业就学情况中文值
     */
    @NotBlank(message = "就业就学情况中文值不能为空，请检查jyjxqkName参数", groups = {add.class, edit.class})
    private String jyjxqkName;

    /**
     * 具体罪名
     */
    @NotNull(message = "具体罪名不能为空，请检查jtzm参数", groups = {add.class, edit.class})
    private String jtzm;

    /**
     * 具体罪名中文值
     */
    @NotNull(message = "具体罪名中文值不能为空，请检查jtzmName参数", groups = {add.class, edit.class})
    private String jtzmName;

    private String riskLevel;

    /**
     * 是否删除（0：未删除，1删除）
     */
    @NotNull(message = "是否删除（0：未删除，1删除）不能为空，请检查delFlag参数", groups = {add.class, edit.class})
    private Integer delFlag;

}
