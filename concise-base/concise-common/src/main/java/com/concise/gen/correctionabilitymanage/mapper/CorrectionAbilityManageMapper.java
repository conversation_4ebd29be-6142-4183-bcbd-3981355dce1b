package com.concise.gen.correctionabilitymanage.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.concise.gen.correctionabilitymanage.entity.CorrectionAbilityManage;
import com.concise.gen.correctionlabelmanage.entity.CorrectionLabelManage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 能力管理
 *
 * <AUTHOR>
 * @date 2022-03-03 10:57:44
 */
public interface CorrectionAbilityManageMapper extends BaseMapper<CorrectionAbilityManage> {

    /**
     *  列表查询
     * @param page 分页对象
     * @param queryWrapper 查询条件
     * @return
     */
    Page<CorrectionAbilityManage> page(@Param("page") Page page, @Param("ew") QueryWrapper queryWrapper, @Param("labelIds") List<String> labelIds);

    /**
     * 根据矫正阶段返回能力信息
     * @param correctPhase
     * @return
     */
    List<CorrectionAbilityManage> findByCorrectPhase(int correctPhase);
}
