package com.concise.gen.correctionlabelcorrpsn.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 标签关联矫正对象
 *
 * <AUTHOR>
 * @date 2022-03-04 14:58:19
 */
@Data
@TableName("correction_label_corrpsn")
public class CorrectionLabelCorrpsn {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 标签ID
     */
    private String labelId;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 矫正对象ID
     */
    private String sqjzryId;

    /**
     * 矫正对象身份证号(由于教育帮扶部分人员id与平台不一致，所以加该字段用以同步人员标签数据)
     */
    private String sfzh;

    /**
     * 矫正对象IDs
     */
    @TableField(exist = false)
    private String[] sqjzryIds;
    /**
     * 标签IDs
     */
    @TableField(exist = false)
    private String[] labelIds;

    /**
     * 方案库信息
     */
    @TableField(exist = false)
    private String abilityInfo;

    /**
     * 方案库id
     */
    @TableField(exist = false)
    private String abilityId;

    /**
     * 方案库pid
     */
    @TableField(exist = false)
    private String abilityPid;

}
