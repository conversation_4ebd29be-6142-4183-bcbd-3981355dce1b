package com.concise.gen.exampaper.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.concise.gen.examquestion.entity.ExamQuestion;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.List;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 新量表管理
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("exam_paper")
public class ExamPaper extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 量表名称
     */
    private String title;

    /**
     * 启用情况（1：启用 2：停用）
     */
    private Integer status;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private String delFlag;

    @TableField(exist = false)
    private List<ExamQuestion> questionList;

    /**
     * 试题包含的题库分类集合
     */
    @TableField(exist = false)
    private String typeId;

}
