<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.correctionestimatereport.mapper.CorrectionEstimateReportMapper">

    <select id="getIdList"
            resultType="com.concise.gen.correctionestimatereport.entity.CorrectionEstimateReport">
        select id,estimate_month ,level,score_estimate,risks from correction_estimate_report where sqjzry_id=#{sqjzryId} and estimate_month <![CDATA[>=]]> #{startMonth} and  estimate_month <![CDATA[<=]]> #{endMonth} order by estimate_month asc
    </select>
</mapper>
