package com.concise.gen.correctioneducationalrectification.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 教育整顿
 *
 * <AUTHOR>
 * @date 2022-03-10 17:37:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_educational_rectification")
public class CorrectionEducationalRectification extends BaseEntity {

    /**
     * 
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * dept_id 发布部门
     */
    private String deptId;

    /**
     * dept_name 发布部门名称
     */
    private String deptName;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

}
