package com.concise.gen.portraitImage.correctionassespersondtl.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.examquestion.entity.ExamQuestion;
import com.concise.gen.portraitImage.correctionassesanswer.entity.CorrectionAssesAnswer;
import com.concise.gen.portraitImage.correctionquestion.entity.CorrectionQuestion;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
* 评估管理--评估人员评估明细参数类（问卷）
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:43
*/
@Data
public class CorrectionAssesPersonDtlParam extends BaseParam {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 评估人员信息ID
     */
    @ApiModelProperty(value = "评估人员信息ID")
    private String assesPersonId;
    /**
     * 矫正人员信息ID
     */
    @ApiModelProperty(value = "矫正人员信息ID")
    private String sqjzryId;
    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String title;

    /**
     * 量表id，冗余，方便查找题目
     */
    @ApiModelProperty(value = "量表id，冗余，方便查找题目")
    private String scaleBaseId;

    /**
     * 测评开始时间，格式: yyyyMMdd
     */
    @ApiModelProperty(value = "测评开始时间，格式: yyyyMMdd")
    private String startTime;

    /**
     * 测评结束时间，格式: yyyyMMdd
     */
    @ApiModelProperty(value = "测评结束时间，格式: yyyyMMdd")
    private String endTime;

    /**
     * 填写状态：0：未填写 1: 已填写
     */
    @ApiModelProperty(value = "填写状态：0：未填写 1: 已填写")
    private Integer status;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Integer orderIndex;

    /**
     * 是否临期提醒：0：否  1：是
     */
    @ApiModelProperty(value = "是否临期提醒：0：否  1：是")
    private Integer needWarn;

    /**
     * 临期天数
     */
    @ApiModelProperty(value = "临期天数")
    private Integer dayNum;

    /**
     * 移动端是否显示提醒： 0：否  1:是
     */
    @ApiModelProperty(value = "移动端是否显示提醒： 0：否  1:是")
    private Integer tag;

    /**
     * 问卷答案
     */
    @ApiModelProperty(value = "问卷答案")
    private List<CorrectionAssesAnswer> answerList;

    /**
     * 问题&答案(查看时使用)
     */
    @ApiModelProperty(value = "问题&答案(查看时使用)")
    private List<CorrectionQuestion> questionList;

    @ApiModelProperty(value = "新的问题&答案")
    private List<ExamQuestion> examQuestionList;
}
