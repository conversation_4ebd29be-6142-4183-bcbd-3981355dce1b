package com.concise.gen.examquestionitem. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.examquestionitem. param.ExamQuestionItemParam;
import com.concise.gen.examquestionitem. service.ExamQuestionItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 问题选项控制器
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:11
 */
@Api(tags = "问题选项")
@RestController
public class ExamQuestionItemController {

    @Resource
    private ExamQuestionItemService examQuestionItemService;

    /**
     * 查询问题选项
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:11
     */
    @Permission
    @GetMapping("/examQuestionItem/page")
    @ApiOperation("问题选项_分页查询")
    @BusinessLog(title = "问题选项_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(ExamQuestionItemParam examQuestionItemParam) {
        return new SuccessResponseData(examQuestionItemService.page(examQuestionItemParam));
    }

    /**
     * 添加问题选项
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:11
     */
    @Permission
    @PostMapping("/examQuestionItem/add")
    @ApiOperation("问题选项_增加")
    @BusinessLog(title = "问题选项_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(ExamQuestionItemParam.add.class) ExamQuestionItemParam examQuestionItemParam) {
        examQuestionItemService.add(examQuestionItemParam);
        return new SuccessResponseData();
    }

    /**
     * 删除问题选项
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:11
     */
    @Permission
    @PostMapping("/examQuestionItem/delete")
    @ApiOperation("问题选项_删除")
    @BusinessLog(title = "问题选项_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(ExamQuestionItemParam.delete.class) ExamQuestionItemParam examQuestionItemParam) {
        examQuestionItemService.delete(examQuestionItemParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑问题选项
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:11
     */
    @Permission
    @PostMapping("/examQuestionItem/edit")
    @ApiOperation("问题选项_编辑")
    @BusinessLog(title = "问题选项_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(ExamQuestionItemParam.edit.class) ExamQuestionItemParam examQuestionItemParam) {
        examQuestionItemService.edit(examQuestionItemParam);
        return new SuccessResponseData();
    }

    /**
     * 查看问题选项
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:11
     */
    @Permission
    @GetMapping("/examQuestionItem/detail")
    @ApiOperation("问题选项_查看")
    @BusinessLog(title = "问题选项_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(ExamQuestionItemParam.detail.class) ExamQuestionItemParam examQuestionItemParam) {
        return new SuccessResponseData(examQuestionItemService.detail(examQuestionItemParam));
    }

    /**
     * 问题选项列表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:11
     */
    @Permission
    @GetMapping("/examQuestionItem/list")
    @ApiOperation("问题选项_列表")
    @BusinessLog(title = "问题选项_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(ExamQuestionItemParam examQuestionItemParam) {
        return new SuccessResponseData(examQuestionItemService.list(examQuestionItemParam));
    }

}
