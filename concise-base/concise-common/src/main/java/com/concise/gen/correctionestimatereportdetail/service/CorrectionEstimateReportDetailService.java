package com.concise.gen.correctionestimatereportdetail.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimatereportdetail.entity.CorrectionEstimateReportDetail;
import com.concise.gen.correctionestimatereportdetail.param.CorrectionEstimateReportDetailParam;
import java.util.List;

/**
 * 评估报告详情service接口
 *
 * <AUTHOR>
 * @date 2023-01-10 15:55:35
 */
public interface CorrectionEstimateReportDetailService extends IService<CorrectionEstimateReportDetail> {

    /**
     * 查询评估报告详情
     *
     * <AUTHOR>
     * @date 2023-01-10 15:55:35
     */
    PageResult<CorrectionEstimateReportDetail> page(CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam);

    /**
     * 评估报告详情列表
     *
     * <AUTHOR>
     * @date 2023-01-10 15:55:35
     */
    List<CorrectionEstimateReportDetail> list(CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam);

    /**
     * 添加评估报告详情
     *
     * <AUTHOR>
     * @date 2023-01-10 15:55:35
     */
    void add(CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam);

    /**
     * 删除评估报告详情
     *
     * <AUTHOR>
     * @date 2023-01-10 15:55:35
     */
    void delete(CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam);

    /**
     * 编辑评估报告详情
     *
     * <AUTHOR>
     * @date 2023-01-10 15:55:35
     */
    void edit(CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam);

    /**
     * 查看评估报告详情
     *
     * <AUTHOR>
     * @date 2023-01-10 15:55:35
     */
     CorrectionEstimateReportDetail detail(CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam);

    /**
     * 根据评估报告id查询
     * @param reportId
     * @return
     */
    List<CorrectionEstimateReportDetail> listByReportId(List<String> reportId);
}
