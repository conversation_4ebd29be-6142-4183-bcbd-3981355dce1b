package com.concise.gen.correctionrisklevelhis.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionrisklevelhis.entity.CorrectionRiskLevelHis;
import com.concise.gen.correctionrisklevelhis.enums.CorrectionRiskLevelHisExceptionEnum;
import com.concise.gen.correctionrisklevelhis.mapper.CorrectionRiskLevelHisMapper;
import com.concise.gen.correctionrisklevelhis.param.CorrectionRiskLevelHisParam;
import com.concise.gen.correctionrisklevelhis.service.CorrectionRiskLevelHisService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 风险等级_历史service接口实现类
 *
 * <AUTHOR>
 * @date 2022-06-06 18:01:04
 */
@Service
public class CorrectionRiskLevelHisServiceImpl extends ServiceImpl<CorrectionRiskLevelHisMapper, CorrectionRiskLevelHis> implements CorrectionRiskLevelHisService {

    @Override
    public PageResult<CorrectionRiskLevelHis> page(CorrectionRiskLevelHisParam correctionRiskLevelHisParam) {
        QueryWrapper<CorrectionRiskLevelHis> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionRiskLevelHisParam)) {

            // 根据评分月度 查询
            if (ObjectUtil.isNotEmpty(correctionRiskLevelHisParam.getScoringTime())) {
                queryWrapper.lambda().eq(CorrectionRiskLevelHis::getScoringTime, correctionRiskLevelHisParam.getScoringTime());
            }
            // 根据评分模型ID 查询
            if (ObjectUtil.isNotEmpty(correctionRiskLevelHisParam.getScoringModelId())) {
                queryWrapper.lambda().eq(CorrectionRiskLevelHis::getScoringModelId, correctionRiskLevelHisParam.getScoringModelId());
            }
            // 根据矫正对象id 查询
            if (ObjectUtil.isNotEmpty(correctionRiskLevelHisParam.getSqjzryId())) {
                queryWrapper.lambda().eq(CorrectionRiskLevelHis::getSqjzryId, correctionRiskLevelHisParam.getSqjzryId());
            }
            // 根据是否删除（0：未删除，1删除） 查询
            if (ObjectUtil.isNotEmpty(correctionRiskLevelHisParam.getDelFlag())) {
                queryWrapper.lambda().eq(CorrectionRiskLevelHis::getDelFlag, correctionRiskLevelHisParam.getDelFlag());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionRiskLevelHis> list(CorrectionRiskLevelHisParam correctionRiskLevelHisParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionRiskLevelHisParam correctionRiskLevelHisParam) {
        CorrectionRiskLevelHis correctionRiskLevelHis = new CorrectionRiskLevelHis();
        BeanUtil.copyProperties(correctionRiskLevelHisParam, correctionRiskLevelHis);
        this.save(correctionRiskLevelHis);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionRiskLevelHisParam correctionRiskLevelHisParam) {
        this.removeById(correctionRiskLevelHisParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionRiskLevelHisParam correctionRiskLevelHisParam) {
        CorrectionRiskLevelHis correctionRiskLevelHis = this.queryCorrectionRiskLevelHis(correctionRiskLevelHisParam);
        BeanUtil.copyProperties(correctionRiskLevelHisParam, correctionRiskLevelHis);
        this.updateById(correctionRiskLevelHis);
    }

    @Override
    public CorrectionRiskLevelHis detail(CorrectionRiskLevelHisParam correctionRiskLevelHisParam) {
        return this.queryCorrectionRiskLevelHis(correctionRiskLevelHisParam);
    }

    /**
     * 获取风险等级_历史
     *
     * <AUTHOR>
     * @date 2022-06-06 18:01:04
     */
    private CorrectionRiskLevelHis queryCorrectionRiskLevelHis(CorrectionRiskLevelHisParam correctionRiskLevelHisParam) {
        CorrectionRiskLevelHis correctionRiskLevelHis = this.getById(correctionRiskLevelHisParam.getId());
        if (ObjectUtil.isNull(correctionRiskLevelHis)) {
            throw new ServiceException(CorrectionRiskLevelHisExceptionEnum.NOT_EXIST);
        }
        return correctionRiskLevelHis;
    }
}
