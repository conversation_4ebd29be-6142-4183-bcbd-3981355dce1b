package com.concise.gen.indexmanageportrayal.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.indexmanage. param.IndexManageParam;
import com.concise.gen.indexmanage. service.IndexManageService;
import com.concise.gen.indexmanageportrayal.param.IndexManagePortrayalParam;
import com.concise.gen.indexmanageportrayal.service.IndexManagePortrayalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 指标管理信息表控制器
 *
 * <AUTHOR>
 * @date 2022-05-12 10:02:19
 */
@Api(tags = "指标管理信息表(精准画像)")
@RestController
public class IndexManagePortrayalController {

    @Resource
    private IndexManagePortrayalService indexManagePortrayalService;

    /**
     * 查询指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    @Permission
    @GetMapping("/indexManagePortrayal/page")
    @ApiOperation("指标管理信息表_分页查询")
    @BusinessLog(title = "指标管理信息表_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(IndexManagePortrayalParam indexManagePortrayalParam) {
        indexManagePortrayalParam.setDelFlag("0");
        return new SuccessResponseData(indexManagePortrayalService.page(indexManagePortrayalParam));
    }

    /**
     * 添加指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    @Permission
    @PostMapping("/indexManagePortrayal/add")
    @ApiOperation("指标管理信息表_增加")
    @BusinessLog(title = "指标管理信息表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(IndexManagePortrayalParam.add.class) IndexManagePortrayalParam indexManagePortrayalParam) {
        indexManagePortrayalParam.setDelFlag("0");
        indexManagePortrayalService.add(indexManagePortrayalParam);
        return new SuccessResponseData();
    }

    /**
     * 删除指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    @Permission
    @PostMapping("/indexManagePortrayal/delete")
    @ApiOperation("指标管理信息表_删除")
    @BusinessLog(title = "指标管理信息表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(IndexManagePortrayalParam.delete.class) IndexManagePortrayalParam indexManagePortrayalParam) {
        indexManagePortrayalService.delete(indexManagePortrayalParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    @Permission
    @PostMapping("/indexManagePortrayal/edit")
    @ApiOperation("指标管理信息表_编辑")
    @BusinessLog(title = "指标管理信息表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(IndexManagePortrayalParam.edit.class) IndexManagePortrayalParam indexManagePortrayalParam) {
        indexManagePortrayalService.edit(indexManagePortrayalParam);
        return new SuccessResponseData();
    }

    /**
     * 查看指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    @Permission
    @GetMapping("/indexManagePortrayal/detail")
    @ApiOperation("指标管理信息表_查看")
    @BusinessLog(title = "指标管理信息表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(IndexManagePortrayalParam.detail.class) IndexManagePortrayalParam indexManagePortrayalParam) {
        return new SuccessResponseData(indexManagePortrayalService.detail(indexManagePortrayalParam));
    }

    /**
     * 指标管理信息表列表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    @Permission
    @GetMapping("/indexManagePortrayal/list")
    @ApiOperation("指标管理信息表_列表")
    @BusinessLog(title = "指标管理信息表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(IndexManagePortrayalParam indexManagePortrayalParam) {
        return new SuccessResponseData(indexManagePortrayalService.list(indexManagePortrayalParam));
    }

}
