package com.concise.gen.correctionresume.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
 * 个人简历参数类
 *
 * <AUTHOR>
 * @date 2022-02-22 16:07:54
 */
@Data
public class CorrectionResumeParam extends BaseParam {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 社区矫正人员标识
     */
    @NotBlank(message = "社区矫正人员标识不能为空，请检查pid参数", groups = {add.class, edit.class})
    private String pid;

    /**
     * 起时
     */
    @NotNull(message = "起时不能为空，请检查qs参数", groups = {add.class, edit.class})
    private String qs;

    /**
     * 止日
     */
    @NotNull(message = "止日不能为空，请检查zr参数", groups = {add.class, edit.class})
    private String zr;

    /**
     * 所在单位
     */
    @NotBlank(message = "所在单位不能为空，请检查szdw参数", groups = {add.class, edit.class})
    private String szdw;

    /**
     * 职务
     */
    @NotBlank(message = "职务不能为空，请检查zw参数", groups = {add.class, edit.class})
    private String zw;

    /**
     * 职务Name
     */
    @NotBlank(message = "职务Name不能为空，请检查zwName参数", groups = {add.class, edit.class})
    private String zwName;

    /**
     * 是否删除（0：未删除，1删除）
     */
    @NotNull(message = "是否删除（0：未删除，1删除）不能为空，请检查delFlag参数", groups = {add.class, edit.class})
    private Integer delFlag;

}
