package com.concise.gen.portraitImage.correctionassespersondtl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.portraitImage.correctionassespersondtl.entity.CorrectionAssesPersonDtl;

import java.util.List;

/**
 * 评估管理--评估人员评估明细
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:43
 */
public interface CorrectionAssesPersonDtlMapper extends BaseMapper<CorrectionAssesPersonDtl> {

    /**
     * 根据评估人员删除明细
     * @param list 评估人员信息ID集合
     */
    void delByAssesPersonId(List<String> list);
}
