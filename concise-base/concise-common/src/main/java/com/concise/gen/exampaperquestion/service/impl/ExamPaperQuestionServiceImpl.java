package com.concise.gen.exampaperquestion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.exampaperquestion.entity.ExamPaperQuestion;
import com.concise.gen.exampaperquestion.enums.ExamPaperQuestionExceptionEnum;
import com.concise.gen.exampaperquestion.mapper.ExamPaperQuestionMapper;
import com.concise.gen.exampaperquestion.param.ExamPaperQuestionParam;
import com.concise.gen.exampaperquestion.service.ExamPaperQuestionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 量表题目内容service接口实现类
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:05
 */
@Service
public class ExamPaperQuestionServiceImpl extends ServiceImpl<ExamPaperQuestionMapper, ExamPaperQuestion> implements ExamPaperQuestionService {

    @Override
    public PageResult<ExamPaperQuestion> page(ExamPaperQuestionParam examPaperQuestionParam) {
        QueryWrapper<ExamPaperQuestion> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(examPaperQuestionParam)) {

            // 根据量表id 查询
            if (ObjectUtil.isNotEmpty(examPaperQuestionParam.getPaperId())) {
                queryWrapper.lambda().eq(ExamPaperQuestion::getPaperId, examPaperQuestionParam.getPaperId());
            }
            // 根据题目id 查询
            if (ObjectUtil.isNotEmpty(examPaperQuestionParam.getQuestionId())) {
                queryWrapper.lambda().eq(ExamPaperQuestion::getQuestionId, examPaperQuestionParam.getQuestionId());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<ExamPaperQuestion> list(ExamPaperQuestionParam examPaperQuestionParam) {
        return this.list();
    }

    @Override
    public void add(ExamPaperQuestionParam examPaperQuestionParam) {
        ExamPaperQuestion examPaperQuestion = new ExamPaperQuestion();
        BeanUtil.copyProperties(examPaperQuestionParam, examPaperQuestion);
        this.save(examPaperQuestion);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(ExamPaperQuestionParam examPaperQuestionParam) {
        this.removeById(examPaperQuestionParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ExamPaperQuestionParam examPaperQuestionParam) {
        ExamPaperQuestion examPaperQuestion = this.queryExamPaperQuestion(examPaperQuestionParam);
        BeanUtil.copyProperties(examPaperQuestionParam, examPaperQuestion);
        this.updateById(examPaperQuestion);
    }

    @Override
    public ExamPaperQuestion detail(ExamPaperQuestionParam examPaperQuestionParam) {
        return this.queryExamPaperQuestion(examPaperQuestionParam);
    }

    /**
     * 获取量表题目内容
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:05
     */
    private ExamPaperQuestion queryExamPaperQuestion(ExamPaperQuestionParam examPaperQuestionParam) {
        ExamPaperQuestion examPaperQuestion = this.getById(examPaperQuestionParam.getId());
        if (ObjectUtil.isNull(examPaperQuestion)) {
            throw new ServiceException(ExamPaperQuestionExceptionEnum.NOT_EXIST);
        }
        return examPaperQuestion;
    }
}
