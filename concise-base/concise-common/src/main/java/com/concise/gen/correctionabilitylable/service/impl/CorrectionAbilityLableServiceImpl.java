package com.concise.gen.correctionabilitylable.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionabilitylable.entity.CorrectionAbilityLable;
import com.concise.gen.correctionabilitylable.enums.CorrectionAbilityLableExceptionEnum;
import com.concise.gen.correctionabilitylable.mapper.CorrectionAbilityLableMapper;
import com.concise.gen.correctionabilitylable.param.CorrectionAbilityLableParam;
import com.concise.gen.correctionabilitylable.service.CorrectionAbilityLableService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 能力管理、标签关联表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-03-03 11:00:37
 */
@Service
public class CorrectionAbilityLableServiceImpl extends ServiceImpl<CorrectionAbilityLableMapper, CorrectionAbilityLable> implements CorrectionAbilityLableService {

    @Override
    public PageResult<CorrectionAbilityLable> page(CorrectionAbilityLableParam correctionAbilityLableParam) {
        QueryWrapper<CorrectionAbilityLable> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionAbilityLableParam)) {

            // 根据能力id 查询
            if (ObjectUtil.isNotEmpty(correctionAbilityLableParam.getAbilityId())) {
                queryWrapper.lambda().eq(CorrectionAbilityLable::getAbilityId, correctionAbilityLableParam.getAbilityId());
            }
            // 根据标签id 查询
            if (ObjectUtil.isNotEmpty(correctionAbilityLableParam.getLableId())) {
                queryWrapper.lambda().eq(CorrectionAbilityLable::getLableId, correctionAbilityLableParam.getLableId());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionAbilityLable> list(CorrectionAbilityLableParam correctionAbilityLableParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionAbilityLableParam correctionAbilityLableParam) {
        CorrectionAbilityLable correctionAbilityLable = new CorrectionAbilityLable();
        BeanUtil.copyProperties(correctionAbilityLableParam, correctionAbilityLable);
        this.save(correctionAbilityLable);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionAbilityLableParam correctionAbilityLableParam) {
        this.removeById(correctionAbilityLableParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionAbilityLableParam correctionAbilityLableParam) {
        CorrectionAbilityLable correctionAbilityLable = this.queryCorrectionAbilityLable(correctionAbilityLableParam);
        BeanUtil.copyProperties(correctionAbilityLableParam, correctionAbilityLable);
        this.updateById(correctionAbilityLable);
    }

    @Override
    public CorrectionAbilityLable detail(CorrectionAbilityLableParam correctionAbilityLableParam) {
        return this.queryCorrectionAbilityLable(correctionAbilityLableParam);
    }

    /**
     * 获取能力管理、标签关联表
     *
     * <AUTHOR>
     * @date 2022-03-03 11:00:37
     */
    private CorrectionAbilityLable queryCorrectionAbilityLable(CorrectionAbilityLableParam correctionAbilityLableParam) {
        CorrectionAbilityLable correctionAbilityLable = this.getById(correctionAbilityLableParam.getId());
        if (ObjectUtil.isNull(correctionAbilityLable)) {
            throw new ServiceException(CorrectionAbilityLableExceptionEnum.NOT_EXIST);
        }
        return correctionAbilityLable;
    }
}
