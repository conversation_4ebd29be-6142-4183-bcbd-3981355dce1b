package com.concise.gen.correctionportrayalassignment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 画像首页待处理任务
 *
 * <AUTHOR>
 * @date 2023-01-09 09:14:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_portrayal_assignment")
public class CorrectionPortrayalAssignment extends BaseEntity {

    /**
     * 
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;


    private String systemType;
    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 处理类型
     */
    private String modelName;

    /**
     * 跳转路径
     */
    private String modelUri;


    /**
     * 姓名
     */
    private String xm;

    /**
     * 处理标志（0处理、1已处理）
     */
    private Integer status;

}
