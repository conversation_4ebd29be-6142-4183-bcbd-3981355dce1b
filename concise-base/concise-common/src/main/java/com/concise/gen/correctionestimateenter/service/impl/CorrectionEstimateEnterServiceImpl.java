package com.concise.gen.correctionestimateenter.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.DateTimeUtil;
import com.concise.gen.correctionabilitymanage.entity.CorrectionAbilityManage;
import com.concise.gen.correctioncorrectplan.entity.CorrectionCorrectPlan;
import com.concise.gen.correctionestimateenter.entity.CorrectionEstimateEnter;
import com.concise.gen.correctionestimateenter.enums.CorrectionEstimateEnterExceptionEnum;
import com.concise.gen.correctionestimateenter.mapper.CorrectionEstimateEnterMapper;
import com.concise.gen.correctionestimateenter.param.CorrectionEstimateEnterParam;
import com.concise.gen.correctionestimateenter.service.CorrectionEstimateEnterService;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.gen.correctionrisklevel.entity.CorrectionRiskLevel;
import com.concise.gen.correctmoodcode.service.CorrectMoodCodeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 入矫评估service接口实现类
 *
 * <AUTHOR>
 * @date 2022-05-13 14:52:58
 */
@Service
public class CorrectionEstimateEnterServiceImpl extends ServiceImpl<CorrectionEstimateEnterMapper, CorrectionEstimateEnter> implements CorrectionEstimateEnterService {

    @Resource
    private CorrectionObjectInformationService correctionObjectInformationService;

    @Resource
    private CorrectMoodCodeService correctMoodCodeService;

    @Override
    public PageResult<CorrectionEstimateEnter> page(CorrectionEstimateEnterParam correctionEstimateEnterParam, Set<String> org) {
        QueryWrapper<CorrectionEstimateEnter> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionEstimateEnterParam)) {

            // 根据矫正机构id 查询
            queryWrapper.lambda().in(CorrectionEstimateEnter::getJzjg, org);
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateEnterParam.getSqjzryName())) {
                queryWrapper.lambda().like(CorrectionEstimateEnter::getSqjzryName, correctionEstimateEnterParam.getSqjzryName());
            }
            // 根据状态（0：暂存，1：提交） 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateEnterParam.getStatus())) {
                queryWrapper.lambda().eq(CorrectionEstimateEnter::getStatus, correctionEstimateEnterParam.getStatus());
            }
            // 根据评估日期 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateEnterParam.getEstimateTime())) {
                queryWrapper.lambda().eq(CorrectionEstimateEnter::getEstimateTime, correctionEstimateEnterParam.getEstimateTime());
            }
            // 评估时间
            if (ObjectUtil.isNotEmpty(correctionEstimateEnterParam.getEstimateTime_begin())) {
                queryWrapper.lambda().ge(CorrectionEstimateEnter::getEstimateTime, correctionEstimateEnterParam.getEstimateTime_begin());
            }
            if (ObjectUtil.isNotEmpty(correctionEstimateEnterParam.getEstimateTime_end())) {
                queryWrapper.lambda().le(CorrectionEstimateEnter::getEstimateTime, correctionEstimateEnterParam.getEstimateTime_end());
            }
        }
        queryWrapper.lambda().eq(CorrectionEstimateEnter::getDelFlag, 0);
        queryWrapper.lambda().orderByDesc(CorrectionEstimateEnter::getEstimateTime);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionEstimateEnter> list(CorrectionEstimateEnterParam correctionEstimateEnterParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionEstimateEnterParam correctionEstimateEnterParam) {
        CorrectionEstimateEnter correctionEstimateEnter = new CorrectionEstimateEnter();
        BeanUtil.copyProperties(correctionEstimateEnterParam, correctionEstimateEnter);
        this.save(correctionEstimateEnter);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionEstimateEnterParam correctionEstimateEnterParam) {
        CorrectionEstimateEnter crrectionEstimateEnter = new CorrectionEstimateEnter();
        crrectionEstimateEnter.setId(correctionEstimateEnterParam.getId());
        crrectionEstimateEnter.setDelFlag(1);
        this.updateById(crrectionEstimateEnter);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionEstimateEnterParam correctionEstimateEnterParam) {
        CorrectionEstimateEnter correctionEstimateEnter = this.queryCorrectionEstimateEnter(correctionEstimateEnterParam);
        BeanUtil.copyProperties(correctionEstimateEnterParam, correctionEstimateEnter);
        this.updateById(correctionEstimateEnter);
    }

    @Override
    public CorrectionEstimateEnter detail(CorrectionEstimateEnterParam correctionEstimateEnterParam) {
        return this.queryCorrectionEstimateEnter(correctionEstimateEnterParam);
    }

    /**
     * 获取入矫评估
     *
     * <AUTHOR>
     * @date 2022-05-13 14:52:58
     */
    private CorrectionEstimateEnter queryCorrectionEstimateEnter(CorrectionEstimateEnterParam correctionEstimateEnterParam) {
        CorrectionEstimateEnter correctionEstimateEnter = this.getById(correctionEstimateEnterParam.getId());
        if (ObjectUtil.isNull(correctionEstimateEnter)) {
            throw new ServiceException(CorrectionEstimateEnterExceptionEnum.NOT_EXIST);
        }
        return correctionEstimateEnter;
    }

    /**
     * 根据选中的矫正对象转换下拉选项的值
     *
     * <AUTHOR>
     * @date 2022-05-13 14:52:58
     */
    @Override
    public CorrectionEstimateEnter transform(CorrectionEstimateEnterParam correctionEstimateEnterParam) {
        CorrectionObjectInformation sqjzry = correctionObjectInformationService.getById(correctionEstimateEnterParam.getSqjzryId());
        CorrectionEstimateEnter correctionEstimateEnter = new CorrectionEstimateEnter();
        //判决书犯罪年龄
        if (ObjectUtil.isNotEmpty(sqjzry.getSfcn())) {
            correctionEstimateEnter.setCrimeAge("0".equals(sqjzry.getSfcn()) ? "FZSNL02" : "FZSNL01");
        }
        //受教育程度
        String whcd = sqjzry.getWhcd();
        if (ObjectUtil.isNotEmpty(whcd)) {
            if ("05".equals(whcd) || "06".equals(whcd) || "07".equals(whcd) || "08".equals(whcd)) {
                correctionEstimateEnter.setEducationLevel("SJYCD01"); //大专及以上
            } else if ("03".equals(whcd) || "04".equals(whcd) || "09".equals(whcd)) {
                correctionEstimateEnter.setEducationLevel("SJYCD02"); //高中、初中及同等程度
            } else if ("01".equals(whcd) || "02".equals(whcd)) {
                correctionEstimateEnter.setEducationLevel("SJYCD03"); //小学、半文盲、文盲
            }
        }
        //婚姻家庭状况
        int age = DateTimeUtil.getDayNum(sqjzry.getCsrq(), new Date()) / 365; //年龄
        String hyzk = sqjzry.getHyzk();
        if ("02".equals(hyzk) || ("01".equals(hyzk) && 25 > age)) {
            correctionEstimateEnter.setMarriageFamily("HYJTZK01"); //已婚或25周岁以下未婚
        }
        if ("03".equals(hyzk) || "04".equals(hyzk) || ("01".equals(hyzk) && 25 < age)) {
            correctionEstimateEnter.setMarriageFamily("HYJTZK02"); //丧偶、离异、大龄未婚（25周岁以上）
        }
        //心理健康状况
        String moodCode = correctMoodCodeService.getMoodCode(sqjzry.getId());
        if (ObjectUtil.isNotEmpty(moodCode)) {
            if ("4".equals(moodCode) || "5".equals(moodCode)) {
                correctionEstimateEnter.setPsychicHealth("XLJKZK01");
            } else if ("3".equals(moodCode) || "2".equals(moodCode)) {
                correctionEstimateEnter.setPsychicHealth("XLJKZK02");
            } else if ("1".equals(moodCode)) {
                correctionEstimateEnter.setPsychicHealth("XLJKZK03");
            }
        }
        //有精神病史或精神病遗传史
        if (ObjectUtil.isNotEmpty(sqjzry.getSfyjsb())) {
            correctionEstimateEnter.setMentalDisease("0".equals(sqjzry.getSfyjsb()) ? "JSBS01" : "JSBS02");
        }
        correctionEstimateEnter.setCrimeReason("");//违法犯罪案由
        //社区矫正类别
        String jzlb = sqjzry.getJzlb();
        if (ObjectUtil.isNotEmpty(jzlb)) {
            if ("01".equals(jzlb) || "04".equals(jzlb)) {
                correctionEstimateEnter.setCorrectType("SQJZLB01"); // 管制、监外执行
            }
            if ("02".equals(jzlb) || "03".equals(jzlb)) {
                correctionEstimateEnter.setCorrectType("SQJZLB02"); // 缓刑、假释
            }
        }
        //累犯惯犯
        if (ObjectUtil.isNotEmpty(sqjzry.getSflf())) {
            correctionEstimateEnter.setOldLag("0".equals(sqjzry.getSflf()) ? "LFGF01" : "LFGF02");
        }
        // correctionEstimateEnter.setMoreCrime();//是否数罪并罚
        // correctionEstimateEnter.setTogetherCrime();//是否共同犯罪
        //是否五毒
        if (ObjectUtil.isNotEmpty(sqjzry.getSfwd())) {
            correctionEstimateEnter.setFivePoisons("0".equals(sqjzry.getSfwd()) ? "SFWD01" : "SFWD02");
        }
        //是否五涉
        if (ObjectUtil.isNotEmpty(sqjzry.getSfws())) {
            correctionEstimateEnter.setFiveInvolvement("0".equals(sqjzry.getSfws()) ? "SFWS01" : "SFWS02");
        }
        //是否四史
        if (ObjectUtil.isNotEmpty(sqjzry.getSfyss())) {
            correctionEstimateEnter.setFourFamous("5".equals(sqjzry.getSfyss()) ? "SFSS01" : "SFSS02");
        }
        // correctionEstimateEnter.setAccessoryPunishment();//附加刑及罚金
        return correctionEstimateEnter;
    }

    @Override
    public String getPercent(String sqjzryId) {
        try {
            QueryWrapper<CorrectionEstimateEnter> queryWrapper = new QueryWrapper<CorrectionEstimateEnter>();
            queryWrapper.lambda().eq(CorrectionEstimateEnter::getSqjzryId, sqjzryId);
            queryWrapper.lambda().eq(CorrectionEstimateEnter::getDelFlag, 0);
            queryWrapper.select("score_total, score_estimate");
            List<CorrectionEstimateEnter> list = this.list(queryWrapper);
            if (list.size() > 0) {
                CorrectionEstimateEnter correctionEstimateEnter = list.get(0);
                BigDecimal totalScore = correctionEstimateEnter.getScoreTotal();
                BigDecimal scoreEstimate = correctionEstimateEnter.getScoreEstimate();
                double val = scoreEstimate.doubleValue() / totalScore.doubleValue();
                DecimalFormat df = new DecimalFormat("0.00");
                return df.format(String.valueOf(val));
            } else {
                return "0";
            }
        } catch(Exception e) {
            log.error("getPercent error: " + e.getMessage());
            return "0";
        }
    }
}
