package com.concise.gen.correctionestimatereportdetail.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.base.entity.BaseEntity;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimatereportdetail.entity.CorrectionEstimateReportDetail;
import com.concise.gen.correctionestimatereportdetail.enums.CorrectionEstimateReportDetailExceptionEnum;
import com.concise.gen.correctionestimatereportdetail.mapper.CorrectionEstimateReportDetailMapper;
import com.concise.gen.correctionestimatereportdetail.param.CorrectionEstimateReportDetailParam;
import com.concise.gen.correctionestimatereportdetail.service.CorrectionEstimateReportDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 评估报告详情service接口实现类
 *
 * <AUTHOR>
 * @date 2023-01-10 15:55:35
 */
@Service
public class CorrectionEstimateReportDetailServiceImpl extends ServiceImpl<CorrectionEstimateReportDetailMapper, CorrectionEstimateReportDetail> implements CorrectionEstimateReportDetailService {

    @Override
    public PageResult<CorrectionEstimateReportDetail> page(CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam) {
        QueryWrapper<CorrectionEstimateReportDetail> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionEstimateReportDetailParam)) {

            // 根据评估报告id 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportDetailParam.getReportId())) {
                queryWrapper.lambda().eq(CorrectionEstimateReportDetail::getReportId, correctionEstimateReportDetailParam.getReportId());
            }
            // 根据指标 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportDetailParam.getIndex())) {
                queryWrapper.lambda().eq(CorrectionEstimateReportDetail::getIndexType, correctionEstimateReportDetailParam.getIndex());
            }
            // 根据指标名称 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportDetailParam.getIndexName())) {
                queryWrapper.lambda().eq(CorrectionEstimateReportDetail::getIndexName, correctionEstimateReportDetailParam.getIndexName());
            }
            // 根据当前分数 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportDetailParam.getScore())) {
                queryWrapper.lambda().eq(CorrectionEstimateReportDetail::getScore, correctionEstimateReportDetailParam.getScore());
            }
            // 根据情况描述 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportDetailParam.getCondition())) {
                queryWrapper.lambda().eq(CorrectionEstimateReportDetail::getConditions, correctionEstimateReportDetailParam.getCondition());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionEstimateReportDetail> list(CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam) {
        CorrectionEstimateReportDetail correctionEstimateReportDetail = new CorrectionEstimateReportDetail();
        BeanUtil.copyProperties(correctionEstimateReportDetailParam, correctionEstimateReportDetail);
        this.save(correctionEstimateReportDetail);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam) {
        this.removeById(correctionEstimateReportDetailParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam) {
        CorrectionEstimateReportDetail correctionEstimateReportDetail = this.queryCorrectionEstimateReportDetail(correctionEstimateReportDetailParam);
        BeanUtil.copyProperties(correctionEstimateReportDetailParam, correctionEstimateReportDetail);
        this.updateById(correctionEstimateReportDetail);
    }

    @Override
    public CorrectionEstimateReportDetail detail(CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam) {
        return this.queryCorrectionEstimateReportDetail(correctionEstimateReportDetailParam);
    }

    /**
     * 获取评估报告详情
     *
     * <AUTHOR>
     * @date 2023-01-10 15:55:35
     */
    private CorrectionEstimateReportDetail queryCorrectionEstimateReportDetail(CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam) {
        CorrectionEstimateReportDetail correctionEstimateReportDetail = this.getById(correctionEstimateReportDetailParam.getId());
        if (ObjectUtil.isNull(correctionEstimateReportDetail)) {
            throw new ServiceException(CorrectionEstimateReportDetailExceptionEnum.NOT_EXIST);
        }
        return correctionEstimateReportDetail;
    }

    @Override
    public List<CorrectionEstimateReportDetail> listByReportId(List<String> reportId) {
        LambdaQueryWrapper<CorrectionEstimateReportDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CorrectionEstimateReportDetail::getReportId, reportId);
        lambdaQueryWrapper.orderByAsc(CorrectionEstimateReportDetail::getCreateTime);
        return list(lambdaQueryWrapper);
    }
}
