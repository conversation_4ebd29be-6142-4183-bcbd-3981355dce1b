package com.concise.gen.correctioncorrectplan.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionabilitymanage.entity.CorrectionAbilityManage;
import com.concise.gen.correctionabilitymanage.mapper.CorrectionAbilityManageMapper;
import com.concise.gen.correctioncorrectplan.entity.CorrectionCorrectPlan;
import com.concise.gen.correctioncorrectplan.enums.CorrectionCorrectPlanExceptionEnum;
import com.concise.gen.correctioncorrectplan.mapper.CorrectionCorrectPlanMapper;
import com.concise.gen.correctioncorrectplan.param.CorrectionCorrectPlanParam;
import com.concise.gen.correctioncorrectplan.service.CorrectionCorrectPlanService;
import com.concise.gen.correctionlabelcorrpsn.mapper.CorrectionLabelCorrpsnMapper;
import com.concise.gen.correctionobjectinformation.service.CorrectionObjectInformationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 矫正方案service接口实现类
 *
 * <AUTHOR>
 * @date 2022-03-07 14:27:46
 */
@Service
public class CorrectionCorrectPlanServiceImpl extends ServiceImpl<CorrectionCorrectPlanMapper, CorrectionCorrectPlan> implements CorrectionCorrectPlanService {

    @Resource
    private CorrectionLabelCorrpsnMapper correctionLabelCorrpsnMapper;

    @Resource
    private CorrectionAbilityManageMapper correctionAbilityManageMapper;

    @Resource
    private CorrectionObjectInformationService correctionObjectInformationService;

    @Override
    public PageResult<CorrectionCorrectPlan> page(CorrectionCorrectPlanParam correctionCorrectPlanParam, Set<String> org) {
        QueryWrapper<CorrectionCorrectPlan> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionCorrectPlanParam)) {
            // 根据矫正机构id 查询
            queryWrapper.lambda().in(CorrectionCorrectPlan::getJzjg, org);

            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionCorrectPlanParam.getSqjzryName())) {
                queryWrapper.lambda().like(CorrectionCorrectPlan::getSqjzryName, correctionCorrectPlanParam.getSqjzryName());
            }

            // 创建日期(方案生成时间)
            if (ObjectUtil.isNotEmpty(correctionCorrectPlanParam.getCreateTime_begin())) {
                queryWrapper.lambda().ge(CorrectionCorrectPlan::getCreateTime, correctionCorrectPlanParam.getCreateTime_begin());
            }
            if (ObjectUtil.isNotEmpty(correctionCorrectPlanParam.getCreateTime_end())) {
                queryWrapper.lambda().le(CorrectionCorrectPlan::getCreateTime, correctionCorrectPlanParam.getCreateTime_end());
            }

            // 调整时间
            if (ObjectUtil.isNotEmpty(correctionCorrectPlanParam.getAdjustTime_begin())) {
                queryWrapper.lambda().ge(CorrectionCorrectPlan::getAdjustTime, correctionCorrectPlanParam.getAdjustTime_begin());
            }
            if (ObjectUtil.isNotEmpty(correctionCorrectPlanParam.getAdjustTime_end())) {
                queryWrapper.lambda().le(CorrectionCorrectPlan::getAdjustTime, correctionCorrectPlanParam.getAdjustTime_end());
            }

            //方案状态
            if (ObjectUtil.isNotEmpty(correctionCorrectPlanParam.getPlanStatus())) {
                queryWrapper.lambda().eq(CorrectionCorrectPlan::getPlanStatus, correctionCorrectPlanParam.getPlanStatus());
            }
        }
        queryWrapper.lambda().eq(CorrectionCorrectPlan::getDelFlag, 0);
        queryWrapper.orderByDesc("create_time");
        return new PageResult<>(this.baseMapper.pageList(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public PageResult<CorrectionCorrectPlan> pagePlan(CorrectionCorrectPlanParam param, Set<String> org) {
        QueryWrapper<CorrectionCorrectPlan> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("cpp.jzjg", org);
        Page<CorrectionCorrectPlan> pageRs = this.baseMapper.page(PageFactory.defaultPage(), param.getSqjzryName(), param.getTag(), queryWrapper);
        // 计算矫正阶段
        List<CorrectionCorrectPlan> list = new ArrayList<>();
        for (CorrectionCorrectPlan plan : pageRs.getRecords()) {
            plan.setPhase(correctionObjectInformationService.getCorrectPhase(plan.getSqjzryId()));
            list.add(plan);
        }
        pageRs.setRecords(list);
        return new PageResult<>(pageRs);
    }

    @Override
    public List<CorrectionCorrectPlan> list(CorrectionCorrectPlanParam correctionCorrectPlanParam) {
        QueryWrapper<CorrectionCorrectPlan> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionCorrectPlanParam)) {
            // 根据矫正对象id 查询
            if (ObjectUtil.isNotEmpty(correctionCorrectPlanParam.getSqjzryId())) {
                queryWrapper.lambda().eq(CorrectionCorrectPlan::getSqjzryId, correctionCorrectPlanParam.getSqjzryId());
            }
        }
        queryWrapper.orderByAsc("create_time");
        return this.list(queryWrapper);
    }

    @Override
    public void add(CorrectionCorrectPlanParam correctionCorrectPlanParam) {
        CorrectionCorrectPlan correctionCorrectPlan = new CorrectionCorrectPlan();
        BeanUtil.copyProperties(correctionCorrectPlanParam, correctionCorrectPlan);
        this.save(correctionCorrectPlan);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionCorrectPlanParam correctionCorrectPlanParam) {
        this.removeById(correctionCorrectPlanParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionCorrectPlanParam correctionCorrectPlanParam) {
        CorrectionCorrectPlan correctionCorrectPlan = this.queryCorrectionCorrectPlan(correctionCorrectPlanParam);
        BeanUtil.copyProperties(correctionCorrectPlanParam, correctionCorrectPlan);
        if (null != correctionCorrectPlan.getPlanStatus() && 2 == correctionCorrectPlan.getPlanStatus()) {
            //解矫总结
            this.updateById(correctionCorrectPlan);
        } else {
            // 如果是入矫制定的一键完善则需要更新原有数据
            if (1 == correctionCorrectPlan.getDelFlag()) {
                this.updateById(correctionCorrectPlan);
            }
            //新增一条
            correctionCorrectPlan.setId(null);
            correctionCorrectPlan.setPlanStatus(1);
            correctionCorrectPlan.setDelFlag(0);
            correctionCorrectPlan.setCreateTime(new Date());
            this.save(correctionCorrectPlan);
        }
    }

    @Override
    public CorrectionCorrectPlan detail(CorrectionCorrectPlanParam correctionCorrectPlanParam) {
        return this.queryCorrectionCorrectPlan(correctionCorrectPlanParam);
    }

    /**
     * 获取矫正方案
     *
     * <AUTHOR>
     * @date 2022-03-07 14:27:46
     */
    private CorrectionCorrectPlan queryCorrectionCorrectPlan(CorrectionCorrectPlanParam correctionCorrectPlanParam) {
        CorrectionCorrectPlan correctionCorrectPlan = this.getById(correctionCorrectPlanParam.getId());
        if (ObjectUtil.isNull(correctionCorrectPlan)) {
            throw new ServiceException(CorrectionCorrectPlanExceptionEnum.NOT_EXIST);
        }
        return correctionCorrectPlan;
    }

    @Override
    public int getNum(String sqjzryId) {
        QueryWrapper<CorrectionCorrectPlan> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(CorrectionCorrectPlan::getSqjzryId, sqjzryId);
        // queryWrapper.lambda().in(CorrectionCorrectPlan::getPlanStatus, planStatus);
        queryWrapper.lambda().in(CorrectionCorrectPlan::getDelFlag, 0);
        return count(queryWrapper);
    }

    /**
     * 一键完善---生成矫正方案措施
     *
     * @param sqjzryId
     * @return
     */
    @Override
    public String createPlan(String sqjzryId, String correctPhase) {
        // 参数校验
        if (ObjectUtil.isEmpty(sqjzryId)) {
            return "";
        }

        // 矫正方案（日常监管措施 + 教育帮扶措施）
        String resStr = "";
        // 日常监管措施
        String rcjgStr = "";
        // 教育帮扶措施
        String jybfStr = "";
        
        try {
            // 查询矫正对象的标签
            List<String> labelList = correctionLabelCorrpsnMapper.findLabelIds(sqjzryId);
            if (CollectionUtil.isEmpty(labelList)) {
                // 若矫正对象未绑定标签则直接返回空字符串
                return "";
            }
            
            String sqjzryLabelIds = String.join(",", labelList);
            
            // 获取矫正阶段
            int phase = correctionObjectInformationService.getCorrectPhase(sqjzryId);
            
            // 根据入矫时间计算，若入矫一个月内的则查询入矫初期的能力，矫正结束时间1个月内则是矫正末期， 反之则是中期
            List<CorrectionAbilityManage> abilityList = correctionAbilityManageMapper.findByCorrectPhase(phase);
            if (CollectionUtil.isEmpty(abilityList)) {
                return "";
            }
            
            for (CorrectionAbilityManage ability : abilityList) {
                if (ability == null) {
                    continue;
                }
                
                String abilityLabelIds = ability.getLabelIds();
                if (ObjectUtil.isEmpty(abilityLabelIds)) {
                    continue;
                }
                
                // 方案是否适用标志位， true为适用
                boolean tag = true;
                String[] abilityLabelIdArray = abilityLabelIds.split(",");
                
                for (String abilityLabelId : abilityLabelIdArray) {
                    if (ObjectUtil.isEmpty(abilityLabelId) || !sqjzryLabelIds.contains(abilityLabelId)) {
                        tag = false;
                        break;
                    }
                }
                
                if (tag) {
                    //如果匹配则将方案对应的标签从矫正对象标签中移除，防止方案重复使用
                    for (String abilityLabelId : abilityLabelIdArray) {
                        if (ObjectUtil.isNotEmpty(abilityLabelId)) {
                            sqjzryLabelIds = sqjzryLabelIds.replace(abilityLabelId + ",", "").replace(abilityLabelId, "");
                        }
                    }
                    // 将内容添加到方案
                    if (ObjectUtil.isNotEmpty(ability.getLabelNames())) {
                        if (ObjectUtil.isNotEmpty(ability.getDailySupervisionStep())) {
                            rcjgStr += "【" + ability.getLabelNames() + "】" + ability.getDailySupervisionStep() + "\n";
                        }
                        if (ObjectUtil.isNotEmpty(ability.getEducationalAssistanceStep())) {
                            jybfStr += "【" + ability.getLabelNames() + "】" + ability.getEducationalAssistanceStep() + "\n";
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("生成矫正方案出错，sqjzryId=" + sqjzryId, e);
            return "";
        }
//        if (ObjectUtil.isNotEmpty(rcjgStr)) {
//            resStr = "【日常监管措施】：\n" + rcjgStr;
//        }
        if (ObjectUtil.isNotEmpty(jybfStr)) {
            if (ObjectUtil.isNotEmpty(rcjgStr)) {
                resStr += "\n【教育帮扶措施】\n" + jybfStr;
            } else {
                resStr += "【教育帮扶措施】\n" + jybfStr;
            }
        }
        return resStr;
    }

}
