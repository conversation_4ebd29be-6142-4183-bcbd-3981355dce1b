package com.concise.gen.correctionestimatereport.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimateportrayal.entity.CorrectionEstimatePortrayal;
import com.concise.gen.correctionestimatereport.dto.HistoryRisksDTO;
import com.concise.gen.correctionestimatereport.dto.ReportDetailDTO;
import com.concise.gen.correctionestimatereport.dto.ReportDetailRiskDTO;
import com.concise.gen.correctionestimatereport.dto.TrendDTO;
import com.concise.gen.correctionestimatereport.entity.CorrectionEstimateReport;
import com.concise.gen.correctionestimatereport.enums.CorrectionEstimateReportExceptionEnum;
import com.concise.gen.correctionestimatereport.mapper.CorrectionEstimateReportMapper;
import com.concise.gen.correctionestimatereport.param.CorrectionEstimateReportParam;
import com.concise.gen.correctionestimatereport.service.CorrectionEstimateReportService;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformationNoEncryption;
import com.concise.gen.correctionobjectinformation.mapper.CorrectionObjectInformationMapper;
import com.concise.gen.correctionobjectinformation.service.CorrectionObjectInformationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 评估报告service接口实现类
 *
 * <AUTHOR>
 * @date 2023-01-09 10:57:38
 */
@Service
@Slf4j
public class CorrectionEstimateReportServiceImpl extends ServiceImpl<CorrectionEstimateReportMapper, CorrectionEstimateReport> implements CorrectionEstimateReportService {

    @Autowired
    private CorrectionEstimateReportMapper correctionEstimateReportMapper;

    @Autowired
    private CorrectionObjectInformationMapper correctionObjectInformationMapper;

    @Autowired
    private CorrectionObjectInformationService correctionObjectInformationService;

    @Override
    public PageResult<CorrectionEstimateReport> page(CorrectionEstimateReportParam correctionEstimateReportParam,Set<String> org) {
        QueryWrapper<CorrectionEstimateReport> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(CorrectionEstimateReport::getId, CorrectionEstimateReport::getSqjzryName,CorrectionEstimateReport::getEstimateMonth ,CorrectionEstimateReport::getJzjgName, CorrectionEstimateReport::getEnterScore, CorrectionEstimateReport::getLastMonthScore, CorrectionEstimateReport::getScoreEstimate, CorrectionEstimateReport::getLevel);
        if (ObjectUtil.isNotNull(correctionEstimateReportParam)) {
            // 根据矫正机构id 查询
            queryWrapper.lambda().in(CorrectionEstimateReport::getJzjg, org);
            // 根据风险等级 查询
            queryWrapper.lambda().eq(ObjectUtil.isNotEmpty(correctionEstimateReportParam.getRiskLevel()),CorrectionEstimateReport::getLevel,correctionEstimateReportParam.getRiskLevel());
            // 根据矫正对象id 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getSqjzryId())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getSqjzryId, correctionEstimateReportParam.getSqjzryId());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getSqjzryName())) {
                queryWrapper.lambda().like(CorrectionEstimateReport::getSqjzryName, correctionEstimateReportParam.getSqjzryName());
            }
            // 根据 性别 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getXb())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getXb, correctionEstimateReportParam.getXb());
            }
            // 根据性别中文值 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getXbName())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getXbName, correctionEstimateReportParam.getXbName());
            }
            // 根据身份证号 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getSfzh())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getSfzh, correctionEstimateReportParam.getSfzh());
            }
            // 根据年龄 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getAge())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getAge, correctionEstimateReportParam.getAge());
            }
            // 根据是否成年 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getSfcn())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getSfcn, correctionEstimateReportParam.getSfcn());
            }
            // 根据婚姻状况 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getHyzk())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getHyzk, correctionEstimateReportParam.getHyzk());
            }
            // 根据婚姻状况中文值 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getHyzkName())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getHyzkName, correctionEstimateReportParam.getHyzkName());
            }
            // 根据文化程度 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getWhcd())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getWhcd, correctionEstimateReportParam.getWhcd());
            }
            // 根据文化程度中文值 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getWhcdName())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getWhcdName, correctionEstimateReportParam.getWhcdName());
            }
//            // 根据矫正机构ID 查询
//            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getJzjg())) {
//                queryWrapper.lambda().eq(CorrectionEstimateReport::getJzjg, correctionEstimateReportParam.getJzjg());
//            }
            // 根据矫正机构名称 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getJzjgName())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getJzjgName, correctionEstimateReportParam.getJzjgName());
            }
            // 根据入娇评估分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getEnterScore())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getEnterScore, correctionEstimateReportParam.getEnterScore());
            }
            // 根据上月评估分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getLastMonthScore())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getLastMonthScore, correctionEstimateReportParam.getLastMonthScore());
            }
            // 根据本月评估分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getScoreEstimate())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getScoreEstimate, correctionEstimateReportParam.getScoreEstimate());
            }
            // 根据评估月份 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getEstimateMonth())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getEstimateMonth, correctionEstimateReportParam.getEstimateMonth());
            }
            // 根据矫正类别 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getJzlb())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getJzlb, correctionEstimateReportParam.getJzlb());
            }
            // 根据矫正类别中文值 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getJzlbName())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getJzlbName, correctionEstimateReportParam.getJzlbName());
            }
            // 根据是否成年中文值 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getSfcnName())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getSfcnName, correctionEstimateReportParam.getSfcnName());
            }
            // 根据就业就学情况 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getJyjxqk())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getJyjxqk, correctionEstimateReportParam.getJyjxqk());
            }
            // 根据就业就学情况中文值 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getJyjxqkName())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getJyjxqkName, correctionEstimateReportParam.getJyjxqkName());
            }
            // 根据具体罪名 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getJtzm())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getJtzm, correctionEstimateReportParam.getJtzm());
            }
            // 根据具体罪名中文值 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getJtzmName())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getJtzmName, correctionEstimateReportParam.getJtzmName());
            }
            // 根据是否删除（0：未删除，1删除） 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateReportParam.getDelFlag())) {
                queryWrapper.lambda().eq(CorrectionEstimateReport::getDelFlag, correctionEstimateReportParam.getDelFlag());
            }
        }
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMM");
        int month = Integer.parseInt(sf.format(new Date()));
        queryWrapper.lambda().eq(CorrectionEstimateReport::getEstimateMonth, month);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionEstimateReport> list(CorrectionEstimateReportParam correctionEstimateReportParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionEstimateReportParam correctionEstimateReportParam) {
        CorrectionEstimateReport correctionEstimateReport = new CorrectionEstimateReport();
        BeanUtil.copyProperties(correctionEstimateReportParam, correctionEstimateReport);
        this.save(correctionEstimateReport);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionEstimateReportParam correctionEstimateReportParam) {
        this.removeById(correctionEstimateReportParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionEstimateReportParam correctionEstimateReportParam) {
        CorrectionEstimateReport correctionEstimateReport = this.queryCorrectionEstimateReport(correctionEstimateReportParam);
        BeanUtil.copyProperties(correctionEstimateReportParam, correctionEstimateReport);
        this.updateById(correctionEstimateReport);
    }


    @Override
    public ReportDetailDTO detail(CorrectionEstimateReportParam correctionEstimateReportParam) {
        CorrectionEstimateReport correctionEstimateReport = this.queryCorrectionEstimateReport(correctionEstimateReportParam);
        int startMonth = Integer.parseInt(DateUtil.offsetMonth(DateUtil.parse(correctionEstimateReport.getEstimateMonth().toString(), "yyyyMM"), -5).toString("yyyyMM"));
        List<CorrectionEstimateReport> reports = correctionEstimateReportMapper.getIdList(correctionEstimateReport.getSqjzryId(), startMonth, correctionEstimateReport.getEstimateMonth());
        CorrectionEstimateReport lastReport = reports.size() > 1 ? reports.get(1) : null;
        ReportDetailDTO reportDetailDTO = new ReportDetailDTO();
        List<ReportDetailRiskDTO> historyRisks = new ArrayList<>();
        Map<String, HistoryRisksDTO> historyRisksMap = new HashMap<>();
        reportDetailDTO.setBaseInfo(correctionEstimateReport);
        reportDetailDTO.setCurrentRisks(ObjectUtil.isNotNull(correctionEstimateReport) ? JSON.parseArray(correctionEstimateReport.getRisks(), ReportDetailRiskDTO.class) : null);
        reportDetailDTO.setLastRisks(ObjectUtil.isNotNull(lastReport) ? JSON.parseArray(lastReport.getRisks(), ReportDetailRiskDTO.class) : null);
        List<String> xAxis = new ArrayList<>();
        List<String> yAxis = new ArrayList<>();
        //构建风险趋势和合并历史风险
        for (CorrectionEstimateReport report : reports) {
            xAxis.add(report.getEstimateMonth().toString());
            yAxis.add(report.getScoreEstimate().toString());
            if (StringUtils.isNotBlank(report.getRisks())) {
                List<ReportDetailRiskDTO> reportDetailRiskDTOS = JSON.parseArray(report.getRisks(), ReportDetailRiskDTO.class);
                for (ReportDetailRiskDTO reportDetailRiskDTO : reportDetailRiskDTOS) {
                    HistoryRisksDTO historyRisksDTO = historyRisksMap.get(reportDetailRiskDTO.getIndexName());
                    if (historyRisksDTO == null) {
                        historyRisksDTO = new HistoryRisksDTO();
                        historyRisksDTO.setIndexName(reportDetailRiskDTO.getIndexName());
                        historyRisksDTO.setScore(reportDetailRiskDTO.getScore());
                        historyRisksDTO.setCount(1);
                        historyRisksDTO.setConditions(reportDetailRiskDTO.getConditions());
                    } else {
                        int count = historyRisksDTO.getCount() + 1;
                        BigDecimal totalSore = historyRisksDTO.getScore().add(reportDetailRiskDTO.getScore());
                        historyRisksDTO.setScore(totalSore.divide(new BigDecimal(count),BigDecimal.ROUND_CEILING));
                        historyRisksDTO.setCount(count);
                        List<String> conditions = historyRisksDTO.getConditions();
                        conditions.addAll(reportDetailRiskDTO.getConditions());
                        historyRisksDTO.setConditions(conditions);
                    }
                    historyRisksMap.put(reportDetailRiskDTO.getIndexName(), historyRisksDTO);
                }
            }
        }
        historyRisks.addAll(historyRisksMap.values());
        reportDetailDTO.setHistoryRisks(historyRisks);
        TrendDTO trendDTO = new TrendDTO();
        trendDTO.setXAxis(xAxis);
        trendDTO.setYAxis(yAxis);
        reportDetailDTO.setRisksTrend(trendDTO);
        reportDetailDTO.setCurrentLevel(correctionEstimateReport.getLevel());
        reportDetailDTO.setLastLevel(lastReport == null ? null : lastReport.getLevel());
        return reportDetailDTO;
    }

    /**
     * 获取评估报告
     *
     * <AUTHOR>
     * @date 2023-01-09 10:57:38
     */
    private CorrectionEstimateReport queryCorrectionEstimateReport(CorrectionEstimateReportParam correctionEstimateReportParam) {
        CorrectionEstimateReport correctionEstimateReport = this.getById(correctionEstimateReportParam.getId());
        if (ObjectUtil.isNull(correctionEstimateReport)) {
            throw new ServiceException(CorrectionEstimateReportExceptionEnum.NOT_EXIST);
        }
        return correctionEstimateReport;
    }

    @Override
    public void initCorrectionEstimateReport() {
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMM");
        int month = Integer.parseInt(sf.format(new Date()));
        CorrectionEstimateReport report;
        List<CorrectionObjectInformationNoEncryption> list = correctionObjectInformationMapper.getCorrectionMultifieldNoEncryption();
        //去除本月要解矫的
        //  List<CorrectionObjectInformationNoEncryption> first = list.stream().filter(item -> item.getSqjzjsrq() != null && Integer.parseInt(sf.format(item.getSqjzjsrq())) > month).collect(Collectors.toList());
        for (CorrectionObjectInformationNoEncryption correctionObjectInformation : list) {
            report = new CorrectionEstimateReport();
            BeanUtil.copyProperties(correctionObjectInformation, report);
            report.setSqjzryId(correctionObjectInformation.getId());
            report.setSqjzryName(correctionObjectInformation.getXm());
            report.setEstimateMonth(month);
            report.setId(null);
            //年龄
            try {
                report.setAge(IdcardUtil.getAgeByIdCard(correctionObjectInformation.getSfzh()));
            } catch (Exception e) {
                log.error("身份证号获取不到年龄{}", correctionObjectInformation.getSfzh());
            }
            this.save(report);

        }
    }

    @Override
    public void updateCorrectionEstimateReport(CorrectionEstimateReport report) {
        LambdaQueryWrapper<CorrectionEstimateReport> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CorrectionEstimateReport::getSqjzryId,report.getSqjzryId());
        lambdaQueryWrapper.eq(CorrectionEstimateReport::getEstimateMonth,report.getEstimateMonth());
        CorrectionEstimateReport one = getOne(lambdaQueryWrapper);
        if (one==null){
            CorrectionObjectInformationNoEncryption correctionObjectInformation = correctionObjectInformationMapper.selectByIdNoEncryption(report.getSqjzryId());
            one = new CorrectionEstimateReport();
            BeanUtil.copyProperties(correctionObjectInformation, one);
            one.setSqjzryId(correctionObjectInformation.getId());
            one.setSqjzryName(correctionObjectInformation.getXm());
            one.setEstimateMonth(report.getEstimateMonth());
            one.setId(null);
            one.setScoreEstimate(report.getScoreEstimate());
            one.setRisks(report.getRisks());
            //年龄
            try {
                one.setAge(IdcardUtil.getAgeByIdCard(correctionObjectInformation.getSfzh()));
            } catch (Exception e) {
                log.error("身份证号获取不到年龄{}", correctionObjectInformation.getSfzh());
            }
            this.save(one);
        }else {
            one.setScoreEstimate(report.getScoreEstimate());
            one.setRisks(report.getRisks());
            updateById(one);
        }

    }


    public static void main(String[] args) {
        System.out.println(DateUtil.offsetMonth(DateUtil.parse("202206", "yyyyMM"), -6).toString("yyyyMM"));
    }
}
