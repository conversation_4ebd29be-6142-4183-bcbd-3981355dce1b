package com.concise.gen.correctionlabelmanage.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.correctionlabelmanage.entity.CorrectionLabelManage;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
* 标签管理参数类
 *
 * <AUTHOR>
 * @date 2022-03-03 09:48:05
*/
@Data
public class CorrectionLabelManageParam extends BaseParam {

    /**
     * 主键id
     */
    private String id;

    /**
     * 标签
     */
    @NotBlank(message = "标签不能为空，请检查label参数", groups = {add.class, edit.class})
    private String label;

    /**
     * 标签属性
     */
    @NotBlank(message = "标签属性不能为空，请检查labelAttribute参数", groups = {add.class, edit.class})
    private String labelAttribute;

    /**
     * 标签属性字典码
     */
    private String labelAttributeCode;

    /**
     * 属性分类
     */
    @NotBlank(message = "属性分类不能为空，请检查attributeType参数", groups = {add.class, edit.class})
    private String attributeType;

    /**
     * 属性分类字典码
     */
    private String attributeTypeCode;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 关联矫正对象数量
     */
    private String sqjzryNum;

    /**
     * 关联字典值
     */
    private String dictNum;

    /**
     * 标签ids
     */
    private String[] idArr;

    /**
     * 所属系统_多个系统间逗号隔开
     */
    private String belongSys;

    /**
     * 电话核查优先级
     */
    private int orderIndex;

    /**
     * 标签集合
     */
    private List<CorrectionLabelManage> labelList;

    /**
     * 树的层级
     */
    private Integer level;

    /**
     * 上级id
     */
    private String parentId;
}
