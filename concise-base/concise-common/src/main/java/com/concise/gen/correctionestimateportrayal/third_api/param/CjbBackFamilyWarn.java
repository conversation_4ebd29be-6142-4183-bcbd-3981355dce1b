package com.concise.gen.correctionestimateportrayal.third_api.param;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 纯净版-回归家庭管理-预警
 * @Author: LiuQC
 * @Date:   2023-01-06
 * @Version: V1.0
 */
@Data
@ApiModel(value="cjb_back_family_warn对象", description="纯净版-回归家庭管理-预警")
public class CjbBackFamilyWarn {
    
	/**id*/
    @ApiModelProperty(value = "id")
	private String id;
	/**预警内容*/
    @ApiModelProperty(value = "预警内容")
	private String warnContent;
	/**预警类型*/
    @ApiModelProperty(value = "预警类型")
	private String warnType;
	/**预警类型*/
    @ApiModelProperty(value = "预警类型")
	private String warnTypeText;
	/**预警等级*/
    @ApiModelProperty(value = "预警等级")
	private String warnLevel;
	/**预警时间*/
	private Date warnTime;
	/**通知人员id*/
    @ApiModelProperty(value = "通知人员id")
	private String notifyUserId;
	/**通知人员姓名*/
    @ApiModelProperty(value = "通知人员姓名")
	private String notifyUserName;
	/**通知人员姓名*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "通知人员姓名")
	private Date notifyTime;
	/**矫正对象姓名*/
    @ApiModelProperty(value = "矫正对象姓名")
	private String userName;
	/**用户id*/
    @ApiModelProperty(value = "用户id")
	private String userId;
	/**部门id*/
    @ApiModelProperty(value = "部门id")
	private String userDeptId;
	/**部门名称*/
    @ApiModelProperty(value = "部门名称")
	private String userDeptName;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
	private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
	private Date createTime;
	/**updateBy*/
    @ApiModelProperty(value = "updateBy")
	private String updateBy;
	/**更新时间*/
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
	private Date updateTime;
}
