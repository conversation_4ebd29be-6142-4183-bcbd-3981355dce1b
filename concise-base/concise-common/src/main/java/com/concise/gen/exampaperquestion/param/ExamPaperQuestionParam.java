package com.concise.gen.exampaperquestion.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 量表题目内容参数类
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:05
*/
@Data
public class ExamPaperQuestionParam extends BaseParam {

    /**
     * 
     */
    @NotNull(message = "不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 量表id
     */
    @NotBlank(message = "量表id不能为空，请检查paperId参数", groups = {add.class, edit.class})
    private String paperId;

    /**
     * 题目id
     */
    @NotBlank(message = "题目id不能为空，请检查questionId参数", groups = {add.class, edit.class})
    private String questionId;

}
