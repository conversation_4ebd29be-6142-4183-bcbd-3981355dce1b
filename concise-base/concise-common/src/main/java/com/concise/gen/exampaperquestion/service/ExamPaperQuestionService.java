package com.concise.gen.exampaperquestion.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.exampaperquestion.entity.ExamPaperQuestion;
import com.concise.gen.exampaperquestion.param.ExamPaperQuestionParam;
import java.util.List;

/**
 * 量表题目内容service接口
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:05
 */
public interface ExamPaperQuestionService extends IService<ExamPaperQuestion> {

    /**
     * 查询量表题目内容
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:05
     */
    PageResult<ExamPaperQuestion> page(ExamPaperQuestionParam examPaperQuestionParam);

    /**
     * 量表题目内容列表
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:05
     */
    List<ExamPaperQuestion> list(ExamPaperQuestionParam examPaperQuestionParam);

    /**
     * 添加量表题目内容
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:05
     */
    void add(ExamPaperQuestionParam examPaperQuestionParam);

    /**
     * 删除量表题目内容
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:05
     */
    void delete(ExamPaperQuestionParam examPaperQuestionParam);

    /**
     * 编辑量表题目内容
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:05
     */
    void edit(ExamPaperQuestionParam examPaperQuestionParam);

    /**
     * 查看量表题目内容
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:05
     */
     ExamPaperQuestion detail(ExamPaperQuestionParam examPaperQuestionParam);
}
