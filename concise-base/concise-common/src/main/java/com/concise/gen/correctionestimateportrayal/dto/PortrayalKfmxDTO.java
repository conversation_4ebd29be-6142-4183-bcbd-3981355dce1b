package com.concise.gen.correctionestimateportrayal.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("扣分明细")
@Data
public class PortrayalKfmxDTO {

    @ApiModelProperty("指标项")
    private String zbx;

    @ApiModelProperty("权重分")
    private int qzf;

    @ApiModelProperty("指标值")
    private String zbz;

    @ApiModelProperty("扣分")
    private int kf;


}
