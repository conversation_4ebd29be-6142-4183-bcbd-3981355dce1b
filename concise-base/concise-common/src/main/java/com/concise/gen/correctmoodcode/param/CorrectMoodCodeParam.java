package com.concise.gen.correctmoodcode.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 心情码参数类
 *
 * <AUTHOR>
 * @date 2022-05-26 18:01:13
*/
@Data
public class CorrectMoodCodeParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 矫正对象id(万达平台)
     */
    @NotBlank(message = "矫正对象id(万达平台)不能为空，请检查thirdId参数", groups = {add.class, edit.class})
    private String thirdId;

    /**
     * 心情码 5：绿码、4：蓝码、3：黄码、2：橙码、1：红码
     */
    @NotBlank(message = "心情码 5：绿码、4：蓝码、3：黄码、2：橙码、1：红码不能为空，请检查moodCode参数", groups = {add.class, edit.class})
    private String moodCode;

    /**
     * 矫正对象姓名
     */
    @NotBlank(message = "矫正对象姓名不能为空，请检查userName参数", groups = {add.class, edit.class})
    private String userName;

    /**
     * 更新时间
     */
    private String updateTime;
}
