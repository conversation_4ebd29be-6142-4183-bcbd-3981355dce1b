package com.concise.gen.examquestion.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.concise.gen.examquestionitem.entity.ExamQuestionItem;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.List;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 考试题目
 *
 * <AUTHOR>
 * @date 2023-01-03 15:52:58
 */
@Data
@TableName("exam_question")
public class ExamQuestion {

    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     *
     */
    private String typeId;

    /**
     *
     */
    private String typeName;

    /**
     * 题型
     */
    private String questionType;

    /**
     *
     */
    private String questionTypeName;

    /**
     * 排序字段
     */
    private String sort;

    /**
     * 题干（长度不够改为text）
     */
    private String stem;

    /**
     * 答案（多选题用逗号隔开）
     */
    private String answer;

    /**
     *
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     *
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建所属人
     */
    private String createDepts;

    /**
     * 选项列表
     */
    @TableField(exist = false)
    private List<ExamQuestionItem> examQuestionItemList;

    /**
     * 用于量表管理树-显示子选项下的题目
     */
    @TableField(exist = false)
    private List<ExamQuestion> childQuestion;

    @TableField(exist = false)
    private ExamQuestion parentQuestion;

    /**
     * 用于量表管理树-显示子选项下的题目
     */
    @TableField(exist = false)
    private String pid;

    /**
     * 用于量表管理树编辑回显-是否被选中
     */
    @TableField(exist = false)
    private String selected;

    /**
     * 答案id
     */
    @TableField(exist = false)
    private String answerIds;

}
