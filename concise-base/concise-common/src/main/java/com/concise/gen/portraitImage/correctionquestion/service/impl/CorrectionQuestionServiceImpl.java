package com.concise.gen.portraitImage.correctionquestion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.portraitImage.correctionquestion.entity.CorrectionQuestion;
import com.concise.gen.portraitImage.correctionquestion.enums.CorrectionQuestionExceptionEnum;
import com.concise.gen.portraitImage.correctionquestion.mapper.CorrectionQuestionMapper;
import com.concise.gen.portraitImage.correctionquestion.param.CorrectionQuestionParam;
import com.concise.gen.portraitImage.correctionquestion.service.CorrectionQuestionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 量表配置--试题信息表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:35
 */
@Service
public class CorrectionQuestionServiceImpl extends ServiceImpl<CorrectionQuestionMapper, CorrectionQuestion> implements CorrectionQuestionService {

    @Override
    public PageResult<CorrectionQuestion> page(CorrectionQuestionParam correctionQuestionParam) {
        QueryWrapper<CorrectionQuestion> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionQuestionParam)) {

            // 根据量表id 查询
            if (ObjectUtil.isNotEmpty(correctionQuestionParam.getScaleBaseId())) {
                queryWrapper.lambda().eq(CorrectionQuestion::getScaleBaseId, correctionQuestionParam.getScaleBaseId());
            }
            // 根据问题名称 查询
            if (ObjectUtil.isNotEmpty(correctionQuestionParam.getQuestion())) {
                queryWrapper.lambda().eq(CorrectionQuestion::getQuestion, correctionQuestionParam.getQuestion());
            }
            // 根据问题类型（0：单选 1：多选） 查询
            if (ObjectUtil.isNotEmpty(correctionQuestionParam.getQuestionType())) {
                queryWrapper.lambda().eq(CorrectionQuestion::getQuestionType, correctionQuestionParam.getQuestionType());
            }
            // 根据问题序号 查询
            if (ObjectUtil.isNotEmpty(correctionQuestionParam.getOrderIndex())) {
                queryWrapper.lambda().eq(CorrectionQuestion::getOrderIndex, correctionQuestionParam.getOrderIndex());
            }
            // 根据是否删除（0：未删除，1删除） 查询
            if (ObjectUtil.isNotEmpty(correctionQuestionParam.getDelFlag())) {
                queryWrapper.lambda().eq(CorrectionQuestion::getDelFlag, correctionQuestionParam.getDelFlag());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionQuestion> list(CorrectionQuestionParam correctionQuestionParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionQuestionParam correctionQuestionParam) {
        CorrectionQuestion correctionQuestion = new CorrectionQuestion();
        BeanUtil.copyProperties(correctionQuestionParam, correctionQuestion);
        this.save(correctionQuestion);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionQuestionParam correctionQuestionParam) {
        this.removeById(correctionQuestionParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionQuestionParam correctionQuestionParam) {
        CorrectionQuestion correctionQuestion = this.queryCorrectionQuestion(correctionQuestionParam);
        BeanUtil.copyProperties(correctionQuestionParam, correctionQuestion);
        this.updateById(correctionQuestion);
    }

    @Override
    public CorrectionQuestion detail(CorrectionQuestionParam correctionQuestionParam) {
        return this.queryCorrectionQuestion(correctionQuestionParam);
    }

    /**
     * 获取量表配置--试题信息表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:35
     */
    private CorrectionQuestion queryCorrectionQuestion(CorrectionQuestionParam correctionQuestionParam) {
        CorrectionQuestion correctionQuestion = this.getById(correctionQuestionParam.getId());
        if (ObjectUtil.isNull(correctionQuestion)) {
            throw new ServiceException(CorrectionQuestionExceptionEnum.NOT_EXIST);
        }
        return correctionQuestion;
    }
}
