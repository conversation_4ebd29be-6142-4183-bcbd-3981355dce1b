package com.concise.gen.correctioncaselable.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 案例与标签关联表参数类
 *
 * <AUTHOR>
 * @date 2022-03-14 17:18:07
*/
@Data
public class CorrectionCaseLableParam extends BaseParam {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 案例id
     */
    private Long caseId;

    /**
     * 标签id
     */
    private String lableId;

    /**
     * 案例IDs
     */
    private String[] caseIds;
    /**
     * 标签IDs
     */
    private String[] labelIds;

}
