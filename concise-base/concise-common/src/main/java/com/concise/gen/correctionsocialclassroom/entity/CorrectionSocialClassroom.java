package com.concise.gen.correctionsocialclassroom.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.concise.common.file.param.SysFileInfoParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.List;

/**
 * 社矫大讲堂
 *
 * <AUTHOR>
 * @date 2022-03-10 17:45:12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_social_classroom")
public class CorrectionSocialClassroom extends BaseEntity {

    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 来源
     */
    private String fromSource;

    /**
     * 视频分类（大讲堂字典值：DJTSPFL 微电影字典值：WDYSPFL）
     */
    private String type;

    /**
     * 所属模块1： 大讲堂  2： 微电影
     */
    private Integer module;

    /**
     * 文件编号
     */
    private String fileId;

    /**
     * 视频简介
     */
    private String remark;

    /**
     * dept_id 发布部门
     */
    private String deptId;

    /**
     * dept_name 发布部门名称
     */
    private String deptName;

    /**
     * 封面ID
     */
    private String imgId;

    /**
     * 视频时长
     */
    private String videoTime;

    /**
     * 链接地址(该字段已停用， 改存上传人)
     */
    private String linkAddress;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 视频集合
     */
    @TableField(exist = false)
    private List<SysFileInfoParam> fileList;

    /**
     * 封面集合
     */
    @TableField(exist = false)
    private List<SysFileInfoParam> imgList;

}
