package com.concise.gen.correctiontablefielddict. controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.correctiontablefielddict. param.CorrectionTableFieldDictParam;
import com.concise.gen.correctiontablefielddict. service.CorrectionTableFieldDictService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 表字段与字典关联表控制器
 *
 * <AUTHOR>
 * @date 2024-11-27 16:36:14
 */
@Api(tags = "表字段与字典关联表")
@RestController
public class CorrectionTableFieldDictController {

    @Resource
    private CorrectionTableFieldDictService correctionTableFieldDictService;

    /**
     * 查询表字段与字典关联表
     *
     * <AUTHOR>
     * @date 2024-11-27 16:36:14
     */
    @Permission
    @GetMapping("/correctionTableFieldDict/page")
    @ApiOperation("表字段与字典关联表_分页查询")
    @BusinessLog(title = "表字段与字典关联表_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(CorrectionTableFieldDictParam correctionTableFieldDictParam) {
        return new SuccessResponseData(correctionTableFieldDictService.page(correctionTableFieldDictParam));
    }

    /**
     * 添加表字段与字典关联表
     *
     * <AUTHOR>
     * @date 2024-11-27 16:36:14
     */
    @Permission
    @PostMapping("/correctionTableFieldDict/add")
    @ApiOperation("表字段与字典关联表_增加")
    @BusinessLog(title = "表字段与字典关联表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(CorrectionTableFieldDictParam.add.class) CorrectionTableFieldDictParam correctionTableFieldDictParam) {
        correctionTableFieldDictService.add(correctionTableFieldDictParam);
        return new SuccessResponseData();
    }

    /**
     * 删除表字段与字典关联表
     *
     * <AUTHOR>
     * @date 2024-11-27 16:36:14
     */
    @Permission
    @PostMapping("/correctionTableFieldDict/delete")
    @ApiOperation("表字段与字典关联表_删除")
    @BusinessLog(title = "表字段与字典关联表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(CorrectionTableFieldDictParam.delete.class) CorrectionTableFieldDictParam correctionTableFieldDictParam) {
        correctionTableFieldDictService.delete(correctionTableFieldDictParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑表字段与字典关联表
     *
     * <AUTHOR>
     * @date 2024-11-27 16:36:14
     */
    @Permission
    @PostMapping("/correctionTableFieldDict/edit")
    @ApiOperation("表字段与字典关联表_编辑")
    @BusinessLog(title = "表字段与字典关联表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(CorrectionTableFieldDictParam.edit.class) CorrectionTableFieldDictParam correctionTableFieldDictParam) {
        correctionTableFieldDictService.edit(correctionTableFieldDictParam);
        return new SuccessResponseData();
    }

    /**
     * 查看表字段与字典关联表
     *
     * <AUTHOR>
     * @date 2024-11-27 16:36:14
     */
    @Permission
    @GetMapping("/correctionTableFieldDict/detail")
    @ApiOperation("表字段与字典关联表_查看")
    @BusinessLog(title = "表字段与字典关联表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(CorrectionTableFieldDictParam.detail.class) CorrectionTableFieldDictParam correctionTableFieldDictParam) {
        return new SuccessResponseData(correctionTableFieldDictService.detail(correctionTableFieldDictParam));
    }

    /**
     * 表字段与字典关联表列表
     *
     * <AUTHOR>
     * @date 2024-11-27 16:36:14
     */
    @Permission
    @GetMapping("/correctionTableFieldDict/list")
    @ApiOperation("表字段与字典关联表_列表")
    @BusinessLog(title = "表字段与字典关联表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(CorrectionTableFieldDictParam correctionTableFieldDictParam) {
        return new SuccessResponseData(correctionTableFieldDictService.list(correctionTableFieldDictParam));
    }

    /**
     * 批量新增表字段与字典关联表
     */
    @Permission
    @PostMapping("/correctionTableFieldDict/addBatch")
    @ApiOperation("表字段与字典关联表_批量新增")
    @BusinessLog(title = "表字段与字典关联表_批量新增", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData batchAdd(@RequestBody List<CorrectionTableFieldDictParam> correctionTableFieldDictParamList) {
        correctionTableFieldDictService.batchAdd(correctionTableFieldDictParamList);
        return new SuccessResponseData();
    }
}
