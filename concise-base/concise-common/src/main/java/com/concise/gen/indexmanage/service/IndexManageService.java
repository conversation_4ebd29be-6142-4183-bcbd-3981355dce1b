package com.concise.gen.indexmanage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.indexmanage.entity.IndexManage;
import com.concise.gen.indexmanage.param.IndexManageParam;
import java.util.List;

/**
 * 指标管理信息表service接口
 *
 * <AUTHOR>
 * @date 2022-05-12 10:02:19
 */
public interface IndexManageService extends IService<IndexManage> {

    /**
     * 查询指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    PageResult<IndexManage> page(IndexManageParam indexManageParam);

    /**
     * 指标管理信息表列表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    List<IndexManage> list(IndexManageParam indexManageParam);

    /**
     * 添加指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    void add(IndexManageParam indexManageParam);

    /**
     * 删除指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    void delete(IndexManageParam indexManageParam);

    /**
     * 编辑指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    void edit(IndexManageParam indexManageParam);

    /**
     * 查看指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
     IndexManage detail(IndexManageParam indexManageParam);
}
