package com.concise.gen.correctionabilitymanage.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.correctionlabelmanage.entity.CorrectionLabelManage;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
* 能力管理参数类
 *
 * <AUTHOR>
 * @date 2022-03-03 10:57:44
*/
@Data
public class CorrectionAbilityManageParam extends BaseParam {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日常监管措施
     */
    private String dailySupervisionStep;

    /**
     * 教育帮扶措施
     */
    private String educationalAssistanceStep;

    /**
     * 心理矫正措施
     */
    private String psychologicalCorrectionStep;

    /**
     * 矫正阶段（1：入矫初期 2：矫正中期 3：矫正末期）
     */
    private Integer correctPhase;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 标签ids
     */
    private String labelIds;

    /**
     * 标签名称s
     */
    @TableField(exist = false)
    private String labelNames;

    /**
     * 标签list
     */
    @TableField(exist = false)
    private List<CorrectionLabelManage> lableList;

    /**
     * 来源
     */
    private String source;


    /**
     * 方案列表
     */
    private List<CorrectionAbilityManageParam> plans;
}
