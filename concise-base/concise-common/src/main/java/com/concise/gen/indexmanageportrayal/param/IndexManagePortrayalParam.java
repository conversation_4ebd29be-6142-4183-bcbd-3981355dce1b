package com.concise.gen.indexmanageportrayal.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
* 指标管理信息表参数类
 *
 * <AUTHOR>
 * @date 2022-05-12 10:02:19
*/
@Data
public class IndexManagePortrayalParam extends BaseParam {

    /**
     * 指标ID
     */
    @NotNull(message = "指标ID不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 指标分类
     */
    @NotBlank(message = "指标分类不能为空，请检查indexType参数", groups = {add.class, edit.class})
    private String indexType;

    private String createTime;
    /**
     * 删除状态
     */
    private String delFlag;

    /**
     *
     */
    private String extend01;

    /**
     *
     */
    private String extend02;

    /**
     *
     */
    private String extend03;

}
