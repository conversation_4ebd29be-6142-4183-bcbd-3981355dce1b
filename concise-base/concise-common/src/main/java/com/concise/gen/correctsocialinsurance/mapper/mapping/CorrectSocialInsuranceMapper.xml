<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.correctsocialinsurance.mapper.CorrectSocialInsuranceMapper">

    <select id="getBySqjzryId" parameterType="String" resultType="com.concise.gen.correctsocialinsurance.entity.CorrectSocialInsurance">
        select * from correct_social_insurance where sqjzry_id = #{sqjzryId}
    </select>
</mapper>
