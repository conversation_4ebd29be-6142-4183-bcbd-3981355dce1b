package com.concise.gen.correctionestimatestay.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimateenter.entity.CorrectionEstimateEnter;
import com.concise.gen.correctionestimatestay.entity.CorrectionEstimateStay;
import com.concise.gen.correctionestimatestay.enums.CorrectionEstimateStayExceptionEnum;
import com.concise.gen.correctionestimatestay.mapper.CorrectionEstimateStayMapper;
import com.concise.gen.correctionestimatestay.param.CorrectionEstimateStayParam;
import com.concise.gen.correctionestimatestay.service.CorrectionEstimateStayService;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.correctionobjectinformation.mapper.CorrectionObjectInformationMapper;
import com.concise.gen.scoringmodeldetailmanage.service.ScoringModelDetailManageService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 在矫评估service接口实现类
 *
 * <AUTHOR>
 * @date 2022-05-17 11:48:38
 */
@Service
public class CorrectionEstimateStayServiceImpl extends ServiceImpl<CorrectionEstimateStayMapper, CorrectionEstimateStay> implements CorrectionEstimateStayService {

    @Resource
    private CorrectionObjectInformationMapper correctionObjectInformationMapper;

    @Resource
    private ScoringModelDetailManageService scoringModelDetailManageService;

    @Override
    public PageResult<CorrectionEstimateStay> page(CorrectionEstimateStayParam correctionEstimateStayParam, Set<String> org) {
        QueryWrapper<CorrectionEstimateStay> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionEstimateStayParam)) {
            // 根据矫正机构id 查询
            queryWrapper.lambda().in(CorrectionEstimateStay::getJzjg, org);
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateStayParam.getSqjzryName())) {
                queryWrapper.lambda().like(CorrectionEstimateStay::getSqjzryName, correctionEstimateStayParam.getSqjzryName());
            }
            // 根据评估月份 查询
            if (ObjectUtil.isNotEmpty(correctionEstimateStayParam.getEstimateMonth())) {
                queryWrapper.lambda().eq(CorrectionEstimateStay::getEstimateMonth, correctionEstimateStayParam.getEstimateMonth());
            }
        }
        queryWrapper.lambda().eq(CorrectionEstimateStay::getDelFlag, 0);
        queryWrapper.lambda().orderByDesc(CorrectionEstimateStay::getEstimateMonth).orderByAsc(CorrectionEstimateStay::getScoreEstimate);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionEstimateStay> list(CorrectionEstimateStayParam correctionEstimateStayParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionEstimateStayParam correctionEstimateStayParam, String scoringModelId) {
        CorrectionEstimateStay correctionEstimateStay = new CorrectionEstimateStay();
        BeanUtil.copyProperties(correctionEstimateStayParam, correctionEstimateStay);
        //总分
        BigDecimal tot = getTotal(scoringModelId);
        correctionEstimateStay.setScoreTotal(tot);
        //计算评估分
        //correctionEstimateStay.setScoreEstimate(getScoreEstimate(correctionEstimateStay, scoringModelId));
        BigDecimal[] scoreArr = getScoreEstimate(correctionEstimateStay, scoringModelId);
        correctionEstimateStay.setScoreEstimate(scoreArr[0]);
        correctionEstimateStay.setRcjgScore(scoreArr[1]);
        correctionEstimateStay.setCfScore(scoreArr[2]);
        correctionEstimateStay.setXwdtScore(scoreArr[3]);
        correctionEstimateStay.setXlztScore(scoreArr[4]);
        this.save(correctionEstimateStay);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionEstimateStayParam correctionEstimateStayParam) {
        CorrectionEstimateStay correctionEstimateStay = new CorrectionEstimateStay();
        correctionEstimateStay.setId(correctionEstimateStayParam.getId());
        correctionEstimateStay.setDelFlag(1);
        this.updateById(correctionEstimateStay);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionEstimateStayParam correctionEstimateStayParam, String scoringModelId) {
        CorrectionEstimateStay correctionEstimateStay = this.queryCorrectionEstimateStay(correctionEstimateStayParam);
        BeanUtil.copyProperties(correctionEstimateStayParam, correctionEstimateStay);
        //总分
        BigDecimal tot = getTotal(scoringModelId);
        correctionEstimateStay.setScoreTotal(tot);
        //计算评估分
        //correctionEstimateStay.setScoreEstimate(getScoreEstimate(correctionEstimateStay, scoringModelId));
        BigDecimal[] scoreArr = getScoreEstimate(correctionEstimateStay, scoringModelId);
        correctionEstimateStay.setScoreEstimate(scoreArr[0]);
        correctionEstimateStay.setRcjgScore(scoreArr[1]);
        correctionEstimateStay.setCfScore(scoreArr[2]);
        correctionEstimateStay.setXwdtScore(scoreArr[3]);
        correctionEstimateStay.setXlztScore(scoreArr[4]);
        this.updateById(correctionEstimateStay);
    }

    @Override
    public CorrectionEstimateStay detail(CorrectionEstimateStayParam correctionEstimateStayParam) {
        return this.queryCorrectionEstimateStay(correctionEstimateStayParam);
    }

    /**
     * 获取在矫评估
     *
     * <AUTHOR>
     * @date 2022-05-17 11:48:38
     */
    private CorrectionEstimateStay queryCorrectionEstimateStay(CorrectionEstimateStayParam correctionEstimateStayParam) {
        CorrectionEstimateStay correctionEstimateStay = this.getById(correctionEstimateStayParam.getId());
        if (ObjectUtil.isNull(correctionEstimateStay)) {
            throw new ServiceException(CorrectionEstimateStayExceptionEnum.NOT_EXIST);
        }
        return correctionEstimateStay;
    }

    /**
     * 根据选中的矫正对象转换下拉选项的值
     *
     * <AUTHOR>
     * @date 2022-05-13 14:52:58
     */
    @Override
    public CorrectionEstimateStay transform(String sqjzryId, int estimateMonth) {
        CorrectionEstimateStay stay = new CorrectionEstimateStay();
        // 先根据万达平台的社区矫正人员id 查找云雀平台的人员id
        String correctionObjectId = this.baseMapper.getCorrectionObjectId(sqjzryId);
        //信息化监管违规*训诫*警告*治安处罚*提请逮捕*提请撤缓*提请撤销假释*提请收监执行*工作变动*夜不归宿
        List<Integer> numList = this.baseMapper.getNums(sqjzryId, correctionObjectId, estimateMonth);
        stay.setYqViolate(numList.get(0)); //信息化监管违规
        stay.setAdvise(numList.get(1)); //训诫
        stay.setWarn(numList.get(2)); //警告
        stay.setPublicSecurity(numList.get(3)); //治安处罚
        stay.setAskArrest(numList.get(4)); //提请逮捕
        stay.setCancelProbation(numList.get(5)); //提请撤缓
        stay.setCancelParole(numList.get(6)); //提请撤销假释
        stay.setCommittedToPrison(numList.get(7)); //提请收监执行
        stay.setWorkChange(numList.get(8)); //工作变动
        stay.setNightOut(numList.get(9)); //夜不归宿
        stay.setPraise(numList.get(10));//受到表扬
        stay.setPenaltyDown(numList.get(11));//获得减刑
        //心情码  5：绿码、4：蓝码、3：黄码、2：橙码、1：红码'
        String moonCode = this.baseMapper.getMoonCode(sqjzryId);
        if (ObjectUtil.isNotEmpty(moonCode) && 1 == moonCode.trim().length() && Integer.parseInt(moonCode) < 4) {
            stay.setHeartCode(Integer.parseInt(moonCode));
        }
        //是否等级降到严管
        stay.setLevelDown(0);
        String gljb = this.baseMapper.getGljb(sqjzryId, estimateMonth);
        if ("1".equals(gljb)) {
            stay.setLevelDown(1);
        }
        return stay;
    }

    /**
     * 初始化在矫评估数据,每月月初一次
     */
    @Override
    public void initCorrectionEstimateStay() {
        //TODO 去除本月要解矫的
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMM");
        int month = Integer.parseInt(sf.format(new Date()));
        CorrectionEstimateStay stay = null;
        List<CorrectionObjectInformation> list = correctionObjectInformationMapper.getCorrection();
        for (CorrectionObjectInformation correctionObjectInformation : list) {
            try {
                stay = new CorrectionEstimateStay();
                stay.setSqjzryId(correctionObjectInformation.getId());
                stay.setSqjzryName(correctionObjectInformation.getXm());
                stay.setSfzh(correctionObjectInformation.getSfzh());
                stay.setJzjg(correctionObjectInformation.getJzjg());
                stay.setJzjgName(correctionObjectInformation.getJzjgName());
                //TODO 从模型取分
                stay.setScoreTotal(new BigDecimal(900));
                stay.setScoreEstimate(new BigDecimal(900));
                stay.setEstimateMonth(month);
                stay.setLevelDown(0);
                stay.setYqViolate(0); //信息化监管违规
                stay.setAdvise(0); //训诫
                stay.setWarn(0); //警告
                stay.setPublicSecurity(0); //治安处罚
                stay.setAskArrest(0); //提请逮捕
                stay.setCancelProbation(0); //提请撤缓
                stay.setCancelParole(0); //提请撤销假释
                stay.setCommittedToPrison(0); //提请收监执行
                stay.setWorkChange(0); //工作变动
                stay.setNightOut(0); //夜不归宿
                stay.setPraise(0);//受到表扬
                stay.setPenaltyDown(0);//获得减刑
                this.save(stay);
            } catch(Exception e) {
                log.error("initCorrectionEstimateStay Error:" + e.getMessage());
            }
        }
    }

    /**
     * 每天更新在矫评估分数
     */
    @Override
    public void updateCorrectionEstimateStay() {
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMM");
        int month = Integer.parseInt(sf.format(new Date()));
        //评分模型
        String scoringModelId = scoringModelDetailManageService.getModel(String.valueOf(month));

        QueryWrapper<CorrectionEstimateStay> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CorrectionEstimateStay::getEstimateMonth, month);
        queryWrapper.lambda().eq(CorrectionEstimateStay::getDelFlag, 0);
        queryWrapper.select("id, sqjzry_id, score_total");
        List<CorrectionEstimateStay> list = this.list(queryWrapper);
        for (CorrectionEstimateStay correctionEstimateStay : list)  {
            CorrectionEstimateStay newObj = transform(correctionEstimateStay.getSqjzryId(), month);
            correctionEstimateStay.setYqViolate(newObj.getYqViolate()); //信息化监管违规
            correctionEstimateStay.setAdvise(newObj.getAdvise()); //训诫
            correctionEstimateStay.setWarn(newObj.getWarn()); //警告
            correctionEstimateStay.setPublicSecurity(newObj.getPublicSecurity()); //治安处罚
            correctionEstimateStay.setAskArrest(newObj.getAskArrest()); //提请逮捕
            correctionEstimateStay.setCancelProbation(newObj.getCancelProbation()); //提请撤缓
            correctionEstimateStay.setCancelParole(newObj.getCancelParole()); //提请撤销假释
            correctionEstimateStay.setCommittedToPrison(newObj.getCommittedToPrison()); //提请收监执行
            correctionEstimateStay.setWorkChange(newObj.getWorkChange()); //工作变动
            correctionEstimateStay.setNightOut(newObj.getNightOut()); //夜不归宿
            correctionEstimateStay.setPraise(newObj.getPraise());//受到表扬
            correctionEstimateStay.setPenaltyDown(newObj.getPenaltyDown());//获得减刑
            correctionEstimateStay.setHeartCode(newObj.getHeartCode());
            correctionEstimateStay.setLevelDown(0);
            //计算评估分、日常监管分数、处罚分数、行为动态分数、心理状态分数
            BigDecimal[] scoreArr = getScoreEstimate(correctionEstimateStay, scoringModelId);
            correctionEstimateStay.setScoreEstimate(scoreArr[0]);
            correctionEstimateStay.setRcjgScore(scoreArr[1]);
            correctionEstimateStay.setCfScore(scoreArr[2]);
            correctionEstimateStay.setXwdtScore(scoreArr[3]);
            correctionEstimateStay.setXlztScore(scoreArr[4]);
            this.updateById(correctionEstimateStay);
        }
    }

    /**
     * 计算评估分、日常监管分数、处罚分数、行为动态分数、心理状态分数
     * @param model
     * @param scoringModelId 模型id
     * @return
     */
    @Override
    public BigDecimal[] getScoreEstimate(CorrectionEstimateStay model, String scoringModelId) {
        int cf = getScoreModel(scoringModelId, "1533357235671048194"); //处罚分
        int djpg = getScoreModel(scoringModelId, "1533358983756279810"); //等级评估
        int xxhwg = getScoreModel(scoringModelId, "1533359102270533633"); //信息化违规
        int gzbd = getScoreModel(scoringModelId, "1530068129784451074"); //工作变动
        int ybgs = getScoreModel(scoringModelId, "1530068244184092673"); //夜不归宿
        int hm = getScoreModel(scoringModelId, "1530068578512064513"); //心情红码
        int hum = getScoreModel(scoringModelId, "1530068630018117633"); //心情黄码
        int cm = getScoreModel(scoringModelId, "1530069174937899009"); //心情橙码

        //计算处罚扣分
        int advise = model.getAdvise() * 25; //训诫
        int warn = model.getWarn() * 75; //警告
        int publicSecurity = model.getPublicSecurity(); //治安处罚
        int ask = model.getAskArrest() * 150; //提请逮捕
        int cp = model.getCancelProbation() * 250; //提请撤缓
        int cpo = model.getCancelParole() * 250; //提请撤销假释
        int ctr = model.getCommittedToPrison() * 200; //提请收监执行
        int cfScore = cf - (advise + warn + publicSecurity + ask + cp + cpo + ctr);
        if (cfScore < 0) {
            cfScore = 0;
        }

        //计算日常监管
        // 是否等级降到严管
        int djpgScore = djpg - model.getLevelDown() * 50;
        if (djpgScore < 0) {
            djpgScore = 0;
        }
        //信息化监管违规
        int xxhwgScore = xxhwg - model.getYqViolate() * 20;
        if (xxhwgScore < 0) {
            xxhwgScore = 0;
        }

        //计算行为分析
        //工作变动
        int gzbdScore = gzbd - model.getWorkChange() * 15;
        if (gzbdScore < 0) {
            gzbdScore = 0;
        }
        //夜不归宿
        int ybgsScore = ybgs - model.getNightOut() * 10;
        if (ybgsScore < 0) {
            ybgsScore = 0;
        }

        //计算心理状态
        int heartCode = 0;
        if (ObjectUtil.isNotEmpty(model.getHeartCode())) {
            switch (model.getHeartCode()) {
                case 1:
                    heartCode = 100;
                    break;
                case 2:
                    heartCode = 50;
                    break;
                case 3:
                    heartCode = 50;
                    break;
            }
        }

        //计算加分
        //表扬、减刑
        int jfScore = model.getPraise() * 25 + model.getPenaltyDown() * 25;
        if (jfScore > 50) {
            jfScore = 50;
        }
        //计算评估分、日常监管分数、处罚分数、行为动态分数、心理状态分数
        BigDecimal[] scoreArr = new BigDecimal[5];
        scoreArr[0] = new BigDecimal(cfScore + djpgScore + xxhwgScore + gzbdScore + ybgsScore + heartCode + jfScore);
        scoreArr[1] = new BigDecimal(djpgScore + xxhwgScore);
        scoreArr[2] = new BigDecimal(cfScore);
        scoreArr[3] = new BigDecimal(gzbdScore + ybgsScore);
        scoreArr[4] = new BigDecimal(heartCode);
        return scoreArr;
    }

    /**
     * 根据选项查询分数
     * @param scoringModelId
     * @param extend01
     * @return
     */
    public int getScoreModel(String scoringModelId, String extend01) {
        return scoringModelDetailManageService.getPoint(scoringModelId, extend01);
    }

    /**
     * 获取总分 日常监管 + 处罚 + 行为趋势 + 心理状态
     * @param scoringModelId 模型id
     * @return
     */
    @Override
    public BigDecimal getTotal(String scoringModelId) {
        return new BigDecimal(
                getScoreModel(scoringModelId, "1530066180762685441") +
                        getScoreModel(scoringModelId, "1533357235671048194") +
                        getScoreModel(scoringModelId, "1530066249226309633") +
                        getScoreModel(scoringModelId, "1530066279328829441")
        );
    }
}
