package com.concise.gen.correctionrulesandregulations.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionrulesandregulations.entity.CorrectionRulesAndRegulations;
import com.concise.gen.correctionrulesandregulations.param.CorrectionRulesAndRegulationsParam;
import java.util.List;

/**
 * 规章制度service接口
 *
 * <AUTHOR>
 * @date 2022-03-10 17:45:09
 */
public interface CorrectionRulesAndRegulationsService extends IService<CorrectionRulesAndRegulations> {

    /**
     * 查询规章制度
     *
     * <AUTHOR>
     * @date 2022-03-10 17:45:09
     */
    PageResult<CorrectionRulesAndRegulations> page(CorrectionRulesAndRegulationsParam correctionRulesAndRegulationsParam);

    /**
     * 规章制度列表
     *
     * <AUTHOR>
     * @date 2022-03-10 17:45:09
     */
    List<CorrectionRulesAndRegulations> list(CorrectionRulesAndRegulationsParam correctionRulesAndRegulationsParam);

    /**
     * 添加规章制度
     *
     * <AUTHOR>
     * @date 2022-03-10 17:45:09
     */
    void add(CorrectionRulesAndRegulationsParam correctionRulesAndRegulationsParam);

    /**
     * 删除规章制度
     *
     * <AUTHOR>
     * @date 2022-03-10 17:45:09
     */
    void delete(CorrectionRulesAndRegulationsParam correctionRulesAndRegulationsParam);

    /**
     * 编辑规章制度
     *
     * <AUTHOR>
     * @date 2022-03-10 17:45:09
     */
    void edit(CorrectionRulesAndRegulationsParam correctionRulesAndRegulationsParam);

    /**
     * 查看规章制度
     *
     * <AUTHOR>
     * @date 2022-03-10 17:45:09
     */
     CorrectionRulesAndRegulations detail(CorrectionRulesAndRegulationsParam correctionRulesAndRegulationsParam);
}
