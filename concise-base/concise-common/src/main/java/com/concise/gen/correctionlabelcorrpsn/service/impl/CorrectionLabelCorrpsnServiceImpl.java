package com.concise.gen.correctionlabelcorrpsn.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.concise.gen.correctionabilitymanage.entity.CorrectionAbilityManage;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionlabelcorrpsn.entity.CorrectionLabelCorrpsn;
import com.concise.gen.correctionlabelcorrpsn.enums.CorrectionLabelCorrpsnExceptionEnum;
import com.concise.gen.correctionlabelcorrpsn.mapper.CorrectionLabelCorrpsnMapper;
import com.concise.gen.correctionlabelcorrpsn.param.CorrectionLabelCorrpsnParam;
import com.concise.gen.correctionlabelcorrpsn.service.CorrectionLabelCorrpsnService;
import com.concise.gen.correctionobjectinformation.entity.ScreenModel;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * 标签关联矫正对象service接口实现类
 *
 * <AUTHOR>
 * @date 2022-03-04 14:58:19
 */
@Service
public class CorrectionLabelCorrpsnServiceImpl extends ServiceImpl<CorrectionLabelCorrpsnMapper, CorrectionLabelCorrpsn> implements CorrectionLabelCorrpsnService {

    @Override
    public PageResult<CorrectionLabelCorrpsn> page(CorrectionLabelCorrpsnParam correctionLabelCorrpsnParam) {
        QueryWrapper<CorrectionLabelCorrpsn> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionLabelCorrpsnParam)) {

            // 根据标签ID 查询
            if (ObjectUtil.isNotEmpty(correctionLabelCorrpsnParam.getLabelId())) {
                queryWrapper.lambda().eq(CorrectionLabelCorrpsn::getLabelId, correctionLabelCorrpsnParam.getLabelId());
            }
            // 根据矫正对象ID 查询
            if (ObjectUtil.isNotEmpty(correctionLabelCorrpsnParam.getSqjzryId())) {
                queryWrapper.lambda().eq(CorrectionLabelCorrpsn::getSqjzryId, correctionLabelCorrpsnParam.getSqjzryId());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionLabelCorrpsn> list(CorrectionLabelCorrpsnParam correctionLabelCorrpsnParam) {
        QueryWrapper<CorrectionLabelCorrpsn> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionLabelCorrpsnParam)) {

            // 根据标签ID 查询
            if (ObjectUtil.isNotEmpty(correctionLabelCorrpsnParam.getLabelId())) {
                queryWrapper.lambda().eq(CorrectionLabelCorrpsn::getLabelId, correctionLabelCorrpsnParam.getLabelId());
            }
            // 根据矫正对象ID 查询
            if (ObjectUtil.isNotEmpty(correctionLabelCorrpsnParam.getSqjzryId())) {
                queryWrapper.lambda().eq(CorrectionLabelCorrpsn::getSqjzryId, correctionLabelCorrpsnParam.getSqjzryId());
            }
        }
        return this.list(queryWrapper);
    }

    @Override
    public void add(CorrectionLabelCorrpsnParam correctionLabelCorrpsnParam) {
        List<CorrectionLabelCorrpsn> list = new ArrayList<>();
        CorrectionLabelCorrpsn cc = null;
        for (String sqjzryId : correctionLabelCorrpsnParam.getSqjzryIds()) {
            for (String labelId : correctionLabelCorrpsnParam.getLabelIds()) {
                // 先判断标签是否已绑定过矫正对象，已存在则跳过
                int num = this.count(new QueryWrapper<CorrectionLabelCorrpsn>().lambda().eq(CorrectionLabelCorrpsn::getLabelId, labelId)
                        .eq(CorrectionLabelCorrpsn::getSqjzryId, sqjzryId));
                if (num == 0) {
                    cc = new CorrectionLabelCorrpsn();
                    cc.setSqjzryId(sqjzryId);
                    cc.setLabelId(labelId);
                    list.add(cc);
                }
            }
        }
        if (list.size() > 0) {
            this.saveBatch(list);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionLabelCorrpsnParam correctionLabelCorrpsnParam) {
        this.removeById(correctionLabelCorrpsnParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionLabelCorrpsnParam correctionLabelCorrpsnParam) {
        CorrectionLabelCorrpsn correctionLabelCorrpsn = this.queryCorrectionLabelCorrpsn(correctionLabelCorrpsnParam);
        BeanUtil.copyProperties(correctionLabelCorrpsnParam, correctionLabelCorrpsn);
        this.updateById(correctionLabelCorrpsn);
    }

    @Override
    public CorrectionLabelCorrpsn detail(CorrectionLabelCorrpsnParam correctionLabelCorrpsnParam) {
        return this.queryCorrectionLabelCorrpsn(correctionLabelCorrpsnParam);
    }

    /**
     * 获取标签关联矫正对象
     *
     * <AUTHOR>
     * @date 2022-03-04 14:58:19
     */
    private CorrectionLabelCorrpsn queryCorrectionLabelCorrpsn(CorrectionLabelCorrpsnParam correctionLabelCorrpsnParam) {
        CorrectionLabelCorrpsn correctionLabelCorrpsn = this.getById(correctionLabelCorrpsnParam.getId());
        if (ObjectUtil.isNull(correctionLabelCorrpsn)) {
            throw new ServiceException(CorrectionLabelCorrpsnExceptionEnum.NOT_EXIST);
        }
        return correctionLabelCorrpsn;
    }

    /**
     * 保存信息
     * @param sqjzryId 矫正对象ID
     * @param labelIdList 标签集合
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveInfo(String sqjzryId, List<String> labelIdList, String sfzh) {
        // 先删除之前标签绑定的字典值
        remove(new QueryWrapper<CorrectionLabelCorrpsn>().lambda()
                .eq(CorrectionLabelCorrpsn::getSqjzryId, sqjzryId));
        List<CorrectionLabelCorrpsn> list = new ArrayList<>();
        CorrectionLabelCorrpsn cc = null;
        for (String labelId : labelIdList) {
            cc = new CorrectionLabelCorrpsn();
            cc.setSqjzryId(sqjzryId);
            cc.setSfzh(sfzh);
            cc.setLabelId(labelId);
            list.add(cc);
        }
        if (list.size() > 0) {
            this.saveBatch(list);
        }
    }

    @Async
    @Override
    public void saveData(List<CorrectionLabelCorrpsn> correctionLabelCorrpsnList) {
        this.saveBatch(correctionLabelCorrpsnList);

    }

    @Override
    public List<ScreenModel> familyStatus(Set<String> userIds) {
        List<ScreenModel> screenModelList=new ArrayList<>();
        ScreenModel unmarried = new ScreenModel();
        unmarried.setTitle("未婚/离异");
        unmarried.setAmount(this.baseMapper.countUnmarried(userIds));
        screenModelList.add(unmarried);

        ScreenModel married = new ScreenModel();
        married.setTitle("已婚");
        married.setAmount(this.baseMapper.countMarried(userIds));
        screenModelList.add(married);

        ScreenModel unmarriedElderly = new ScreenModel();
        unmarriedElderly.setTitle("大龄未婚");
        unmarriedElderly.setAmount(this.baseMapper.countUnmarriedElderly(userIds));
        screenModelList.add(unmarriedElderly);
        return screenModelList;
    }

    @Override
    public List<String> filterLabelsByPersonAndLabels(String jzdxId, List<String> basicLabelIds) {
        List<CorrectionLabelCorrpsn> list = this.list(new QueryWrapper<CorrectionLabelCorrpsn>().lambda().eq(CorrectionLabelCorrpsn::getSqjzryId, jzdxId).in(CorrectionLabelCorrpsn::getLabelId, basicLabelIds));
        if (CollectionUtil.isNotEmpty(list)) {
            return list.stream().map(item->String.valueOf(item.getLabelId())).collect(Collectors.toList());

        }
        return Collections.emptyList();
    }

    @Override
    public List<CorrectionLabelCorrpsn> getLabelMeasures(Set<String> labelIds) {
        return this.baseMapper.getLabelMeasures(labelIds);
    }

    @Override
    public List<CorrectionAbilityManage> getLabelAbility(Set<String> labelManageList) {
        return this.baseMapper.getAbilityMeasures(labelManageList);
    }
}
