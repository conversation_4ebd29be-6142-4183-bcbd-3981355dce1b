package com.concise.gen.correctsocialinsurance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.gen.correctmoveperson.entity.CorrectMovePerson;
import com.concise.gen.correctsocialinsurance.entity.CorrectSocialInsurance;
import com.concise.gen.correctsocialinsurance.enums.CorrectSocialInsuranceExceptionEnum;
import com.concise.gen.correctsocialinsurance.mapper.CorrectSocialInsuranceMapper;
import com.concise.gen.correctsocialinsurance.param.CorrectSocialInsuranceParam;
import com.concise.gen.correctsocialinsurance.service.CorrectSocialInsuranceService;
import com.concise.gen.correctsocialinsurancedtl.entity.CorrectSocialInsuranceDtl;
import com.concise.gen.correctsocialinsurancedtl.mapper.CorrectSocialInsuranceDtlMapper;
import com.concise.gen.correctsocialinsurancedtl.service.CorrectSocialInsuranceDtlService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 历史社保主表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-06-07 15:08:49
 */
@Service
@Slf4j
public class CorrectSocialInsuranceServiceImpl extends ServiceImpl<CorrectSocialInsuranceMapper, CorrectSocialInsurance> implements CorrectSocialInsuranceService {

    @Override
    public PageResult<CorrectSocialInsurance> page(CorrectSocialInsuranceParam correctSocialInsuranceParam, List<String> sqjzryIdList) {
        QueryWrapper<CorrectSocialInsurance> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(sqjzryIdList)){
            queryWrapper.lambda().in(CorrectSocialInsurance::getSqjzryId, sqjzryIdList);
        }
        if (ObjectUtil.isNotNull(correctSocialInsuranceParam)) {

            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctSocialInsuranceParam.getXm())) {
                queryWrapper.lambda().like(CorrectSocialInsurance::getXm, correctSocialInsuranceParam.getXm());
            }
            // 根据身份证号 查询
            if (ObjectUtil.isNotEmpty(correctSocialInsuranceParam.getSfzh())) {
                queryWrapper.lambda().like(CorrectSocialInsurance::getSfzh, correctSocialInsuranceParam.getSfzh());
            }
        }
        queryWrapper.lambda().orderByDesc(CorrectSocialInsurance::getMonth);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectSocialInsurance> list(CorrectSocialInsuranceParam correctSocialInsuranceParam) {
        return this.list();
    }

    @Override
    public void add(CorrectSocialInsuranceParam correctSocialInsuranceParam) {
        CorrectSocialInsurance correctSocialInsurance = new CorrectSocialInsurance();
        BeanUtil.copyProperties(correctSocialInsuranceParam, correctSocialInsurance);
        this.save(correctSocialInsurance);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectSocialInsuranceParam correctSocialInsuranceParam) {
        this.removeById(correctSocialInsuranceParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectSocialInsuranceParam correctSocialInsuranceParam) {
        CorrectSocialInsurance correctSocialInsurance = this.queryCorrectSocialInsurance(correctSocialInsuranceParam);
        BeanUtil.copyProperties(correctSocialInsuranceParam, correctSocialInsurance);
        this.updateById(correctSocialInsurance);
    }

    @Override
    public CorrectSocialInsurance detail(CorrectSocialInsuranceParam correctSocialInsuranceParam) {
        return this.queryCorrectSocialInsurance(correctSocialInsuranceParam);
    }

    /**
     * 获取历史社保主表
     *
     * <AUTHOR>
     * @date 2022-06-07 15:08:49
     */
    private CorrectSocialInsurance queryCorrectSocialInsurance(CorrectSocialInsuranceParam correctSocialInsuranceParam) {
        CorrectSocialInsurance correctSocialInsurance = this.getById(correctSocialInsuranceParam.getId());
        if (ObjectUtil.isNull(correctSocialInsurance)) {
            throw new ServiceException(CorrectSocialInsuranceExceptionEnum.NOT_EXIST);
        }
        return correctSocialInsurance;
    }


}
