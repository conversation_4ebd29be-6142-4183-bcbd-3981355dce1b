package com.concise.gen.correctioncase.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.concise.gen.correctionlabelmanage.entity.CorrectionLabelManage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.List;

/**
 * 案例库
 *
 * <AUTHOR>
 * @date 2022-03-14 17:18:03
 */
@Data
@TableName("correction_case")
public class CorrectionCase {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 标题
     */
    @Excel(name = "标题", width = 20)
    private String title;

    /**
     * 案例类型
     */
    @Excel(name = "案例类型", width = 20)
    private String caseType;

    /**
     * 案例内容
     */
    @Excel(name = "案例内容", width = 20)
    private String caseContent;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Excel(name = "时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createUser;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateUser;

    /**
     * 标签list
     */
    @TableField(exist = false)
    private List<CorrectionLabelManage> lableList;

    /**
     * 标签ids
     */
    @TableField(exist = false)
    private String labelIds;

}
