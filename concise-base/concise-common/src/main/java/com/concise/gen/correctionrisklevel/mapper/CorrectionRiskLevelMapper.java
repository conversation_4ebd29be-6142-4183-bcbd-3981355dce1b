package com.concise.gen.correctionrisklevel.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.concise.gen.correctionobjectinformation.entity.ScreenModel;
import com.concise.gen.correctionrisklevel.entity.CorrectionRiskLevel;
import com.concise.gen.correctionrisklevel.entity.CorrectionRiskLevelDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 风险等级
 *
 * <AUTHOR>
 * @date 2022-05-18 18:29:16
 */
public interface CorrectionRiskLevelMapper extends BaseMapper<CorrectionRiskLevel> {

    /**
     * 根据矫正对象id 和 评分月度查询
     *
     * @param sqjzryId
     * @param scoringTime
     * @return
     */
    CorrectionRiskLevel getInfo(@Param("sqjzryId") String sqjzryId, @Param("scoringTime") String scoringTime);

    //日常监管_实时监控情况   【查普管降严管、信息化违规】
    List<CorrectionRiskLevel> findRcjg(@Param("pid") String pid, @Param("correctionObjectId") String correctionObjectId, @Param("estimateMonth") String estimateMonth);

    //处罚_实时监控情况   【训诫、警告、治安处罚、提请逮捕、提请撤缓、提请撤销假释、提请收监执行】
    List<CorrectionRiskLevel> findListCf(@Param("pid") String pid, @Param("estimateMonth") String estimateMonth);

    //行为动态_实时监控情况   【工作变动、夜不归宿】
    List<CorrectionRiskLevel> findListXwdt(@Param("correctionObjectId") String correctionObjectId, @Param("estimateMonth") String estimateMonth);

    // 日常监管_普管降严管
    List<CorrectionRiskLevel> findLevel(@Param("pid") String pid);

    // 日常监管_心理状态
    List<CorrectionRiskLevel> findMoonCode(@Param("pid") String pid);

    List<ScreenModel> riskLevel(@Param("userIds") Set<String> userIds);

    List<CorrectionRiskLevel> riskLevelDetail(@Param("title") String title, @Param("userIds") Set<String> userIds, @Param("name") String name);

    Page<CorrectionRiskLevelDto> riskAssessmentPage(@Param("page") Page page, @Param("ew") QueryWrapper queryWrapper);
}
