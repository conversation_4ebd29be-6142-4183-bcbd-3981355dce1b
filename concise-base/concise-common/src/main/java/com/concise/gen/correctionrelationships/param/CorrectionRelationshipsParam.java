package com.concise.gen.correctionrelationships.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
 * 家庭成员参数类
 *
 * <AUTHOR>
 * @date 2022-02-22 16:07:50
 */
@Data
public class CorrectionRelationshipsParam extends BaseParam {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 社区矫正人员标识
     */
    @NotBlank(message = "社区矫正人员标识不能为空，请检查pid参数", groups = {add.class, edit.class})
    private String pid;

    /**
     * 称谓
     */
    @NotBlank(message = "称谓不能为空，请检查gx参数", groups = {add.class, edit.class})
    private String gx;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 所在单位
     */
    @NotBlank(message = "所在单位不能为空，请检查szdw参数", groups = {add.class, edit.class})
    private String szdw;

    /**
     * 居住地址
     */
    @NotBlank(message = "居住地址不能为空，请检查jtzz参数", groups = {add.class, edit.class})
    private String jtzz;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空，请检查lxdh参数", groups = {add.class, edit.class})
    private String lxdh;

    /**
     * 称谓name
     */
    @NotBlank(message = "称谓name不能为空，请检查gxName参数", groups = {add.class, edit.class})
    private String gxName;

    /**
     * 是否删除（0：未删除，1删除）
     */
    @NotNull(message = "是否删除（0：未删除，1删除）不能为空，请检查delFlag参数", groups = {add.class, edit.class})
    private Integer delFlag;

}
