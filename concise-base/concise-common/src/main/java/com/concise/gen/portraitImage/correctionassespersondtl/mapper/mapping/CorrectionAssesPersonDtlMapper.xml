<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.portraitImage.correctionassespersondtl.mapper.CorrectionAssesPersonDtlMapper">

    <delete id="delByAssesPersonId">
        delete from correction_asses_person_dtl
        where asses_Person_Id in
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>
</mapper>
