package com.concise.gen.correctionestimateportrayal.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.correctionestimateportrayal.entity.CorrectionEstimatePortrayal;
import com.concise.gen.correctionestimateportrayal.entity.vo.PortrayalGjqsVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 在矫评估
 *
 * <AUTHOR>
 * @date 2022-05-17 11:48:38
 */
public interface CorrectionEstimatePortrayalMapper extends BaseMapper<CorrectionEstimatePortrayal> {

    /**
     * 根据万达的ID查找云雀平台矫正对象id
     *
     * @param sqjzryId
     * @return
     */
    String getCorrectionObjectId(String sqjzryId);

    /**
     * 按年月查询矫正对象相关事项次数（共10项）
     * 【信息化监管违规*训诫*警告*治安处罚*提请逮捕*提请撤缓*提请撤销假释*提请收监执行*工作变动*夜不归宿*受到表扬*获得减刑】
     *
     * @param pid                万达平台矫正对象id
     * @param correctionObjectId 云雀平台ID
     * @param estimateMonth      评估月份
     * @return
     */
    List<Integer> getNums(@Param("pid") String pid, @Param("correctionObjectId") String correctionObjectId, @Param("estimateMonth") Integer estimateMonth);

    /**
     * 查询矫正对象相关事项次数（共10项）
     * 【信息化监管违规*训诫*警告*治安处罚*提请逮捕*提请撤缓*提请撤销假释*提请收监执行*工作变动*夜不归宿*受到表扬*获得减刑】
     *
     * @param pid                万达平台矫正对象id
     * @param correctionObjectId 云雀平台ID
     * @return
     */
    List<Integer> getNumsAll(@Param("pid") String pid, @Param("correctionObjectId") String correctionObjectId);

    /**
     * 获取矫正对象心情码
     *
     * @param thirdId 万达平台矫正对象id
     * @return
     */
    String getMoonCode(String thirdId);

    /**
     * 按年月查询等级管理 1: 严管 2：普管
     *
     * @param pid           万达平台矫正对象id
     * @param estimateMonth 评估月份
     * @return
     */
    String getGljb(@Param("pid") String pid, @Param("estimateMonth") Integer estimateMonth);


    /**
     *
     */
    List<PortrayalGjqsVO> portrayalGjqs(@Param("id") String id);
}
