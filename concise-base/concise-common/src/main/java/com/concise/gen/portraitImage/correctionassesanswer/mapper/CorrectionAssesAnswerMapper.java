package com.concise.gen.portraitImage.correctionassesanswer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.correctionobjectinformation.entity.ScreenModel;
import com.concise.gen.portraitImage.correctionassesanswer.entity.CorrectionAssesAnswer;
import com.concise.gen.portraitImage.correctionassespersondtl.entity.CorrectionAssesPersonDtlModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 评估管理--评估人员评估明细答案
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:45
 */
public interface CorrectionAssesAnswerMapper extends BaseMapper<CorrectionAssesAnswer> {

    /**
     * 根据问卷查找所有的试题id
     *
     * @param dtlId
     * @return
     */
    List<String> getQuestionIds(String dtlId);

    /**
     * 根据参数获取试题选项
     *
     * @param dtlId
     * @param questionId
     * @return
     */
    String get(@Param("dtlId") String dtlId, @Param("questionId") String questionId);

    /**
     * 知法画像统计
     *
     * @param userIds
     * @return
     */
    List<ScreenModel> law(@Param("userIds") Set<String> userIds);

    /**
     * 工作状态统计
     * @param userIds
     * @return
     */
    List<ScreenModel> workingCondition(@Param("userIds") Set<String> userIds);

    List<ScreenModel> workingType(@Param("userIds") Set<String> userIds);

    List<ScreenModel> identityAndOccupation(@Param("userIds") Set<String> userIds);

    List<ScreenModel> sourceOfIncome(@Param("userIds") Set<String> userIds);

    List<ScreenModel> householdIncome(@Param("userIds") Set<String> userIds);

    List<ScreenModel> householdDebt(@Param("userIds") Set<String> userIds);

    List<ScreenModel> familyConflicts(@Param("userIds") Set<String> userIds);

    List<CorrectionAssesPersonDtlModel> latestRevenueList(@Param("userIds") Set<String> userIds);

    List<CorrectionAssesPersonDtlModel> liabilityList(@Param("userIds") Set<String> userIds);
}
