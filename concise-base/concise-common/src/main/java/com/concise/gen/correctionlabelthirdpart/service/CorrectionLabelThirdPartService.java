package com.concise.gen.correctionlabelthirdpart.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionlabelthirdpart.entity.CorrectionLabelThirdPart;
import com.concise.gen.correctionlabelthirdpart.param.CorrectionLabelThirdPartParam;
import java.util.List;

/**
 * 标签管理-第三方对接service接口
 *
 * <AUTHOR>
 * @date 2023-04-23 14:35:27
 */
public interface CorrectionLabelThirdPartService extends IService<CorrectionLabelThirdPart> {

    /**
     * 查询标签管理-第三方对接
     *
     * <AUTHOR>
     * @date 2023-04-23 14:35:27
     */
    PageResult<CorrectionLabelThirdPart> page(CorrectionLabelThirdPartParam correctionLabelThirdPartParam);

    /**
     * 标签管理-第三方对接列表
     *
     * <AUTHOR>
     * @date 2023-04-23 14:35:27
     */
    List<CorrectionLabelThirdPart> list(CorrectionLabelThirdPartParam correctionLabelThirdPartParam);

    /**
     * 添加标签管理-第三方对接
     *
     * <AUTHOR>
     * @date 2023-04-23 14:35:27
     */
    void add(CorrectionLabelThirdPartParam correctionLabelThirdPartParam);

    /**
     * 删除标签管理-第三方对接
     *
     * <AUTHOR>
     * @date 2023-04-23 14:35:27
     */
    void delete(CorrectionLabelThirdPartParam correctionLabelThirdPartParam);

    /**
     * 编辑标签管理-第三方对接
     *
     * <AUTHOR>
     * @date 2023-04-23 14:35:27
     */
    void edit(CorrectionLabelThirdPartParam correctionLabelThirdPartParam);

    /**
     * 查看标签管理-第三方对接
     *
     * <AUTHOR>
     * @date 2023-04-23 14:35:27
     */
     CorrectionLabelThirdPart detail(CorrectionLabelThirdPartParam correctionLabelThirdPartParam);
}
