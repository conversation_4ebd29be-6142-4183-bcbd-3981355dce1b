package com.concise.gen.portraitImage.correctionassesperson.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.portraitImage.correctionassesperson.entity.CorrectionAssesPerson;
import com.concise.gen.portraitImage.correctionassesperson.param.CorrectionAssesPersonParam;

import java.util.List;
import java.util.Set;

/**
 * 评估管理--评估人员信息service接口
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:42
 */
public interface CorrectionAssesPersonService extends IService<CorrectionAssesPerson> {

    /**
     * 查询评估管理--评估人员信息
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:42
     */
    PageResult<CorrectionAssesPerson> page(CorrectionAssesPersonParam correctionAssesPersonParam, Set<String> org);

    /**
     * 评估管理--评估人员信息列表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:42
     */
    List<CorrectionAssesPerson> list(CorrectionAssesPersonParam correctionAssesPersonParam);

    /**
     * 添加评估管理--评估人员信息
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:42
     */
    void add(CorrectionAssesPersonParam correctionAssesPersonParam);

    /**
     * 删除评估管理--评估人员信息
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:42
     */
    void delete(CorrectionAssesPersonParam correctionAssesPersonParam);

    /**
     * 编辑评估管理--评估人员信息
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:42
     */
    void edit(CorrectionAssesPersonParam correctionAssesPersonParam);

    /**
     * 查看评估管理--评估人员信息
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:42
     */
     CorrectionAssesPerson detail(CorrectionAssesPersonParam correctionAssesPersonParam);
}
