package com.concise.gen.correctionlabelsystem.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 标签所属应用参数类
 *
 * <AUTHOR>
 * @date 2022-08-22 09:04:42
*/
@Data
public class CorrectionLabelSystemParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 标签ID
     */
    @NotBlank(message = "标签ID不能为空，请检查labelId参数", groups = {add.class, edit.class})
    private String labelId;

    /**
     * 所属应用,字典值: SSYY
     */
    @NotBlank(message = "所属应用,字典值: SSYY不能为空，请检查sysCode参数", groups = {add.class, edit.class})
    private String sysCode;

}
