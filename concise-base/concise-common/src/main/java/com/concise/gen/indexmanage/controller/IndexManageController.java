package com.concise.gen.indexmanage. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.indexmanage. param.IndexManageParam;
import com.concise.gen.indexmanage. service.IndexManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 指标管理信息表控制器
 *
 * <AUTHOR>
 * @date 2022-05-12 10:02:19
 */
@Api(tags = "指标管理信息表")
@RestController
public class IndexManageController {

    @Resource
    private IndexManageService indexManageService;

    /**
     * 查询指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    @Permission
    @GetMapping("/indexManage/page")
    @ApiOperation("指标管理信息表_分页查询")
    @BusinessLog(title = "指标管理信息表_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(IndexManageParam indexManageParam) {
        indexManageParam.setDelFlag("0");
        return new SuccessResponseData(indexManageService.page(indexManageParam));
    }

    /**
     * 添加指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    @Permission
    @PostMapping("/indexManage/add")
    @ApiOperation("指标管理信息表_增加")
    @BusinessLog(title = "指标管理信息表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(IndexManageParam.add.class) IndexManageParam indexManageParam) {
        indexManageParam.setDelFlag("0");
        indexManageService.add(indexManageParam);
        return new SuccessResponseData();
    }

    /**
     * 删除指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    @Permission
    @PostMapping("/indexManage/delete")
    @ApiOperation("指标管理信息表_删除")
    @BusinessLog(title = "指标管理信息表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(IndexManageParam.delete.class) IndexManageParam indexManageParam) {
        indexManageService.delete(indexManageParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    @Permission
    @PostMapping("/indexManage/edit")
    @ApiOperation("指标管理信息表_编辑")
    @BusinessLog(title = "指标管理信息表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(IndexManageParam.edit.class) IndexManageParam indexManageParam) {
        indexManageService.edit(indexManageParam);
        return new SuccessResponseData();
    }

    /**
     * 查看指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    @Permission
    @GetMapping("/indexManage/detail")
    @ApiOperation("指标管理信息表_查看")
    @BusinessLog(title = "指标管理信息表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(IndexManageParam.detail.class) IndexManageParam indexManageParam) {
        return new SuccessResponseData(indexManageService.detail(indexManageParam));
    }

    /**
     * 指标管理信息表列表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    @Permission
    @GetMapping("/indexManage/list")
    @ApiOperation("指标管理信息表_列表")
    @BusinessLog(title = "指标管理信息表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(IndexManageParam indexManageParam) {
        return new SuccessResponseData(indexManageService.list(indexManageParam));
    }

}
