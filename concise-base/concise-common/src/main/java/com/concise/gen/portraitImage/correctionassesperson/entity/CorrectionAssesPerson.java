package com.concise.gen.portraitImage.correctionassesperson.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 评估管理--评估人员信息
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:42
 */
@Data
@TableName("correction_asses_person")
public class CorrectionAssesPerson {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 评估管理ID
     */
    private String baseId;

    /**
     * 矫正对象id
     */
    private String sqjzryId;

    /**
     * 填写状态：0：未填写 1: 已填写
     */
    private Integer status;

    /**
     * 进度--总次数
     */
    private Integer totalNum;

    /**
     * 进度--完成次数
     */
    private Integer completeNum;

    /**
     * 矫正级别
     */
    @TableField(exist = false)
    private String jzjb;

    /**
     * 矫正级别名称
     */
    @TableField(exist = false)
    private String jzjbName;

    /**
     * 矫正机构
     */
    @TableField(exist = false)
    private String jzjg;

    /**
     * 矫正机构名称
     */
    @TableField(exist = false)
    private String jzjgName;

    /**
     * 矫正对象姓名
     */
    @TableField(exist = false)
    private String sqjzryName;
}
