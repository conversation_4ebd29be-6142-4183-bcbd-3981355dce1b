package com.concise.gen.correctionplaninfomeasureeducation.param;

import com.concise.common.pojo.base.param.BaseParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 矫正方案2.0监管措施_教育帮扶每月课件分类学习记录参数类
 *
 * <AUTHOR>
 * @date 2024-11-21 16:55:24
*/
@Data
public class CorrectionPlanInfoMeasureEducationParam extends BaseParam {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 矫正对象id
     */
    @ApiModelProperty(value = "矫正对象id")
    @NotBlank(message = "矫正对象id不能为空，请检查jzdxId参数", groups = {add.class, edit.class})
    private String jzdxId;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @NotBlank(message = "月份不能为空，请检查month参数", groups = {add.class, edit.class})
    private String month;

    /**
     * 课件分类id
     */
    @ApiModelProperty(value = "课件分类id")
    @NotBlank(message = "课件分类id不能为空，请检查categoryId参数", groups = {add.class, edit.class})
    private String categoryId;

    /**
     * 课件分类
     */
    @ApiModelProperty(value = "课件分类")
    @NotBlank(message = "课件分类不能为空，请检查categoryName参数", groups = {add.class, edit.class})
    private String categoryName;

}
