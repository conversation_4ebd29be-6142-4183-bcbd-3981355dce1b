package com.concise.gen.examquestionitem.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.examquestionitem.entity.ExamQuestionItem;
import com.concise.gen.examquestionitem.enums.ExamQuestionItemExceptionEnum;
import com.concise.gen.examquestionitem.mapper.ExamQuestionItemMapper;
import com.concise.gen.examquestionitem.param.ExamQuestionItemParam;
import com.concise.gen.examquestionitem.service.ExamQuestionItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 问题选项service接口实现类
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:11
 */
@Service
public class ExamQuestionItemServiceImpl extends ServiceImpl<ExamQuestionItemMapper, ExamQuestionItem> implements ExamQuestionItemService {

    @Override
    public PageResult<ExamQuestionItem> page(ExamQuestionItemParam examQuestionItemParam) {
        QueryWrapper<ExamQuestionItem> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(examQuestionItemParam)) {

            // 根据关联exam_question id 查询
            if (ObjectUtil.isNotEmpty(examQuestionItemParam.getQuestionId())) {
                queryWrapper.lambda().eq(ExamQuestionItem::getQuestionId, examQuestionItemParam.getQuestionId());
            }
            // 根据选项内容 查询
            if (ObjectUtil.isNotEmpty(examQuestionItemParam.getContent())) {
                queryWrapper.lambda().eq(ExamQuestionItem::getContent, examQuestionItemParam.getContent());
            }
            // 根据排序字段 查询
            if (ObjectUtil.isNotEmpty(examQuestionItemParam.getSort())) {
                queryWrapper.lambda().eq(ExamQuestionItem::getSort, examQuestionItemParam.getSort());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<ExamQuestionItem> list(ExamQuestionItemParam examQuestionItemParam) {
        return this.list();
    }

    @Override
    public void add(ExamQuestionItemParam examQuestionItemParam) {
        ExamQuestionItem examQuestionItem = new ExamQuestionItem();
        BeanUtil.copyProperties(examQuestionItemParam, examQuestionItem);
        this.save(examQuestionItem);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(ExamQuestionItemParam examQuestionItemParam) {
        this.removeById(examQuestionItemParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ExamQuestionItemParam examQuestionItemParam) {
        ExamQuestionItem examQuestionItem = this.queryExamQuestionItem(examQuestionItemParam);
        BeanUtil.copyProperties(examQuestionItemParam, examQuestionItem);
        this.updateById(examQuestionItem);
    }

    @Override
    public ExamQuestionItem detail(ExamQuestionItemParam examQuestionItemParam) {
        return this.queryExamQuestionItem(examQuestionItemParam);
    }

    /**
     * 获取问题选项
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:11
     */
    private ExamQuestionItem queryExamQuestionItem(ExamQuestionItemParam examQuestionItemParam) {
        ExamQuestionItem examQuestionItem = this.getById(examQuestionItemParam.getId());
        if (ObjectUtil.isNull(examQuestionItem)) {
            throw new ServiceException(ExamQuestionItemExceptionEnum.NOT_EXIST);
        }
        return examQuestionItem;
    }
}
