package com.concise.gen.correctionestimatestay.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimateenter.entity.CorrectionEstimateEnter;
import com.concise.gen.correctionestimateenter.param.CorrectionEstimateEnterParam;
import com.concise.gen.correctionestimatestay.entity.CorrectionEstimateStay;
import com.concise.gen.correctionestimatestay.param.CorrectionEstimateStayParam;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 在矫评估service接口
 *
 * <AUTHOR>
 * @date 2022-05-17 11:48:38
 */
public interface CorrectionEstimateStayService extends IService<CorrectionEstimateStay> {

    /**
     * 查询在矫评估
     *
     * <AUTHOR>
     * @date 2022-05-17 11:48:38
     */
    PageResult<CorrectionEstimateStay> page(CorrectionEstimateStayParam correctionEstimateStayParam, Set<String> org);

    /**
     * 在矫评估列表
     *
     * <AUTHOR>
     * @date 2022-05-17 11:48:38
     */
    List<CorrectionEstimateStay> list(CorrectionEstimateStayParam correctionEstimateStayParam);

    /**
     * 添加在矫评估
     *
     * <AUTHOR>
     * @date 2022-05-17 11:48:38
     */
    void add(CorrectionEstimateStayParam correctionEstimateStayParam, String scoringModelId);

    /**
     * 删除在矫评估
     *
     * <AUTHOR>
     * @date 2022-05-17 11:48:38
     */
    void delete(CorrectionEstimateStayParam correctionEstimateStayParam);

    /**
     * 编辑在矫评估
     *
     * <AUTHOR>
     * @date 2022-05-17 11:48:38
     */
    void edit(CorrectionEstimateStayParam correctionEstimateStayParam, String scoringModelId);

    /**
     * 查看在矫评估
     *
     * <AUTHOR>
     * @date 2022-05-17 11:48:38
     */
     CorrectionEstimateStay detail(CorrectionEstimateStayParam correctionEstimateStayParam);

    /**
     * 根据选中的矫正对象转换下拉选项的值
     *
     * <AUTHOR>
     * @date 2022-05-13 14:52:58
     */
    CorrectionEstimateStay transform(String sqjzryId, int estimateMonth);

    /**
     * 初始化在矫评估数据，每月月初一次
     */
    void initCorrectionEstimateStay();

    /**
     * 每天更新在矫评估分数
     */
    void updateCorrectionEstimateStay();

    /**
     * 计算评估分
     * @param model
     * @param scoringModelId 模型id
     * @return
     */
    BigDecimal[] getScoreEstimate(CorrectionEstimateStay model, String scoringModelId);

    BigDecimal getTotal(String scoringModelId);
}
