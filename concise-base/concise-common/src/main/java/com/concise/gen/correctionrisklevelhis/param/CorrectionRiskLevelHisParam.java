package com.concise.gen.correctionrisklevelhis.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
* 风险等级_历史参数类
 *
 * <AUTHOR>
 * @date 2022-06-06 18:01:04
*/
@Data
public class CorrectionRiskLevelHisParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 评分月度
     */
    @NotNull(message = "评分月度不能为空，请检查scoringTime参数", groups = {add.class, edit.class})
    private Integer scoringTime;

    /**
     * 评分模型ID
     */
    @NotBlank(message = "评分模型ID不能为空，请检查scoringModelId参数", groups = {add.class, edit.class})
    private String scoringModelId;

    /**
     * 矫正对象id
     */
    @NotBlank(message = "矫正对象id不能为空，请检查sqjzryId参数", groups = {add.class, edit.class})
    private String sqjzryId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查sqjzryName参数", groups = {add.class, edit.class})
    private String sqjzryName;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空，请检查sfzh参数", groups = {add.class, edit.class})
    private String sfzh;

    /**
     * 矫正机构ID
     */
    @NotBlank(message = "矫正机构ID不能为空，请检查jzjg参数", groups = {add.class, edit.class})
    private String jzjg;

    /**
     * 矫正机构名称
     */
    @NotBlank(message = "矫正机构名称不能为空，请检查jzjgName参数", groups = {add.class, edit.class})
    private String jzjgName;

    /**
     * 基础分
     */
    @NotNull(message = "基础分不能为空，请检查scoreBase参数", groups = {add.class, edit.class})
    private BigDecimal scoreBase;

    /**
     * 加分项
     */
    @NotNull(message = "加分项不能为空，请检查scoreBonus参数", groups = {add.class, edit.class})
    private BigDecimal scoreBonus;

    /**
     * 评估分(当月)
     */
    @NotNull(message = "评估分(当月)不能为空，请检查scoreEstimate参数", groups = {add.class, edit.class})
    private BigDecimal scoreEstimate;

    /**
     * 风险等级(当月)
     */
    @NotBlank(message = "风险等级(当月)不能为空，请检查riskLevel参数", groups = {add.class, edit.class})
    private String riskLevel;

    /**
     * 评估总分(矫正期)
     */
    @NotNull(message = "评估总分(矫正期)不能为空，请检查scoreEstimateAverage参数", groups = {add.class, edit.class})
    private BigDecimal scoreEstimateAverage;

    /**
     * 风险等级(矫正期)
     */
    @NotBlank(message = "风险等级(矫正期)不能为空，请检查riskLevelAverage参数", groups = {add.class, edit.class})
    private String riskLevelAverage;

    /**
     * 日常监管_实时监控情况
     */
    @NotBlank(message = "日常监管_实时监控情况不能为空，请检查dailySupervision参数", groups = {add.class, edit.class})
    private String dailySupervision;

    /**
     * 处罚_实时监控情况
     */
    @NotBlank(message = "处罚_实时监控情况不能为空，请检查punish参数", groups = {add.class, edit.class})
    private String punish;

    /**
     * 行为动态_实时监控情况
     */
    @NotBlank(message = "行为动态_实时监控情况不能为空，请检查actionTrends参数", groups = {add.class, edit.class})
    private String actionTrends;

    /**
     * 心理动态_实时监控情况
     */
    @NotBlank(message = "心理动态_实时监控情况不能为空，请检查psychicAction参数", groups = {add.class, edit.class})
    private String psychicAction;

    /**
     * 入矫评估_得分占比
     */
    @NotBlank(message = "入矫评估_得分占比不能为空，请检查estimateEnterPercent参数", groups = {add.class, edit.class})
    private String estimateEnterPercent;

    /**
     * 日常监管_得分占比
     */
    @NotBlank(message = "日常监管_得分占比不能为空，请检查dailySupervisionPercent参数", groups = {add.class, edit.class})
    private String dailySupervisionPercent;

    /**
     * 处罚_得分占比
     */
    @NotBlank(message = "处罚_得分占比不能为空，请检查punishPercent参数", groups = {add.class, edit.class})
    private String punishPercent;

    /**
     * 行为动态_得分占比
     */
    @NotBlank(message = "行为动态_得分占比不能为空，请检查actionTrendsPercent参数", groups = {add.class, edit.class})
    private String actionTrendsPercent;

    /**
     * 心理状态_得分占比
     */
    @NotBlank(message = "心理状态_得分占比不能为空，请检查psychicActionPercent参数", groups = {add.class, edit.class})
    private String psychicActionPercent;

    /**
     * 日常监管_普管降严管
     */
    @NotBlank(message = "日常监管_普管降严管不能为空，请检查dailySupervisionDetail参数", groups = {add.class, edit.class})
    private String dailySupervisionDetail;

    /**
     * 日常监管_信息化违规次数
     */
    @NotNull(message = "日常监管_信息化违规次数不能为空，请检查yqViolate参数", groups = {add.class, edit.class})
    private Integer yqViolate;

    /**
     * 处罚_训诫_次
     */
    @NotNull(message = "处罚_训诫_次不能为空，请检查advise参数", groups = {add.class, edit.class})
    private Integer advise;

    /**
     * 处罚_警告_次
     */
    @NotNull(message = "处罚_警告_次不能为空，请检查warn参数", groups = {add.class, edit.class})
    private Integer warn;

    /**
     * 处罚_治安处罚_次
     */
    @NotNull(message = "处罚_治安处罚_次不能为空，请检查publicSecurity参数", groups = {add.class, edit.class})
    private Integer publicSecurity;

    /**
     * 处罚_提请逮捕_次
     */
    @NotNull(message = "处罚_提请逮捕_次不能为空，请检查askArrest参数", groups = {add.class, edit.class})
    private Integer askArrest;

    /**
     * 处罚_提请撤缓_次
     */
    @NotNull(message = "处罚_提请撤缓_次不能为空，请检查cancelProbation参数", groups = {add.class, edit.class})
    private Integer cancelProbation;

    /**
     * 处罚_提请撤销假释_次
     */
    @NotNull(message = "处罚_提请撤销假释_次不能为空，请检查cancelParole参数", groups = {add.class, edit.class})
    private Integer cancelParole;

    /**
     * 处罚_提请收监执行_次
     */
    @NotNull(message = "处罚_提请收监执行_次不能为空，请检查committedToPrison参数", groups = {add.class, edit.class})
    private Integer committedToPrison;

    /**
     * 处罚_行政处罚_次
     */
    @NotNull(message = "处罚_行政处罚_次不能为空，请检查xzPunish参数", groups = {add.class, edit.class})
    private Integer xzPunish;

    /**
     * 行为动态_工作变动_次
     */
    @NotNull(message = "行为动态_工作变动_次不能为空，请检查workChange参数", groups = {add.class, edit.class})
    private Integer workChange;

    /**
     * 行为动态_夜不归宿_次
     */
    @NotNull(message = "行为动态_夜不归宿_次不能为空，请检查nightOut参数", groups = {add.class, edit.class})
    private Integer nightOut;

    /**
     * 心理状态
     */
    @NotBlank(message = "心理状态不能为空，请检查psychicStatus参数", groups = {add.class, edit.class})
    private String psychicStatus;

    /**
     * 总结分析
     */
    @NotBlank(message = "总结分析不能为空，请检查summary参数", groups = {add.class, edit.class})
    private String summary;

    /**
     * 是否删除（0：未删除，1删除）
     */
    @NotNull(message = "是否删除（0：未删除，1删除）不能为空，请检查delFlag参数", groups = {add.class, edit.class})
    private Integer delFlag;

    /**
     * 日常监管得分
     */
    private BigDecimal rcjgScore;

    /**
     * 处罚得分
     */
    private BigDecimal cfScore;

    /**
     * 行为动态得分
     */
    private BigDecimal xwdtScore;

    /**
     * 心理状态得分
     */
    private BigDecimal xlztScore;
}
