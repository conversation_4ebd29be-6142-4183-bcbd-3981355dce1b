<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.correctioncorrectplan.mapper.CorrectionCorrectPlanMapper">

    <resultMap id="planResult" type="com.concise.gen.correctioncorrectplan.entity.CorrectionCorrectPlan">
        <id column="id" property="id" />
        <result column="sqjzry_id" property="sqjzryId" />
        <result column="sqjzry_name" property="sqjzryName" />
        <result column="create_time" property="createTime" />
        <result column="plan_num" property="planNum" />
        <result column="sfzh" property="sfzh" />
        <result column="jzjg_name" property="jzjgName" />
    </resultMap>

    <select id="page" resultMap="planResult">

        SELECT n.*,
            (SELECT COUNT(cp.id) FROM correction_correct_plan cp WHERE cp.sqjzry_id = n.sqjzry_id AND cp.plan_status = 1) AS plan_num
        FROM (
                SELECT
                    cpp.sqjzry_id, MIN(cpp.create_time) as create_time, cpp.sqjzry_name, cpp.sfzh, cpp.jzjg_name
                    FROM precision_correction0.correction_correct_plan cpp, sqjzzxsjk0.correction_object_information coi  ${ew.customSqlSegment} and coi.id = cpp.sqjzry_id

                    <if test="sqjzryName != null and sqjzryName != ''">
                        and coi.xm like concat('%',#{sqjzryName},'%')
                    </if>
                    <if test="0 == tag">
                        and coi.zhuangtai = '200'
                    </if>
                    <if test="1 == tag">
                        and not coi.zhuangtai = '200'
                    </if>
                GROUP BY cpp.sqjzry_id ORDER BY cpp.create_time DESC

        ) n
    </select>
    <select id="pageList" resultType="com.concise.gen.correctioncorrectplan.entity.CorrectionCorrectPlan">
        select  * from  (SELECT
            a.*
        FROM
            precision_correction0.correction_correct_plan a,
            sqjzzxsjk0.correction_object_information b

          where a.sqjzry_id = b.id
          AND b.zhuangtai = '200') n
                            ${ew.customSqlSegment}

    </select>

</mapper>
