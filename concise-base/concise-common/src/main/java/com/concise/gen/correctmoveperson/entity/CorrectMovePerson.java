package com.concise.gen.correctmoveperson.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 流动人口
 *
 * <AUTHOR>
 * @date 2022-05-24 16:51:04
 */
@Data
@TableName("sqjzzxsjk0.correct_move_person")
public class CorrectMovePerson {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 矫正对象id
     */
    private String sqjzryId;

    /**
     * 登记日期
     */
    private String djrq;

    /**
     * 登记状态名称
     */
    private String djztmc;

    /**
     * 到期日期
     */
    private String dqrq;

    /**
     * 出租人公民身份号码
     */
    private String fzsfzh;

    /**
     * 出租人姓名
     */
    private String fzxm;

    /**
     * 街路巷
     */
    private String jlx;

    /**
     * 民族
     */
    private String mz;

    /**
     * 派出所
     */
    private String pcs;
    /**
     * 派出所中文
     */
    private String pcsdm;

    /**
     * 县（市、区）
     */
    private String qx;
    /**
     * 县（市、区）中文
     */
    private String qxdm;

    /**
     * 公民身份号码
     */
    private String sfzh;

    /**
     * 数据归集日期
     */
    private String tongTime;

    /**
     * 性别
     */
    private String xb;
    /**
     * 性别中文名
     */
    private String xbdm;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 居住地址
     */
    private String zzdz;

    /**
     * 居住证号
     */
    private String zzzh;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /**
     * 登记日期_begin
     */
    @TableField(exist = false)
    private String djrq_begin;

    /**
     * 登记日期_end
     */
    @TableField(exist = false)
    private String djrq_end;

    /**
     * 矫正机构ID
     */
    @TableField(exist = false)
    private String jzjg;
}
