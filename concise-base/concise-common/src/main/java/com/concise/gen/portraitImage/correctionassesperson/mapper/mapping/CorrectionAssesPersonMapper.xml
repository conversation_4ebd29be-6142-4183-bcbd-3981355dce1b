<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.portraitImage.correctionassesperson.mapper.CorrectionAssesPersonMapper">

    <select id="getNum" parameterType="String" resultType="int">
        select SUM(total_num) from correction_asses_person where base_id = #{baseId}
    </select>

    <delete id="delByBaseId" parameterType="String">
        delete from correction_asses_person where base_id = #{baseId}
    </delete>


    <resultMap id="assesPerson" type="com.concise.gen.portraitImage.correctionassesperson.entity.CorrectionAssesPerson">
        <result column="id" property="id" />
        <result column="base_id" property="baseId" />
        <result column="sqjzry_id" property="sqjzryId" />
        <result column="status" property="status" />
        <result column="total_num" property="totalNum" />
        <result column="complete_num" property="completeNum" />
        <result column="jzjb_name" property="jzjbName" />
        <result column="jzjg_name" property="jzjgName" />
        <result column="sqjzry_name" property="sqjzryName" />
    </resultMap>

    <select id="page" resultMap="assesPerson">
        select p.*, obj.jzjb_name, obj.jzjg_name, obj.xm as sqjzry_name
        FROM correction_asses_person p, sqjzzxsjk0.correction_object_information obj
        where p.sqjzry_id = obj.id
            and obj.jzjg in
            <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        <if test="baseId != null and baseId != ''">
            and p.base_id = #{baseId}
        </if>
        <if test="status != null and status != ''">
            and p.status = #{status}
        </if>
        <if test="jzjb != null and jzjb != ''">
            and obj.jzjb = #{jzjb}
        </if>
        <if test="sqjzryName != null and sqjzryName != ''">
            and obj.xm like concat('%',#{sqjzryName},'%')
        </if>
        ORDER BY p.status desc
    </select>
</mapper>
