package com.concise.gen.portraitImage.correctionassespersondtl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.portraitImage.correctionassespersondtl.entity.CorrectionAssesPersonDtl;
import com.concise.gen.portraitImage.correctionassespersondtl.param.CorrectionAssesPersonDtlParam;

import java.util.List;

/**
 * 评估管理--评估人员评估明细service接口
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:43
 */
public interface CorrectionAssesPersonDtlService extends IService<CorrectionAssesPersonDtl> {

    /**
     * 查询评估管理--评估人员评估明细
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:43
     */
    PageResult<CorrectionAssesPersonDtl> page(CorrectionAssesPersonDtlParam correctionAssesPersonDtlParam);

    /**
     * 评估管理--评估人员评估明细列表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:43
     */
    List<CorrectionAssesPersonDtl> list(CorrectionAssesPersonDtlParam correctionAssesPersonDtlParam);

    /**
     * 添加评估管理--评估人员评估明细
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:43
     */
    void add(CorrectionAssesPersonDtlParam correctionAssesPersonDtlParam);

    /**
     * 删除评估管理--评估人员评估明细
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:43
     */
    void delete(CorrectionAssesPersonDtlParam correctionAssesPersonDtlParam);

    /**
     * 编辑评估管理--评估人员评估明细
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:43
     */
    void edit(CorrectionAssesPersonDtlParam correctionAssesPersonDtlParam);

    /**
     * 查看评估管理--评估人员评估明细
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:43
     */
     CorrectionAssesPersonDtl detail(CorrectionAssesPersonDtlParam correctionAssesPersonDtlParam);
}
