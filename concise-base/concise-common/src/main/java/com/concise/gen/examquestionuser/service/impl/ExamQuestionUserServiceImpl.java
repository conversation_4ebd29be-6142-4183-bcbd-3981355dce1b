package com.concise.gen.examquestionuser.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.examquestionuser.entity.ExamQuestionUser;
import com.concise.gen.examquestionuser.enums.ExamQuestionUserExceptionEnum;
import com.concise.gen.examquestionuser.mapper.ExamQuestionUserMapper;
import com.concise.gen.examquestionuser.param.ExamQuestionUserParam;
import com.concise.gen.examquestionuser.service.ExamQuestionUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户答题表service接口实现类
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:27
 */
@Service
public class ExamQuestionUserServiceImpl extends ServiceImpl<ExamQuestionUserMapper, ExamQuestionUser> implements ExamQuestionUserService {

    @Override
    public PageResult<ExamQuestionUser> page(ExamQuestionUserParam examQuestionUserParam) {
        QueryWrapper<ExamQuestionUser> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(examQuestionUserParam)) {

            // 根据 查询
            if (ObjectUtil.isNotEmpty(examQuestionUserParam.getUserId())) {
                queryWrapper.lambda().eq(ExamQuestionUser::getUserId, examQuestionUserParam.getUserId());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(examQuestionUserParam.getManageId())) {
                queryWrapper.lambda().eq(ExamQuestionUser::getManageId, examQuestionUserParam.getManageId());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(examQuestionUserParam.getQuestionId())) {
                queryWrapper.lambda().eq(ExamQuestionUser::getQuestionId, examQuestionUserParam.getQuestionId());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(examQuestionUserParam.getAnswer())) {
                queryWrapper.lambda().eq(ExamQuestionUser::getAnswer, examQuestionUserParam.getAnswer());
            }
            // 根据分数 查询
            if (ObjectUtil.isNotEmpty(examQuestionUserParam.getScore())) {
                queryWrapper.lambda().eq(ExamQuestionUser::getScore, examQuestionUserParam.getScore());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<ExamQuestionUser> list(ExamQuestionUserParam examQuestionUserParam) {
        return this.list();
    }

    @Override
    public void add(ExamQuestionUserParam examQuestionUserParam) {
        ExamQuestionUser examQuestionUser = new ExamQuestionUser();
        BeanUtil.copyProperties(examQuestionUserParam, examQuestionUser);
        this.save(examQuestionUser);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(ExamQuestionUserParam examQuestionUserParam) {
        this.removeById(examQuestionUserParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ExamQuestionUserParam examQuestionUserParam) {
        ExamQuestionUser examQuestionUser = this.queryExamQuestionUser(examQuestionUserParam);
        BeanUtil.copyProperties(examQuestionUserParam, examQuestionUser);
        this.updateById(examQuestionUser);
    }

    @Override
    public ExamQuestionUser detail(ExamQuestionUserParam examQuestionUserParam) {
        return this.queryExamQuestionUser(examQuestionUserParam);
    }

    /**
     * 获取用户答题表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:27
     */
    private ExamQuestionUser queryExamQuestionUser(ExamQuestionUserParam examQuestionUserParam) {
        ExamQuestionUser examQuestionUser = this.getById(examQuestionUserParam.getId());
        if (ObjectUtil.isNull(examQuestionUser)) {
            throw new ServiceException(ExamQuestionUserExceptionEnum.NOT_EXIST);
        }
        return examQuestionUser;
    }
}
