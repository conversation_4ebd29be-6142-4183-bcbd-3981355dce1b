package com.concise.gen.correctionestimatereport.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.correctionestimatereport.entity.CorrectionEstimateReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 评估报告
 *
 * <AUTHOR>
 * @date 2023-01-09 10:57:38
 */
public interface CorrectionEstimateReportMapper extends BaseMapper<CorrectionEstimateReport> {
    /**
     * 查询最近6个月的评估报告id
     */
    List<CorrectionEstimateReport> getIdList(@Param("sqjzryId") String sqjzryId, @Param("startMonth") int startMonth, @Param("endMonth") int endMonth);
}
