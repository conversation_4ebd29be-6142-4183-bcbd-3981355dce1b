package com.concise.gen.indexmanageportrayal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 指标管理信息表
 *
 * <AUTHOR>
 * @date 2022-05-12 10:02:19
 */
@Data
@TableName("index_manage_portrayal")
public class IndexManagePortrayal {

    /**
     * 指标ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 指标分类
     */
    private String indexType;

    private Date createTime;

    /**
     * 删除状态
     */
    private String delFlag;

    /**
     *
     */
    private String extend01;

    /**
     *
     */
    private String extend02;

    /**
     *
     */
    private String extend03;

}
