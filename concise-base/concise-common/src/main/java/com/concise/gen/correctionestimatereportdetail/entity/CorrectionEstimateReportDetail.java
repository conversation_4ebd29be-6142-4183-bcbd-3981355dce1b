package com.concise.gen.correctionestimatereportdetail.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 评估报告详情
 *
 * <AUTHOR>
 * @date 2023-01-10 15:55:35
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_estimate_report_detail")
public class CorrectionEstimateReportDetail extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 评估报告id
     */
    private String reportId;

    /**
     * 指标
     */
    private Integer indexType;

    /**
     * 指标名称
     */
    private String indexName;

    /**
     * 当前分数
     */
    private BigDecimal score;

    /**
     * 情况描述
     */
    private String conditions;

}
