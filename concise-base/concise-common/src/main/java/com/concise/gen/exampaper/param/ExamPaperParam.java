package com.concise.gen.exampaper.param;

import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.examquestion.entity.ExamQuestion;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
* 新量表管理参数类
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:03
*/
@Data
public class ExamPaperParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 量表名称
     */
    @NotBlank(message = "量表名称不能为空，请检查title参数", groups = {add.class, edit.class})
    private String title;

    /**
     * 启用情况（0：启用 1：停用）
     */
//    @NotBlank(message = "启用情况（0：启用 1：停用）不能为空，请检查status参数", groups = {add.class, edit.class})
    private Integer status;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private String delFlag;

    private String createUser;

    private String updateUser;

    private List<String> questionList;

}
