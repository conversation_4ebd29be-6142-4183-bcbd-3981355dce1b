package com.concise.gen.correctionlabelmanage.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.concise.gen.correctionlabelmanage.entity.CorrectionLabelManage;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 标签管理
 *
 * <AUTHOR>
 * @date 2022-03-03 09:48:05
 */
public interface CorrectionLabelManageMapper extends BaseMapper<CorrectionLabelManage> {

    /**
     *  列表查询
     * @param page 分页对象
     * @param queryWrapper 查询条件
     * @return
     */
    Page<CorrectionLabelManage> page(@Param("page") Page page, @Param("ew") QueryWrapper queryWrapper);

    /**
     * 根据能力id查找关联的标签信息
     * @param abilityId
     * @return
     */
    List<CorrectionLabelManage> findByAbilityId(Long abilityId);

    /**
     * 根据案例id查找关联的标签信息
     * @param caseId
     * @return
     */
    List<CorrectionLabelManage> findByCaseId(Long caseId);
}
