package com.concise.gen.correctionestimateportrayalparticulars.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimateportrayalparticulars.entity.CorrectionEstimatePortrayalParticulars;
import com.concise.gen.correctionestimateportrayalparticulars.param.CorrectionEstimatePortrayalParticularsParam;
import java.util.List;

/**
 * 心理画像明细（新）service接口
 *
 * <AUTHOR>
 * @date 2023-01-14 10:38:47
 */
public interface CorrectionEstimatePortrayalParticularsService extends IService<CorrectionEstimatePortrayalParticulars> {

    /**
     * 查询心理画像明细（新）
     *
     * <AUTHOR>
     * @date 2023-01-14 10:38:47
     */
    PageResult<CorrectionEstimatePortrayalParticulars> page(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam);

    /**
     * 心理画像明细（新）列表
     *
     * <AUTHOR>
     * @date 2023-01-14 10:38:47
     */
    List<CorrectionEstimatePortrayalParticulars> list(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam);

    /**
     * 添加心理画像明细（新）
     *
     * <AUTHOR>
     * @date 2023-01-14 10:38:47
     */
    void add(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam);

    /**
     * 删除心理画像明细（新）
     *
     * <AUTHOR>
     * @date 2023-01-14 10:38:47
     */
    void delete(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam);

    /**
     * 编辑心理画像明细（新）
     *
     * <AUTHOR>
     * @date 2023-01-14 10:38:47
     */
    void edit(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam);

    /**
     * 查看心理画像明细（新）
     *
     * <AUTHOR>
     * @date 2023-01-14 10:38:47
     */
     CorrectionEstimatePortrayalParticulars detail(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam);

    /**
     * 根据画像id查询
     * @param portrayalId
     * @return
     */
     CorrectionEstimatePortrayalParticulars getByPortrayalId(String portrayalId);
}
