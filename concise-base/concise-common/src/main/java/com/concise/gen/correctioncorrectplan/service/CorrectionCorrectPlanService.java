package com.concise.gen.correctioncorrectplan.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctioncorrectplan.entity.CorrectionCorrectPlan;
import com.concise.gen.correctioncorrectplan.param.CorrectionCorrectPlanParam;
import java.util.List;
import java.util.Set;

/**
 * 矫正方案service接口
 *
 * <AUTHOR>
 * @date 2022-03-07 14:27:46
 */
public interface CorrectionCorrectPlanService extends IService<CorrectionCorrectPlan> {

    /**
     * 查询入矫制定、在矫调整列表
     *
     * <AUTHOR>
     * @date 2022-03-07 14:27:46
     */
    PageResult<CorrectionCorrectPlan> page(CorrectionCorrectPlanParam correctionCorrectPlanParam, Set<String> org);

    /**
     * 查询矫正方案、历史方案列表
     *
     * <AUTHOR>
     * @date 2022-03-07 14:27:46
     */
    PageResult<CorrectionCorrectPlan> pagePlan(CorrectionCorrectPlanParam correctionCorrectPlanParam, Set<String> org);


    /**
     * 矫正方案列表
     *
     * <AUTHOR>
     * @date 2022-03-07 14:27:46
     */
    List<CorrectionCorrectPlan> list(CorrectionCorrectPlanParam correctionCorrectPlanParam);

    /**
     * 添加矫正方案
     *
     * <AUTHOR>
     * @date 2022-03-07 14:27:46
     */
    void add(CorrectionCorrectPlanParam correctionCorrectPlanParam);

    /**
     * 删除矫正方案
     *
     * <AUTHOR>
     * @date 2022-03-07 14:27:46
     */
    void delete(CorrectionCorrectPlanParam correctionCorrectPlanParam);

    /**
     * 编辑矫正方案
     *
     * <AUTHOR>
     * @date 2022-03-07 14:27:46
     */
    void edit(CorrectionCorrectPlanParam correctionCorrectPlanParam);

    /**
     * 查看矫正方案
     *
     * <AUTHOR>
     * @date 2022-03-07 14:27:46
     */
     CorrectionCorrectPlan detail(CorrectionCorrectPlanParam correctionCorrectPlanParam);

    /**
     * 获取方案数量
     * @param sqjzryId
     * @return
     */
     int getNum(String sqjzryId);

    /**
     * 一键完善---生成矫正方案措施
     * @param sqjzryId
     * @param correctPhase 有值则查询传的期限的矫正方案，没传则计算
     * @return
     */
     String createPlan(String sqjzryId, String correctPhase);
}
