package com.concise.gen.correctionportrayalassignment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionportrayalassignment.entity.CorrectionPortrayalAssignment;
import com.concise.gen.correctionportrayalassignment.enums.CorrectionPortrayalAssignmentExceptionEnum;
import com.concise.gen.correctionportrayalassignment.mapper.CorrectionPortrayalAssignmentMapper;
import com.concise.gen.correctionportrayalassignment.param.CorrectionPortrayalAssignmentParam;
import com.concise.gen.correctionportrayalassignment.service.CorrectionPortrayalAssignmentService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 画像首页待处理任务service接口实现类
 *
 * <AUTHOR>
 * @date 2023-01-09 09:14:33
 */
@Service
public class CorrectionPortrayalAssignmentServiceImpl extends ServiceImpl<CorrectionPortrayalAssignmentMapper, CorrectionPortrayalAssignment> implements CorrectionPortrayalAssignmentService {

    @Override
    public PageResult<CorrectionPortrayalAssignment> page(CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam) {
        QueryWrapper<CorrectionPortrayalAssignment> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionPortrayalAssignmentParam)) {

            // 根据系统名称 查询
            if (ObjectUtil.isNotEmpty(correctionPortrayalAssignmentParam.getSystemName())) {
                queryWrapper.lambda().eq(CorrectionPortrayalAssignment::getSystemName, correctionPortrayalAssignmentParam.getSystemName());
            }
            // 根据处理类型 查询
            if (ObjectUtil.isNotEmpty(correctionPortrayalAssignmentParam.getModelName())) {
                queryWrapper.lambda().eq(CorrectionPortrayalAssignment::getModelName, correctionPortrayalAssignmentParam.getModelName());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionPortrayalAssignmentParam.getXm())) {
                queryWrapper.lambda().eq(CorrectionPortrayalAssignment::getXm, correctionPortrayalAssignmentParam.getXm());
            }
            // 根据处理标志（0处理、1已处理） 查询
            if (ObjectUtil.isNotEmpty(correctionPortrayalAssignmentParam.getStatus())) {
                queryWrapper.lambda().eq(CorrectionPortrayalAssignment::getStatus, correctionPortrayalAssignmentParam.getStatus());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionPortrayalAssignment> list(CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam) {
        CorrectionPortrayalAssignment correctionPortrayalAssignment = new CorrectionPortrayalAssignment();
        BeanUtil.copyProperties(correctionPortrayalAssignmentParam, correctionPortrayalAssignment);
        this.save(correctionPortrayalAssignment);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam) {
        this.removeById(correctionPortrayalAssignmentParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam) {
        CorrectionPortrayalAssignment correctionPortrayalAssignment = this.queryCorrectionPortrayalAssignment(correctionPortrayalAssignmentParam);
        BeanUtil.copyProperties(correctionPortrayalAssignmentParam, correctionPortrayalAssignment);
        this.updateById(correctionPortrayalAssignment);
    }

    @Override
    public CorrectionPortrayalAssignment detail(CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam) {
        return this.queryCorrectionPortrayalAssignment(correctionPortrayalAssignmentParam);
    }

    /**
     * 获取画像首页待处理任务
     *
     * <AUTHOR>
     * @date 2023-01-09 09:14:33
     */
    private CorrectionPortrayalAssignment queryCorrectionPortrayalAssignment(CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam) {
        CorrectionPortrayalAssignment correctionPortrayalAssignment = this.getById(correctionPortrayalAssignmentParam.getId());
        if (ObjectUtil.isNull(correctionPortrayalAssignment)) {
            throw new ServiceException(CorrectionPortrayalAssignmentExceptionEnum.NOT_EXIST);
        }
        return correctionPortrayalAssignment;
    }
}
