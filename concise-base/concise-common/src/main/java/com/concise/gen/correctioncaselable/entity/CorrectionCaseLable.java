package com.concise.gen.correctioncaselable.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 案例与标签关联表
 *
 * <AUTHOR>
 * @date 2022-03-14 17:18:07
 */
@Data
@TableName("correction_case_lable")
public class CorrectionCaseLable {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 案例id
     */
    private Long caseId;

    /**
     * 标签id
     */
    private String lableId;

    /**
     * 案例IDs
     */
    @TableField(exist = false)
    private String[] caseIds;
    /**
     * 标签IDs
     */
    @TableField(exist = false)
    private String[] labelIds;
}
