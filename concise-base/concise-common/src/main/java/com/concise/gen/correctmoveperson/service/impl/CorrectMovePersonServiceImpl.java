package com.concise.gen.correctmoveperson.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimateenter.entity.CorrectionEstimateEnter;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.gen.correctmoveperson.entity.CorrectMovePerson;
import com.concise.gen.correctmoveperson.enums.CorrectMovePersonExceptionEnum;
import com.concise.gen.correctmoveperson.mapper.CorrectMovePersonMapper;
import com.concise.gen.correctmoveperson.param.CorrectMovePersonParam;
import com.concise.gen.correctmoveperson.service.CorrectMovePersonService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 流动人口service接口实现类
 *
 * <AUTHOR>
 * @date 2022-05-24 16:51:04
 */
@Service
public class CorrectMovePersonServiceImpl extends ServiceImpl<CorrectMovePersonMapper, CorrectMovePerson> implements CorrectMovePersonService {

    @Override
    public PageResult<CorrectMovePerson> page(CorrectMovePersonParam correctMovePersonParam, List<String> sqjzryIdList) {
        QueryWrapper<CorrectMovePerson> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(sqjzryIdList)){
            queryWrapper.lambda().in(CorrectMovePerson::getSqjzryId, sqjzryIdList);
        }
        if (ObjectUtil.isNotNull(correctMovePersonParam)) {
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctMovePersonParam.getXm())) {
                queryWrapper.lambda().like(CorrectMovePerson::getXm, correctMovePersonParam.getXm());
            }
            // 登记时间
            if (ObjectUtil.isNotEmpty(correctMovePersonParam.getDjrq_begin())) {
                queryWrapper.lambda().ge(CorrectMovePerson::getDjrq, correctMovePersonParam.getDjrq_begin().replaceAll("-", ""));
            }
            if (ObjectUtil.isNotEmpty(correctMovePersonParam.getDjrq_end())) {
                queryWrapper.lambda().le(CorrectMovePerson::getDjrq, correctMovePersonParam.getDjrq_end().replaceAll("-", ""));
            }
            // 根据数据归集日期 查询
            if (ObjectUtil.isNotEmpty(correctMovePersonParam.getTongTime())) {
                queryWrapper.lambda().eq(CorrectMovePerson::getTongTime, correctMovePersonParam.getTongTime());
            }
        }
        queryWrapper.lambda().orderByDesc(CorrectMovePerson::getDjrq);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectMovePerson> list(CorrectMovePersonParam correctMovePersonParam) {
        return this.list();
    }

    @Override
    public void add(CorrectMovePersonParam correctMovePersonParam) {
        CorrectMovePerson correctMovePerson = new CorrectMovePerson();
        BeanUtil.copyProperties(correctMovePersonParam, correctMovePerson);
        this.save(correctMovePerson);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectMovePersonParam correctMovePersonParam) {
        this.removeById(correctMovePersonParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectMovePersonParam correctMovePersonParam) {
        CorrectMovePerson correctMovePerson = this.queryCorrectMovePerson(correctMovePersonParam);
        BeanUtil.copyProperties(correctMovePersonParam, correctMovePerson);
        this.updateById(correctMovePerson);
    }

    @Override
    public CorrectMovePerson detail(CorrectMovePersonParam correctMovePersonParam) {
        return this.queryCorrectMovePerson(correctMovePersonParam);
    }

    /**
     * 获取流动人口
     *
     * <AUTHOR>
     * @date 2022-05-24 16:51:04
     */
    private CorrectMovePerson queryCorrectMovePerson(CorrectMovePersonParam correctMovePersonParam) {
        CorrectMovePerson correctMovePerson = this.getById(correctMovePersonParam.getId());
        if (ObjectUtil.isNull(correctMovePerson)) {
            throw new ServiceException(CorrectMovePersonExceptionEnum.NOT_EXIST);
        }
        return correctMovePerson;
    }
}
