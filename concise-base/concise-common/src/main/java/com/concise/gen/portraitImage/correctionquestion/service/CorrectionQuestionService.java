package com.concise.gen.portraitImage.correctionquestion.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.portraitImage.correctionquestion.entity.CorrectionQuestion;
import com.concise.gen.portraitImage.correctionquestion.param.CorrectionQuestionParam;

import java.util.List;

/**
 * 量表配置--试题信息表service接口
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:35
 */
public interface CorrectionQuestionService extends IService<CorrectionQuestion> {

    /**
     * 查询量表配置--试题信息表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:35
     */
    PageResult<CorrectionQuestion> page(CorrectionQuestionParam correctionQuestionParam);

    /**
     * 量表配置--试题信息表列表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:35
     */
    List<CorrectionQuestion> list(CorrectionQuestionParam correctionQuestionParam);

    /**
     * 添加量表配置--试题信息表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:35
     */
    void add(CorrectionQuestionParam correctionQuestionParam);

    /**
     * 删除量表配置--试题信息表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:35
     */
    void delete(CorrectionQuestionParam correctionQuestionParam);

    /**
     * 编辑量表配置--试题信息表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:35
     */
    void edit(CorrectionQuestionParam correctionQuestionParam);

    /**
     * 查看量表配置--试题信息表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:35
     */
     CorrectionQuestion detail(CorrectionQuestionParam correctionQuestionParam);
}
