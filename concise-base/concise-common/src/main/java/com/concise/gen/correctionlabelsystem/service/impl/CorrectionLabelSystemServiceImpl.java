package com.concise.gen.correctionlabelsystem.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionlabelsystem.entity.CorrectionLabelSystem;
import com.concise.gen.correctionlabelsystem.enums.CorrectionLabelSystemExceptionEnum;
import com.concise.gen.correctionlabelsystem.mapper.CorrectionLabelSystemMapper;
import com.concise.gen.correctionlabelsystem.param.CorrectionLabelSystemParam;
import com.concise.gen.correctionlabelsystem.service.CorrectionLabelSystemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 标签所属应用service接口实现类
 *
 * <AUTHOR>
 * @date 2022-08-22 09:04:42
 */
@Service
public class CorrectionLabelSystemServiceImpl extends ServiceImpl<CorrectionLabelSystemMapper, CorrectionLabelSystem> implements CorrectionLabelSystemService {

    @Override
    public PageResult<CorrectionLabelSystem> page(CorrectionLabelSystemParam correctionLabelSystemParam) {
        QueryWrapper<CorrectionLabelSystem> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionLabelSystemParam)) {

            // 根据标签ID 查询
            if (ObjectUtil.isNotEmpty(correctionLabelSystemParam.getLabelId())) {
                queryWrapper.lambda().eq(CorrectionLabelSystem::getLabelId, correctionLabelSystemParam.getLabelId());
            }
            // 根据所属应用,字典值: SSYY 查询
            if (ObjectUtil.isNotEmpty(correctionLabelSystemParam.getSysCode())) {
                queryWrapper.lambda().eq(CorrectionLabelSystem::getSysCode, correctionLabelSystemParam.getSysCode());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionLabelSystem> list(CorrectionLabelSystemParam correctionLabelSystemParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionLabelSystemParam correctionLabelSystemParam) {
        CorrectionLabelSystem correctionLabelSystem = new CorrectionLabelSystem();
        BeanUtil.copyProperties(correctionLabelSystemParam, correctionLabelSystem);
        this.save(correctionLabelSystem);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionLabelSystemParam correctionLabelSystemParam) {
        this.removeById(correctionLabelSystemParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionLabelSystemParam correctionLabelSystemParam) {
        CorrectionLabelSystem correctionLabelSystem = this.queryCorrectionLabelSystem(correctionLabelSystemParam);
        BeanUtil.copyProperties(correctionLabelSystemParam, correctionLabelSystem);
        this.updateById(correctionLabelSystem);
    }

    @Override
    public CorrectionLabelSystem detail(CorrectionLabelSystemParam correctionLabelSystemParam) {
        return this.queryCorrectionLabelSystem(correctionLabelSystemParam);
    }

    /**
     * 获取标签所属应用
     *
     * <AUTHOR>
     * @date 2022-08-22 09:04:42
     */
    private CorrectionLabelSystem queryCorrectionLabelSystem(CorrectionLabelSystemParam correctionLabelSystemParam) {
        CorrectionLabelSystem correctionLabelSystem = this.getById(correctionLabelSystemParam.getId());
        if (ObjectUtil.isNull(correctionLabelSystem)) {
            throw new ServiceException(CorrectionLabelSystemExceptionEnum.NOT_EXIST);
        }
        return correctionLabelSystem;
    }
}
