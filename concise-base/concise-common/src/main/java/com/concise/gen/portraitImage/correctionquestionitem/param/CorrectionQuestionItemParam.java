package com.concise.gen.portraitImage.correctionquestionitem.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.portraitImage.correctionquestion.entity.CorrectionQuestion;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
* 量表配置--试题选项表参数类
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:38
*/
@Data
public class CorrectionQuestionItemParam extends BaseParam {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 问题id
     */
    @ApiModelProperty(value = "问题id")
    private String questionId;

    /**
     * 选项序号
     */
    @ApiModelProperty(value = "选项序号")
    private Integer orderIndex;

    /**
     * 选项内容
     */
    @ApiModelProperty(value = "选项内容")
    private String content;

    /**
     * 是否有关联问题（0：没有 1：有）
     */
    @ApiModelProperty(value = "是否有关联问题（0：没有 1：有）")
    private Integer haveHigherQuestion;

    /**
     * 是否删除（0：未删除，1删除）
     */
    @ApiModelProperty(value = "是否删除（0：未删除，1删除）")
    private Integer delFlag;

    /**
     * 问题列表
     */
    @ApiModelProperty(value = "问题列表")
    private List<CorrectionQuestion> questionList;

    /**
     * 是否选中， true 选中   false 未选中
     */
    @ApiModelProperty(value = "是否选中， true 选中   false 未选中")
    private boolean isChecked;
}
