package com.concise.gen.correctionobjectinformation.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.correctionlabelcorrpsn.entity.CorrectionLabelCorrpsn;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformationNoEncryption;
import com.concise.gen.correctionobjectinformation.entity.ScreenModel;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 矫正对象信息表
 *
 * <AUTHOR>
 * @date 2021-12-07 14:19:57
 */
@DS("slave")
public interface CorrectionObjectInformationMapper extends BaseMapper<CorrectionObjectInformation> {

    /**
     * 只获取id和sfzh(只取在矫的)
     *
     * @return
     */
    List<CorrectionObjectInformation> getCorrection();

    /**
     * 根据身份证号查询在矫矫正对象
     *
     * @param sfzh
     * @return
     */
    List<CorrectionObjectInformation> getBySfzh(String sfzh);


    /**
     * 只取在矫的
     *
     * @return
     */
    List<CorrectionObjectInformation> getCorrectionMultifield();


    /**
     * 只取在矫的（身份证不替换）
     *
     * @return
     */
    List<CorrectionObjectInformationNoEncryption> getCorrectionMultifieldNoEncryption();

    CorrectionObjectInformationNoEncryption selectByIdNoEncryption(@Param("id") String id);

    List<CorrectionLabelCorrpsn> findCorrectionObjectInformation(@Param("fieldName") String fieldName, @Param("dictNames") List<String> dictNames);

    List<CorrectionLabelCorrpsn> findCorrectionBan(@Param("fieldName") String fieldName, @Param("dictNames") List<String> dictNames);

    List<CorrectionLabelCorrpsn> findCorrectionCrime(@Param("fieldName") String fieldName, @Param("dictNames") List<String> dictNames);

    List<CorrectionLabelCorrpsn> findCorrectionRisk(@Param("fieldName") String fieldName, @Param("dictNames") List<String> dictNames);

    List<CorrectionLabelCorrpsn> findExam(@Param("fieldName") String fieldName, @Param("dictNames") List<String> dictNames);

    List<ScreenModel> moodCode(@Param("userIds") Set<String> userIds);

    Date getJiejiaoriqiById(@Param("id") String id);
}
