package com.concise.gen.correctionportrayalassignment. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.correctionportrayalassignment. param.CorrectionPortrayalAssignmentParam;
import com.concise.gen.correctionportrayalassignment. service.CorrectionPortrayalAssignmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 画像首页待处理任务控制器
 *
 * <AUTHOR>
 * @date 2023-01-09 09:14:33
 */
@Api(tags = "画像首页待处理任务")
@RestController
public class CorrectionPortrayalAssignmentController {

    @Resource
    private CorrectionPortrayalAssignmentService correctionPortrayalAssignmentService;

    /**
     * 查询画像首页待处理任务
     *
     * <AUTHOR>
     * @date 2023-01-09 09:14:33
     */
    @Permission
    @GetMapping("/correctionPortrayalAssignment/page")
    @ApiOperation("画像首页待处理任务_分页查询")
    @BusinessLog(title = "画像首页待处理任务_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam) {
        return new SuccessResponseData(correctionPortrayalAssignmentService.page(correctionPortrayalAssignmentParam));
    }

    /**
     * 添加画像首页待处理任务
     *
     * <AUTHOR>
     * @date 2023-01-09 09:14:33
     */
    @Permission
    @PostMapping("/correctionPortrayalAssignment/add")
    @ApiOperation("画像首页待处理任务_增加")
    @BusinessLog(title = "画像首页待处理任务_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(CorrectionPortrayalAssignmentParam.add.class) CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam) {
        correctionPortrayalAssignmentService.add(correctionPortrayalAssignmentParam);
        return new SuccessResponseData();
    }

    /**
     * 删除画像首页待处理任务
     *
     * <AUTHOR>
     * @date 2023-01-09 09:14:33
     */
    @Permission
    @PostMapping("/correctionPortrayalAssignment/delete")
    @ApiOperation("画像首页待处理任务_删除")
    @BusinessLog(title = "画像首页待处理任务_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(CorrectionPortrayalAssignmentParam.delete.class) CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam) {
        correctionPortrayalAssignmentService.delete(correctionPortrayalAssignmentParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑画像首页待处理任务
     *
     * <AUTHOR>
     * @date 2023-01-09 09:14:33
     */
    @Permission
    @PostMapping("/correctionPortrayalAssignment/edit")
    @ApiOperation("画像首页待处理任务_编辑")
    @BusinessLog(title = "画像首页待处理任务_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(CorrectionPortrayalAssignmentParam.edit.class) CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam) {
        correctionPortrayalAssignmentService.edit(correctionPortrayalAssignmentParam);
        return new SuccessResponseData();
    }

    /**
     * 查看画像首页待处理任务
     *
     * <AUTHOR>
     * @date 2023-01-09 09:14:33
     */
    @Permission
    @GetMapping("/correctionPortrayalAssignment/detail")
    @ApiOperation("画像首页待处理任务_查看")
    @BusinessLog(title = "画像首页待处理任务_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(CorrectionPortrayalAssignmentParam.detail.class) CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam) {
        return new SuccessResponseData(correctionPortrayalAssignmentService.detail(correctionPortrayalAssignmentParam));
    }

    /**
     * 画像首页待处理任务列表
     *
     * <AUTHOR>
     * @date 2023-01-09 09:14:33
     */
    @Permission
    @GetMapping("/correctionPortrayalAssignment/list")
    @ApiOperation("画像首页待处理任务_列表")
    @BusinessLog(title = "画像首页待处理任务_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(CorrectionPortrayalAssignmentParam correctionPortrayalAssignmentParam) {
        return new SuccessResponseData(correctionPortrayalAssignmentService.list(correctionPortrayalAssignmentParam));
    }

}
