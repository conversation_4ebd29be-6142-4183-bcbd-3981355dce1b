package com.concise.gen.correctioncrimepartner.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
 * 同案犯参数类
 *
 * <AUTHOR>
 * @date 2022-02-22 16:07:46
 */
@Data
public class CorrectionCrimePartnerParam extends BaseParam {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 社区矫正人员标识
     */
    @NotBlank(message = "社区矫正人员标识不能为空，请检查pid参数", groups = {add.class, edit.class})
    private String pid;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空，请检查xb参数", groups = {add.class, edit.class})
    private String xb;

    /**
     * 性别name
     */
    @NotBlank(message = "性别name不能为空，请检查xbName参数", groups = {add.class, edit.class})
    private String xbName;

    /**
     * 出生日期
     */
    @NotNull(message = "出生日期不能为空，请检查csrq参数", groups = {add.class, edit.class})
    private String csrq;

    /**
     * 罪名
     */
    @NotBlank(message = "罪名不能为空，请检查szdw参数", groups = {add.class, edit.class})
    private String szdw;

    /**
     * 罪名name
     */
    @NotBlank(message = "罪名name不能为空，请检查szdwName参数", groups = {add.class, edit.class})
    private String szdwName;

    /**
     * 被判处刑罚及所在监所
     */
    @NotBlank(message = "被判处刑罚及所在监所不能为空，请检查bpcxzjszjs参数", groups = {add.class, edit.class})
    private String bpcxzjszjs;

    /**
     * 是否删除（0：未删除，1删除）
     */
    @NotNull(message = "是否删除（0：未删除，1删除）不能为空，请检查delFlag参数", groups = {add.class, edit.class})
    private Integer delFlag;

}
