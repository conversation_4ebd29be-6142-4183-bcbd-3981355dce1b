package com.concise.gen.correctioneducationalrectification.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctioneducationalrectification.entity.CorrectionEducationalRectification;
import com.concise.gen.correctioneducationalrectification.enums.CorrectionEducationalRectificationExceptionEnum;
import com.concise.gen.correctioneducationalrectification.mapper.CorrectionEducationalRectificationMapper;
import com.concise.gen.correctioneducationalrectification.param.CorrectionEducationalRectificationParam;
import com.concise.gen.correctioneducationalrectification.service.CorrectionEducationalRectificationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 教育整顿service接口实现类
 *
 * <AUTHOR>
 * @date 2022-03-10 17:37:39
 */
@Service
public class CorrectionEducationalRectificationServiceImpl extends ServiceImpl<CorrectionEducationalRectificationMapper, CorrectionEducationalRectification> implements CorrectionEducationalRectificationService {

    @Override
    public PageResult<CorrectionEducationalRectification> page(CorrectionEducationalRectificationParam correctionEducationalRectificationParam) {
        QueryWrapper<CorrectionEducationalRectification> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionEducationalRectificationParam)) {

            // 根据标题 查询
            if (ObjectUtil.isNotEmpty(correctionEducationalRectificationParam.getTitle())) {
                queryWrapper.lambda().like(CorrectionEducationalRectification::getTitle, correctionEducationalRectificationParam.getTitle());
            }
        }
        queryWrapper.lambda().eq(CorrectionEducationalRectification::getDelFlag, 0);
        queryWrapper.orderByDesc("create_time");
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionEducationalRectification> list(CorrectionEducationalRectificationParam correctionEducationalRectificationParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionEducationalRectificationParam correctionEducationalRectificationParam) {
        CorrectionEducationalRectification correctionEducationalRectification = new CorrectionEducationalRectification();
        BeanUtil.copyProperties(correctionEducationalRectificationParam, correctionEducationalRectification);
        this.save(correctionEducationalRectification);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionEducationalRectificationParam correctionEducationalRectificationParam) {
        CorrectionEducationalRectification correctionEducationalRectification = new CorrectionEducationalRectification();
        BeanUtil.copyProperties(correctionEducationalRectificationParam, correctionEducationalRectification);
        correctionEducationalRectification.setDelFlag(1);
        this.updateById(correctionEducationalRectification);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionEducationalRectificationParam correctionEducationalRectificationParam) {
        CorrectionEducationalRectification correctionEducationalRectification = this.queryCorrectionEducationalRectification(correctionEducationalRectificationParam);
        BeanUtil.copyProperties(correctionEducationalRectificationParam, correctionEducationalRectification);
        this.updateById(correctionEducationalRectification);
    }

    @Override
    public CorrectionEducationalRectification detail(CorrectionEducationalRectificationParam correctionEducationalRectificationParam) {
        return this.queryCorrectionEducationalRectification(correctionEducationalRectificationParam);
    }

    /**
     * 获取教育整顿
     *
     * <AUTHOR>
     * @date 2022-03-10 17:37:39
     */
    private CorrectionEducationalRectification queryCorrectionEducationalRectification(CorrectionEducationalRectificationParam correctionEducationalRectificationParam) {
        CorrectionEducationalRectification correctionEducationalRectification = this.getById(correctionEducationalRectificationParam.getId());
        if (ObjectUtil.isNull(correctionEducationalRectification)) {
            throw new ServiceException(CorrectionEducationalRectificationExceptionEnum.NOT_EXIST);
        }
        return correctionEducationalRectification;
    }
}
