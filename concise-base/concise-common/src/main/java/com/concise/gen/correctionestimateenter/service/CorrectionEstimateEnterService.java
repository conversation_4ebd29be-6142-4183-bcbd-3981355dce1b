package com.concise.gen.correctionestimateenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimateenter.entity.CorrectionEstimateEnter;
import com.concise.gen.correctionestimateenter.param.CorrectionEstimateEnterParam;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 入矫评估service接口
 *
 * <AUTHOR>
 * @date 2022-05-13 14:52:58
 */
public interface CorrectionEstimateEnterService extends IService<CorrectionEstimateEnter> {

    /**
     * 查询入矫评估
     *
     * <AUTHOR>
     * @date 2022-05-13 14:52:58
     */
    PageResult<CorrectionEstimateEnter> page(CorrectionEstimateEnterParam correctionEstimateEnterParam, Set<String> org);

    /**
     * 入矫评估列表
     *
     * <AUTHOR>
     * @date 2022-05-13 14:52:58
     */
    List<CorrectionEstimateEnter> list(CorrectionEstimateEnterParam correctionEstimateEnterParam);

    /**
     * 添加入矫评估
     *
     * <AUTHOR>
     * @date 2022-05-13 14:52:58
     */
    void add(CorrectionEstimateEnterParam correctionEstimateEnterParam);

    /**
     * 删除入矫评估
     *
     * <AUTHOR>
     * @date 2022-05-13 14:52:58
     */
    void delete(CorrectionEstimateEnterParam correctionEstimateEnterParam);

    /**
     * 编辑入矫评估
     *
     * <AUTHOR>
     * @date 2022-05-13 14:52:58
     */
    void edit(CorrectionEstimateEnterParam correctionEstimateEnterParam);

    /**
     * 查看入矫评估
     *
     * <AUTHOR>
     * @date 2022-05-13 14:52:58
     */
     CorrectionEstimateEnter detail(CorrectionEstimateEnterParam correctionEstimateEnterParam);

    /**
     * 根据选中的矫正对象转换下拉选项的值
     *
     * <AUTHOR>
     * @date 2022-05-13 14:52:58
     */
    CorrectionEstimateEnter transform(CorrectionEstimateEnterParam correctionEstimateEnterParam);

    /**
     * 计算入矫评估得分占比
     * @param sqjzryId
     * @return
     */
    String getPercent(String sqjzryId);
}
