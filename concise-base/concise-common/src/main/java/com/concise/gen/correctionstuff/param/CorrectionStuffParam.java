/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.concise.gen.correctionstuff.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
 * 矫正小组参数类
 *
 * <AUTHOR>
 * @date 2021-09-16 16:39:00
 */
@Data
public class CorrectionStuffParam extends BaseParam {

    /**
     * 唯一标识
     */
    @NotNull(message = "唯一标识不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 社区矫正人员标识
     */
    @NotBlank(message = "社区矫正人员标识不能为空，请检查pid参数", groups = {add.class, edit.class})
    private String pid;

    /**
     * 社区矫正人员姓名
     */
    @NotBlank(message = "社区矫正人员姓名不能为空，请检查pname参数", groups = {add.class, edit.class})
    private String pname;

    /**
     * 小组成员类型
     */
    @NotBlank(message = "小组成员类型不能为空，请检查xzcylx参数", groups = {add.class, edit.class})
    private String xzcylx;

    /**
     * 小组成员类型中文值
     */
    @NotBlank(message = "小组成员类型中文值不能为空，请检查xzcylxName参数", groups = {add.class, edit.class})
    private String xzcylxName;

    /**
     * 小组成员类别
     */
    @NotBlank(message = "小组成员类别不能为空，请检查xzcylb参数", groups = {add.class, edit.class})
    private String xzcylb;

    /**
     * 小组成员类别中文值
     */
    @NotBlank(message = "小组成员类别中文值不能为空，请检查xzcylbName参数", groups = {add.class, edit.class})
    private String xzcylbName;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空，请检查xb参数", groups = {add.class, edit.class})
    private String xb;

    /**
     * 性别中文值
     */
    @NotBlank(message = "性别中文值不能为空，请检查xbName参数", groups = {add.class, edit.class})
    private String xbName;

    /**
     * 出生日期
     */
    @NotNull(message = "出生日期不能为空，请检查csrq参数", groups = {add.class, edit.class})
    private String csrq;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空，请检查sfzh参数", groups = {add.class, edit.class})
    private String sfzh;

    /**
     * 学历
     */
    @NotBlank(message = "学历不能为空，请检查xl参数", groups = {add.class, edit.class})
    private String xl;

    /**
     * 学历中文值
     */
    @NotBlank(message = "学历中文值不能为空，请检查xlName参数", groups = {add.class, edit.class})
    private String xlName;

    /**
     * 最高学位
     */
    @NotBlank(message = "最高学位不能为空，请检查zgxw参数", groups = {add.class, edit.class})
    private String zgxw;

    /**
     * 最高学位中文值
     */
    @NotBlank(message = "最高学位中文值不能为空，请检查zgxwName参数", groups = {add.class, edit.class})
    private String zgxwName;

    /**
     * 政治面貌
     */
    @NotBlank(message = "政治面貌不能为空，请检查zzmm参数", groups = {add.class, edit.class})
    private String zzmm;

    /**
     * 政治面貌中文值
     */
    @NotBlank(message = "政治面貌中文值不能为空，请检查zzmmName参数", groups = {add.class, edit.class})
    private String zzmmName;

    /**
     * 专业
     */
    @NotBlank(message = "专业不能为空，请检查zy参数", groups = {add.class, edit.class})
    private String zy;

    /**
     * 职业
     */
    @NotBlank(message = "职业不能为空，请检查zhiye参数", groups = {add.class, edit.class})
    private String zhiye;

    /**
     * 职业中文值
     */
    @NotBlank(message = "职业中文值不能为空，请检查zhiyeName参数", groups = {add.class, edit.class})
    private String zhiyeName;

    /**
     * 工作单位
     */
    @NotBlank(message = "工作单位不能为空，请检查gzdw参数", groups = {add.class, edit.class})
    private String gzdw;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空，请检查lxdh参数", groups = {add.class, edit.class})
    private String lxdh;

    /**
     * 手机
     */
    @NotBlank(message = "手机不能为空，请检查sj参数", groups = {add.class, edit.class})
    private String sj;

    /**
     * 家庭住址
     */
    @NotBlank(message = "家庭住址不能为空，请检查jtzz参数", groups = {add.class, edit.class})
    private String jtzz;

    /**
     * 社会工作专业类职称
     */
    @NotBlank(message = "社会工作专业类职称不能为空，请检查shgzzylzc参数", groups = {add.class, edit.class})
    private String shgzzylzc;

    /**
     * 社会工作专业类职称中文值
     */
    @NotBlank(message = "社会工作专业类职称中文值不能为空，请检查shgzzylzcName参数", groups = {add.class, edit.class})
    private String shgzzylzcName;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空，请检查bz参数", groups = {add.class, edit.class})
    private String bz;

}
