package com.concise.gen.examquestionuser.enums;

import com.concise.common.annotion.ExpEnumType;
import com.concise.common.consts.SysExpEnumConstant;
import com.concise.common.exception.enums.abs.AbstractBaseExceptionEnum;
import com.concise.common.factory.ExpEnumCodeFactory;

/**
 * 用户答题表
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:27
 */
@ExpEnumType(module = SysExpEnumConstant.SNOWY_SYS_MODULE_EXP_CODE, kind = SysExpEnumConstant.SYS_POS_EXCEPTION_ENUM)
public enum ExamQuestionUserExceptionEnum implements AbstractBaseExceptionEnum {

    /**
     * 数据不存在
     */
    NOT_EXIST(1, "此数据不存在");

    private final Integer code;

    private final String message;
        ExamQuestionUserExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return ExpEnumCodeFactory.getExpEnumCode(this.getClass(), code);
    }

    @Override
    public String getMessage() {
        return message;
    }

}
