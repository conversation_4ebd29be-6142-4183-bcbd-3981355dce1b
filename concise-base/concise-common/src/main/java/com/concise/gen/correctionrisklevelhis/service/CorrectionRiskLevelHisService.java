package com.concise.gen.correctionrisklevelhis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionrisklevelhis.entity.CorrectionRiskLevelHis;
import com.concise.gen.correctionrisklevelhis.param.CorrectionRiskLevelHisParam;
import java.util.List;

/**
 * 风险等级_历史service接口
 *
 * <AUTHOR>
 * @date 2022-06-06 18:01:04
 */
public interface CorrectionRiskLevelHisService extends IService<CorrectionRiskLevelHis> {

    /**
     * 查询风险等级_历史
     *
     * <AUTHOR>
     * @date 2022-06-06 18:01:04
     */
    PageResult<CorrectionRiskLevelHis> page(CorrectionRiskLevelHisParam correctionRiskLevelHisParam);

    /**
     * 风险等级_历史列表
     *
     * <AUTHOR>
     * @date 2022-06-06 18:01:04
     */
    List<CorrectionRiskLevelHis> list(CorrectionRiskLevelHisParam correctionRiskLevelHisParam);

    /**
     * 添加风险等级_历史
     *
     * <AUTHOR>
     * @date 2022-06-06 18:01:04
     */
    void add(CorrectionRiskLevelHisParam correctionRiskLevelHisParam);

    /**
     * 删除风险等级_历史
     *
     * <AUTHOR>
     * @date 2022-06-06 18:01:04
     */
    void delete(CorrectionRiskLevelHisParam correctionRiskLevelHisParam);

    /**
     * 编辑风险等级_历史
     *
     * <AUTHOR>
     * @date 2022-06-06 18:01:04
     */
    void edit(CorrectionRiskLevelHisParam correctionRiskLevelHisParam);

    /**
     * 查看风险等级_历史
     *
     * <AUTHOR>
     * @date 2022-06-06 18:01:04
     */
     CorrectionRiskLevelHis detail(CorrectionRiskLevelHisParam correctionRiskLevelHisParam);
}
