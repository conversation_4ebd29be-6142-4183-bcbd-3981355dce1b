<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.correctionlabeldictionary.mapper.CorrectionLabelDictionaryMapper">

    <select id="getLabelIds" parameterType="String" resultType="String">
          SELECT distinct label_id  FROM correction_label_dictionary WHERE dictionary_code in
        <foreach collection="dictionaryCode" index="index" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
</mapper>
