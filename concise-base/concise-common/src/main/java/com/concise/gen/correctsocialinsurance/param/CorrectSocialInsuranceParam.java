package com.concise.gen.correctsocialinsurance.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 历史社保主表参数类
 *
 * <AUTHOR>
 * @date 2022-06-07 15:08:49
*/
@Data
public class CorrectSocialInsuranceParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 矫正对象id
     */
    @NotBlank(message = "矫正对象id不能为空，请检查sqjzryId参数", groups = {add.class, edit.class})
    private String sqjzryId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空，请检查sfzh参数", groups = {add.class, edit.class})
    private String sfzh;

    /**
     * 单位名称
     */
    @NotBlank(message = "单位名称不能为空，请检查dwmc参数", groups = {add.class, edit.class})
    private String dwmc;

    /**
     * 统一社会信用代码
     */
    @NotBlank(message = "统一社会信用代码不能为空，请检查tydm参数", groups = {add.class, edit.class})
    private String tydm;

    /**
     * 险种
     */
    @NotBlank(message = "险种不能为空，请检查xz参数", groups = {add.class, edit.class})
    private String xz;

    /**
     * 所属月
     */
    @NotBlank(message = "所属月不能为空，请检查month参数", groups = {add.class, edit.class})
    private String month;

    /**
     * 缴费类型
     */
    @NotBlank(message = "缴费类型不能为空，请检查jflx参数", groups = {add.class, edit.class})
    private String jflx;

    /**
     * 统筹区
     */
    @NotBlank(message = "统筹区不能为空，请检查tcq参数", groups = {add.class, edit.class})
    private String tcq;

    /**
     * 统筹区名称
     */
    @NotBlank(message = "统筹区名称不能为空，请检查tcqName参数", groups = {add.class, edit.class})
    private String tcqName;

    /**
     * 行政区划
     */
    @NotBlank(message = "行政区划不能为空，请检查xzqh参数", groups = {add.class, edit.class})
    private String xzqh;

    /**
     * 行政区划名称
     */
    @NotBlank(message = "行政区划名称不能为空，请检查xzqhName参数", groups = {add.class, edit.class})
    private String xzqhName;

    /**
     * 矫正机构ID
     */
    private String jzjg;
}
