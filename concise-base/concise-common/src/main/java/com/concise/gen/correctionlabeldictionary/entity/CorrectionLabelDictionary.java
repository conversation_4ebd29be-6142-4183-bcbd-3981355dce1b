package com.concise.gen.correctionlabeldictionary.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 标签关联字典值表
 *
 * <AUTHOR>
 * @date 2022-03-08 22:24:16
 */
@Data
@TableName("correction_label_dictionary")
public class CorrectionLabelDictionary {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 标签ID
     */
    private String labelId;

    /**
     * 字典code
     */
    private String dictionaryCode;

    /**
     * 字典name
     */
    private String dictionaryName;

    /**
     * 字典类别name
     */
    private String dictionaryTypeName;

}
