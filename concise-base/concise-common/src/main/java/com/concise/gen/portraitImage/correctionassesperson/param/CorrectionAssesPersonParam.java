package com.concise.gen.portraitImage.correctionassesperson.param;

import com.concise.common.pojo.base.param.BaseParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 评估管理--评估人员信息参数类
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:42
*/
@Data
public class CorrectionAssesPersonParam extends BaseParam {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 评估管理ID
     */
    @ApiModelProperty(value = "评估管理ID")
    private String baseId;

    /**
     * 矫正对象id
     */
    @ApiModelProperty(value = "矫正对象id")
    private String sqjzryId;

    /**
     * 填写状态：0：未填写 1: 已填写
     */
    @ApiModelProperty(value = "填写状态：0：未填写 1: 已填写")
    private Integer status;

    /**
     * 进度--总次数
     */
    @ApiModelProperty(value = "进度--总次数")
    private Integer totalNum;

    /**
     * 进度--完成次数
     */
    @ApiModelProperty(value = "进度--完成次数")
    private Integer completeNum;

    /**
     * 矫正级别
     */
    @ApiModelProperty(value = "矫正级别")
    private String jzjb;

    /**
     * 矫正级别名称
     */
    @ApiModelProperty(value = "矫正级别名称")
    private String jzjbName;

    /**
     * 矫正机构
     */
    @ApiModelProperty(value = "矫正机构")
    private String jzjg;

    /**
     * 矫正机构名称
     */
    @ApiModelProperty(value = "矫正机构名称")
    private String jzjgName;

    /**
     * 矫正对象姓名
     */
    @ApiModelProperty(value = "矫正对象姓名")
    private String sqjzryName;
}
