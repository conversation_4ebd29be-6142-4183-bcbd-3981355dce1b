package com.concise.gen.correctioncorrectplan.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.concise.gen.correctioncorrectplan.entity.CorrectionCorrectPlan;
import org.apache.ibatis.annotations.Param;

/**
 * 矫正方案
 *
 * <AUTHOR>
 * @date 2022-03-07 14:27:46
 */
public interface CorrectionCorrectPlanMapper extends BaseMapper<CorrectionCorrectPlan> {

    Page<CorrectionCorrectPlan> page(@Param("page") Page page, @Param("sqjzryName") String sqjzryName, @Param("tag") int tag, @Param("ew") QueryWrapper queryWrapper);

    Page<CorrectionCorrectPlan> pageList(@Param("page") Page page, @Param("ew") QueryWrapper<CorrectionCorrectPlan> queryWrapper);
}
