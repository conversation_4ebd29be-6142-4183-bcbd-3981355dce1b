package com.concise.gen.correctioncase.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 案例库参数类
 *
 * <AUTHOR>
 * @date 2022-03-14 17:18:03
*/
@Data
public class CorrectionCaseParam extends BaseParam {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 案例类型,字典值caseType
     */
    private String caseType;

    /**
     * 案例内容
     */
    private String caseContent;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

}
