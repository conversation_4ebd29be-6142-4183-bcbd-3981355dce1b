package com.concise.gen.correctionstuff.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import cn.afterturn.easypoi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 矫正小组
 *
 * <AUTHOR>
 * @date 2021-09-16 16:39:00
 */
@Data
@TableName("correction_stuff")
public class CorrectionStuff {

    /**
     * 唯一标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "唯一标识")
    private String id;

    /**
     * 社区矫正人员标识
     */
    private String pid;

    /**
     * 社区矫正人员姓名
     */
    private String pname;

    /**
     * 小组成员类型
     */
    private String xzcylx;

    /**
     * 小组成员类型中文值
     */
    private String xzcylxName;

    /**
     * 小组成员类别
     */
    private String xzcylb;

    /**
     * 小组成员类别中文值
     */
    private String xzcylbName;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    private String xb;

    /**
     * 性别中文值
     */
    private String xbName;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    @ApiModelProperty(value = "出生日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date csrq;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 学历
     */
    private String xl;

    /**
     * 学历中文值
     */
    private String xlName;

    /**
     * 最高学位
     */
    private String zgxw;

    /**
     * 最高学位中文值
     */
    private String zgxwName;

    /**
     * 政治面貌
     */
    private String zzmm;

    /**
     * 政治面貌中文值
     */
    private String zzmmName;

    /**
     * 专业
     */
    private String zy;

    /**
     * 职业
     */
    private String zhiye;

    /**
     * 职业中文值
     */
    private String zhiyeName;

    /**
     * 工作单位
     */
    private String gzdw;

    /**
     * 联系电话
     */
    private String lxdh;

    /**
     * 手机
     */
    private String sj;

    /**
     * 家庭住址
     */
    private String jtzz;

    /**
     * 社会工作专业类职称
     */
    private String shgzzylzc;

    /**
     * 社会工作专业类职称中文值
     */
    private String shgzzylzcName;

    /**
     * 备注
     */
    private String bz;

}
