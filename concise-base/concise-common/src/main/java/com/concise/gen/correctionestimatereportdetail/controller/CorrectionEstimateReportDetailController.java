package com.concise.gen.correctionestimatereportdetail. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.correctionestimatereportdetail. param.CorrectionEstimateReportDetailParam;
import com.concise.gen.correctionestimatereportdetail. service.CorrectionEstimateReportDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 评估报告详情控制器
 *
 * <AUTHOR>
 * @date 2023-01-10 15:55:35
 */
@Api(tags = "评估报告详情")
@RestController
public class CorrectionEstimateReportDetailController {

    @Resource
    private CorrectionEstimateReportDetailService correctionEstimateReportDetailService;

    /**
     * 查询评估报告详情
     *
     * <AUTHOR>
     * @date 2023-01-10 15:55:35
     */
    @Permission
    @GetMapping("/correctionEstimateReportDetail/page")
    @ApiOperation("评估报告详情_分页查询")
    @BusinessLog(title = "评估报告详情_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam) {
        return new SuccessResponseData(correctionEstimateReportDetailService.page(correctionEstimateReportDetailParam));
    }

    /**
     * 添加评估报告详情
     *
     * <AUTHOR>
     * @date 2023-01-10 15:55:35
     */
    @Permission
    @PostMapping("/correctionEstimateReportDetail/add")
    @ApiOperation("评估报告详情_增加")
    @BusinessLog(title = "评估报告详情_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(CorrectionEstimateReportDetailParam.add.class) CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam) {
        correctionEstimateReportDetailService.add(correctionEstimateReportDetailParam);
        return new SuccessResponseData();
    }

    /**
     * 删除评估报告详情
     *
     * <AUTHOR>
     * @date 2023-01-10 15:55:35
     */
    @Permission
    @PostMapping("/correctionEstimateReportDetail/delete")
    @ApiOperation("评估报告详情_删除")
    @BusinessLog(title = "评估报告详情_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(CorrectionEstimateReportDetailParam.delete.class) CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam) {
        correctionEstimateReportDetailService.delete(correctionEstimateReportDetailParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑评估报告详情
     *
     * <AUTHOR>
     * @date 2023-01-10 15:55:35
     */
    @Permission
    @PostMapping("/correctionEstimateReportDetail/edit")
    @ApiOperation("评估报告详情_编辑")
    @BusinessLog(title = "评估报告详情_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(CorrectionEstimateReportDetailParam.edit.class) CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam) {
        correctionEstimateReportDetailService.edit(correctionEstimateReportDetailParam);
        return new SuccessResponseData();
    }

    /**
     * 查看评估报告详情
     *
     * <AUTHOR>
     * @date 2023-01-10 15:55:35
     */
    @Permission
    @GetMapping("/correctionEstimateReportDetail/detail")
    @ApiOperation("评估报告详情_查看")
    @BusinessLog(title = "评估报告详情_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(CorrectionEstimateReportDetailParam.detail.class) CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam) {
        return new SuccessResponseData(correctionEstimateReportDetailService.detail(correctionEstimateReportDetailParam));
    }

    /**
     * 评估报告详情列表
     *
     * <AUTHOR>
     * @date 2023-01-10 15:55:35
     */
    @Permission
    @GetMapping("/correctionEstimateReportDetail/list")
    @ApiOperation("评估报告详情_列表")
    @BusinessLog(title = "评估报告详情_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(CorrectionEstimateReportDetailParam correctionEstimateReportDetailParam) {
        return new SuccessResponseData(correctionEstimateReportDetailService.list(correctionEstimateReportDetailParam));
    }

}
