package com.concise.gen.correctiontablefielddict.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctiontablefielddict.entity.CorrectionTableFieldDict;
import com.concise.gen.correctiontablefielddict.enums.CorrectionTableFieldDictExceptionEnum;
import com.concise.gen.correctiontablefielddict.mapper.CorrectionTableFieldDictMapper;
import com.concise.gen.correctiontablefielddict.param.CorrectionTableFieldDictParam;
import com.concise.gen.correctiontablefielddict.service.CorrectionTableFieldDictService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 表字段与字典关联表service接口实现类
 *
 * <AUTHOR>
 * @date 2024-11-27 16:36:14
 */
@Service
public class CorrectionTableFieldDictServiceImpl extends ServiceImpl<CorrectionTableFieldDictMapper, CorrectionTableFieldDict> implements CorrectionTableFieldDictService {

    @Override
    public PageResult<CorrectionTableFieldDict> page(CorrectionTableFieldDictParam correctionTableFieldDictParam) {
        QueryWrapper<CorrectionTableFieldDict> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionTableFieldDictParam)) {

            // 根据数据库名称 查询
            if (ObjectUtil.isNotEmpty(correctionTableFieldDictParam.getDatabaseName())) {
                queryWrapper.lambda().eq(CorrectionTableFieldDict::getDatabaseName, correctionTableFieldDictParam.getDatabaseName());
            }
            // 根据表名称 查询
            if (ObjectUtil.isNotEmpty(correctionTableFieldDictParam.getTableName())) {
                queryWrapper.lambda().eq(CorrectionTableFieldDict::getTableName, correctionTableFieldDictParam.getTableName());
            }
            // 根据字段名称 查询
            if (ObjectUtil.isNotEmpty(correctionTableFieldDictParam.getFieldName())) {
                queryWrapper.lambda().eq(CorrectionTableFieldDict::getFieldName, correctionTableFieldDictParam.getFieldName());
            }
            // 根据字典类型id 查询
            if (ObjectUtil.isNotEmpty(correctionTableFieldDictParam.getDictTypeId())) {
                queryWrapper.lambda().eq(CorrectionTableFieldDict::getDictTypeId, correctionTableFieldDictParam.getDictTypeId());
            }
            // 根据字典类型 查询
            if (ObjectUtil.isNotEmpty(correctionTableFieldDictParam.getDictType())) {
                queryWrapper.lambda().eq(CorrectionTableFieldDict::getDictType, correctionTableFieldDictParam.getDictType());
            }
            // 根据字典类型名称 查询
            if (ObjectUtil.isNotEmpty(correctionTableFieldDictParam.getDictTypeName())) {
                queryWrapper.lambda().eq(CorrectionTableFieldDict::getDictTypeName, correctionTableFieldDictParam.getDictTypeName());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionTableFieldDict> list(CorrectionTableFieldDictParam correctionTableFieldDictParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionTableFieldDictParam correctionTableFieldDictParam) {
        CorrectionTableFieldDict correctionTableFieldDict = new CorrectionTableFieldDict();
        BeanUtil.copyProperties(correctionTableFieldDictParam, correctionTableFieldDict);
        this.save(correctionTableFieldDict);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionTableFieldDictParam correctionTableFieldDictParam) {
        this.removeById(correctionTableFieldDictParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionTableFieldDictParam correctionTableFieldDictParam) {
        CorrectionTableFieldDict correctionTableFieldDict = this.queryCorrectionTableFieldDict(correctionTableFieldDictParam);
        BeanUtil.copyProperties(correctionTableFieldDictParam, correctionTableFieldDict);
        this.updateById(correctionTableFieldDict);
    }

    @Override
    public CorrectionTableFieldDict detail(CorrectionTableFieldDictParam correctionTableFieldDictParam) {
        return this.queryCorrectionTableFieldDict(correctionTableFieldDictParam);
    }

    /**
     * 获取表字段与字典关联表
     *
     * <AUTHOR>
     * @date 2024-11-27 16:36:14
     */
    private CorrectionTableFieldDict queryCorrectionTableFieldDict(CorrectionTableFieldDictParam correctionTableFieldDictParam) {
        CorrectionTableFieldDict correctionTableFieldDict = this.getById(correctionTableFieldDictParam.getId());
        if (ObjectUtil.isNull(correctionTableFieldDict)) {
            throw new ServiceException(CorrectionTableFieldDictExceptionEnum.NOT_EXIST);
        }
        return correctionTableFieldDict;
    }

    @Override
    public void batchAdd(List<CorrectionTableFieldDictParam> correctionTableFieldDictParamList) {
        for (CorrectionTableFieldDictParam correctionTableFieldDictParam : correctionTableFieldDictParamList) {
            CorrectionTableFieldDict correctionTableFieldDict = new CorrectionTableFieldDict();
            BeanUtil.copyProperties(correctionTableFieldDictParam, correctionTableFieldDict);
            //如果不存在才添加
            if (!this.exists(correctionTableFieldDict)) {
                correctionTableFieldDict.setId(IdWorker.getIdStr());
                this.save(correctionTableFieldDict);
            }
        }

    }

    private boolean exists(CorrectionTableFieldDict correctionTableFieldDict) {
        return this.getOne(new QueryWrapper<CorrectionTableFieldDict>().lambda().eq(CorrectionTableFieldDict::getDatabaseName, correctionTableFieldDict.getDatabaseName())
                .eq(CorrectionTableFieldDict::getTableName, correctionTableFieldDict.getTableName())
                .eq(CorrectionTableFieldDict::getFieldName, correctionTableFieldDict.getFieldName())) != null;
    }
}
