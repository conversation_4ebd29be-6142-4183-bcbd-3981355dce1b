<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.correctionlabelcorrpsn.mapper.CorrectionLabelCorrpsnMapper">

    <select id="findSqjzryIds" parameterType="String" resultType="String">
            select sqjzry_id from correction_label_corrpsn where label_id = #{labelId}
    </select>

    <select id="findLabelIds" parameterType="String" resultType="String">
            select label_id from correction_label_corrpsn where sqjzry_id = #{sqjzryId} order by label_id asc
    </select>
    <select id="countUnmarried" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            correction_label_corrpsn
                LEFT JOIN correction_label_manage ON correction_label_corrpsn.label_id = correction_label_manage.id
        WHERE
            (correction_label_corrpsn.label_id = '12345613'
           OR correction_label_corrpsn.label_id = '12345617')
        and  correction_label_corrpsn.sqjzry_id   in
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="countMarried" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            correction_label_corrpsn
                LEFT JOIN correction_label_manage ON correction_label_corrpsn.label_id = correction_label_manage.id
        WHERE
            correction_label_corrpsn.label_id = '12345615'

        and  correction_label_corrpsn.sqjzry_id   in
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="countUnmarriedElderly" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            correction_label_corrpsn
                LEFT JOIN correction_label_manage ON correction_label_corrpsn.label_id = correction_label_manage.id
        WHERE
            correction_label_corrpsn.label_id = '12345614'
        and  correction_label_corrpsn.sqjzry_id   in
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getLabelMeasures" resultType="com.concise.gen.correctionlabelcorrpsn.entity.CorrectionLabelCorrpsn">
        SELECT
            correction_label_corrpsn.*,
            correction_ability_manage.educational_assistance_step AS ability_info,
            correction_ability_manage.id as ability_id,
            correction_ability_manage.pid as ability_pid
        FROM
            correction_label_corrpsn
            INNER JOIN correction_label_table_relation ON correction_label_corrpsn.label_id = correction_label_table_relation.id
            INNER JOIN correction_ability_lable ON correction_label_corrpsn.label_id = correction_ability_lable.lable_id
            INNER JOIN correction_ability_manage ON correction_ability_lable.ability_id = correction_ability_manage.id
        WHERE
        correction_ability_manage.del_flag = 0 and
            correction_label_corrpsn.id IN
        <foreach collection="labelIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getAbilityMeasures"
            resultType="com.concise.gen.correctionabilitymanage.entity.CorrectionAbilityManage">
        SELECT
            correction_ability_manage.*
        FROM
            correction_ability_lable
                INNER JOIN correction_ability_manage ON correction_ability_lable.ability_id = correction_ability_manage.id
        WHERE
            correction_ability_manage.del_flag = 0 and
            correction_ability_lable.lable_id IN
        <foreach collection="labelIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <update id="updateLabelNameByNativeSql">
        UPDATE correction_label_corrpsn c 
        INNER JOIN correction_label_table_relation r ON c.label_id = r.id 
        SET c.label_name = r.label 
        WHERE (c.label_name IS NULL OR c.label_name = '') AND r.del_flag = 0
    </update>

</mapper>
