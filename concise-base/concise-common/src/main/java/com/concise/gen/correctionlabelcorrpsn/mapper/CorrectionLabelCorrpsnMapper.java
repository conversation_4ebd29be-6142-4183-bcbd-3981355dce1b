package com.concise.gen.correctionlabelcorrpsn.mapper;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.correctionabilitymanage.entity.CorrectionAbilityManage;
import com.concise.gen.correctionlabelcorrpsn.entity.CorrectionLabelCorrpsn;

/**
 * 标签关联矫正对象
 *
 * <AUTHOR>
 * @date 2022-03-04 14:58:19
 */
public interface CorrectionLabelCorrpsnMapper extends BaseMapper<CorrectionLabelCorrpsn> {

    /**
     * 根据标签id查询关联的矫正对象id集合
     * @param labelId
     * @return
     */
    List<String> findSqjzryIds(String labelId);

    /**
     * 根据矫正对象id查询关联的标签id集合
     * @param sqjzryId
     * @return
     */
    List<String> findLabelIds(String sqjzryId);

    int countUnmarried(@Param("userIds") Set<String> userIds);

    int countMarried(@Param("userIds") Set<String> userIds);

    int countUnmarriedElderly(@Param("userIds") Set<String> userIds);

    List<CorrectionLabelCorrpsn> getLabelMeasures(@Param("labelIds") Set<String> labelIds);

    List<CorrectionAbilityManage> getAbilityMeasures(@Param("labelIds") Set<String> labelIds);
    
    /**
     * 更新标签名称为空的数据
     * @return 更新的记录数
     */
    int updateLabelNameByNativeSql();
}
