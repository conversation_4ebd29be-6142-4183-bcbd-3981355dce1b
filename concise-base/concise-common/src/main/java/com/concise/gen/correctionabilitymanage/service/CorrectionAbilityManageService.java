package com.concise.gen.correctionabilitymanage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionabilitymanage.entity.CorrectionAbilityManage;
import com.concise.gen.correctionabilitymanage.param.CorrectionAbilityManageParam;
import com.concise.gen.correctionlabelmanage.entity.CorrectionLabelManage;

import java.util.List;

/**
 * 能力管理service接口
 *
 * <AUTHOR>
 * @date 2022-03-03 10:57:44
 */
public interface CorrectionAbilityManageService extends IService<CorrectionAbilityManage> {

    /**
     * 查询能力管理
     *
     * <AUTHOR>
     * @date 2022-03-03 10:57:44
     */
    PageResult<CorrectionAbilityManage> page(CorrectionAbilityManageParam correctionAbilityManageParam);

    /**
     * 能力管理列表
     *
     * <AUTHOR>
     * @date 2022-03-03 10:57:44
     */
    List<CorrectionAbilityManage> list(CorrectionAbilityManageParam correctionAbilityManageParam);

    /**
     * 添加能力管理
     *
     * <AUTHOR>
     * @date 2022-03-03 10:57:44
     */
    void add(CorrectionAbilityManageParam correctionAbilityManageParam);

    /**
     * 删除能力管理
     *
     * <AUTHOR>
     * @date 2022-03-03 10:57:44
     */
    void delete(CorrectionAbilityManageParam correctionAbilityManageParam);

    /**
     * 编辑能力管理
     *
     * <AUTHOR>
     * @date 2022-03-03 10:57:44
     */
    void edit(CorrectionAbilityManageParam correctionAbilityManageParam);

    /**
     * 查看能力管理
     *
     * <AUTHOR>
     * @date 2022-03-03 10:57:44
     */
     CorrectionAbilityManage detail(CorrectionAbilityManageParam correctionAbilityManageParam);
}
