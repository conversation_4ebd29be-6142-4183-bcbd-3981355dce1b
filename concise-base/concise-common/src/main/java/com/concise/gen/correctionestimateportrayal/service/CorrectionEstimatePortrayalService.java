package com.concise.gen.correctionestimateportrayal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimateportrayal.entity.CorrectionEstimatePortrayal;
import com.concise.gen.correctionestimateportrayal.param.CorrectionEstimatePortrayalParam;
import com.concise.gen.scoringmodeldetailmanageportrayal.entity.ScoringModelDetailManagePortrayal;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 在矫评估service接口
 *
 * <AUTHOR>
 * @date 2022-05-17 11:48:38
 */
public interface CorrectionEstimatePortrayalService extends IService<CorrectionEstimatePortrayal> {

    /**
     * 查询在矫评估
     *
     * <AUTHOR>
     * @date 2022-05-17 11:48:38
     */
    PageResult<CorrectionEstimatePortrayal> page(CorrectionEstimatePortrayalParam correctionEstimatePortrayalParam, Set<String> org);

    /**
     * 在矫评估列表
     *
     * <AUTHOR>
     * @date 2022-05-17 11:48:38
     */
    List<CorrectionEstimatePortrayal> list(CorrectionEstimatePortrayalParam correctionEstimatePortrayalParam);

    /**
     * 添加在矫评估
     *
     * <AUTHOR>
     * @date 2022-05-17 11:48:38
     */
    void add(CorrectionEstimatePortrayalParam correctionEstimatePortrayalParam, String scoringModelId);

    /**
     * 删除在矫评估
     *
     * <AUTHOR>
     * @date 2022-05-17 11:48:38
     */
    void delete(CorrectionEstimatePortrayalParam correctionEstimatePortrayalParam);

    /**
     * 编辑在矫评估
     *
     * <AUTHOR>
     * @date 2022-05-17 11:48:38
     */
    void edit(CorrectionEstimatePortrayalParam correctionEstimatePortrayalParam, String scoringModelId);

    /**
     * 查看在矫评估
     *
     * <AUTHOR>
     * @date 2022-05-17 11:48:38
     */
    CorrectionEstimatePortrayal detail(CorrectionEstimatePortrayalParam correctionEstimatePortrayalParam);

    /**
     * 根据选中的矫正对象转换下拉选项的值
     *
     * <AUTHOR>
     * @date 2022-05-13 14:52:58
     */
    CorrectionEstimatePortrayal transform(String sqjzryId, int estimateMonth);

    /**
     * 初始化在矫评估数据，每月月初一次
     */
    void initCorrectionEstimateStay();

    /**
     * 每天更新在矫评估分数
     */
    void updateCorrectionEstimateStay();

    /**
     * 计算评估分
     * @param portrayal
     * @param scoringModelDetailManagePortrayals 模型
     * @return
     */
    CorrectionEstimatePortrayal getScoreEstimate(CorrectionEstimatePortrayal portrayal, List<ScoringModelDetailManagePortrayal> scoringModelDetailManagePortrayals);


    /**
     * 计算评估分
     * @param portrayal
     * @param scoringModelDetailManagePortrayals 模型
     * @return
     */

    void getScoreEstimateNew(CorrectionEstimatePortrayal portrayal, List<ScoringModelDetailManagePortrayal> scoringModelDetailManagePortrayals);

    BigDecimal getTotal(String scoringModelId);

    /**
     * 根据矫正对象id查询最新评估
     * @param ryid
     * @return
     */
    CorrectionEstimatePortrayal newestByRyid(String ryid);


    /**
     * 得分计算
     * @param correctionEstimatePortrayal
     * @return
     */
    CorrectionEstimatePortrayal calculateScore(CorrectionEstimatePortrayal correctionEstimatePortrayal);

}
