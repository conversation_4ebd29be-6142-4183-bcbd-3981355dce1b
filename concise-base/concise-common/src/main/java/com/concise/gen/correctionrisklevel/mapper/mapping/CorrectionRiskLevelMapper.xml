<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.correctionrisklevel.mapper.CorrectionRiskLevelMapper">
    <select id="getInfo" parameterType="String" resultType="com.concise.gen.correctionrisklevel.entity.CorrectionRiskLevel">
        select id, rcjg_score, cf_score, xwdt_score, xlzt_score from correction_risk_level where scoring_time = #{scoringTime} and sqjzry_id = #{sqjzryId} and del_flag = 0
    </select>

    <!-- 日常监管 -->
    <select id="findRcjg" parameterType="String" resultType="com.concise.gen.correctionrisklevel.entity.CorrectionRiskLevel">
        SELECT date_format(bdrq, '%Y-%m-%d') AS time, '普管降到严管' AS content FROM sqjzzxsjk0.correction_level  WHERE pid = #{pid} AND del_flag = 0 AND date_format(bdrq, '%Y%m') = #{estimateMonth} and gljb = '1'
        UNION ALL
        SELECT date_format(alert_time, '%Y-%m-%d') AS time, alert_type_text AS content FROM ccgf0.ccgf_abnormal_handle WHERE correction_object_id = #{correctionObjectId} AND date_format(alert_time, '%Y%m') = #{estimateMonth} and handle_result &lt; 205
    </select>

    <!-- 处罚 -->
    <select id="findListCf" parameterType="String" resultType="com.concise.gen.correctionrisklevel.entity.CorrectionRiskLevel">
        SELECT DATE_FORMAT(sfssqsj, '%Y-%m-%d') AS time, CONCAT('训诫:', IFNULL(jgss,'')) AS content FROM sqjzzxsjk0.correction_xj WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT DATE_FORMAT(sfssqsj, '%Y-%m-%d') AS time, CONCAT('警告:', IFNULL(jgly,'')) AS content FROM sqjzzxsjk0.correction_warning WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT DATE_FORMAT(sfssqsj, '%Y-%m-%d') AS time, CONCAT('治安处罚:', IFNULL(tqly,'')) AS content FROM sqjzzxsjk0.correction_penalty WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT DATE_FORMAT(sfssqsj, '%Y-%m-%d') AS time, CONCAT('提请逮捕:', IFNULL(tqly,'')) AS content FROM sqjzzxsjk0.correction_arrest  WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT DATE_FORMAT(sfssqsj, '%Y-%m-%d') AS time, CONCAT('提请撤缓:', IFNULL(tqly,'')) AS content FROM sqjzzxsjk0.correction_probation WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT DATE_FORMAT(sfssqsj, '%Y-%m-%d') AS time, CONCAT('提请撤销假释:', IFNULL(tqly,'')) AS content FROM sqjzzxsjk0.correction_parole WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT DATE_FORMAT(sfssqsj, '%Y-%m-%d') AS time, CONCAT('提请收监执行:', IFNULL(tqly,'')) AS content FROM sqjzzxsjk0.correction_prison WHERE pid = #{pid} AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
    </select>

    <!-- 行为动态 -->
    <select id="findListXwdt" parameterType="String" resultType="com.concise.gen.correctionrisklevel.entity.CorrectionRiskLevel">
        SELECT DATE_FORMAT(time, '%Y-%m-%d') AS time, '工作地变动' AS content FROM ccgf0.correction_analyse_workplace WHERE correction_object_id = #{correctionObjectId} AND time = #{estimateMonth}
        UNION ALL
        SELECT DATE_FORMAT(time, '%Y-%m-%d') AS time, '夜不归宿' AS content FROM ccgf0.correction_night_out WHERE correction_object_id = #{correctionObjectId} AND date_format(time, '%Y%m') = #{estimateMonth}
    </select>

    <select id="findLevel" parameterType="String" resultType="com.concise.gen.correctionrisklevel.entity.CorrectionRiskLevel">
        SELECT date_format(bdrq, '%Y-%m-%d') AS time, '普管降到严管' AS content FROM sqjzzxsjk0.correction_level  WHERE pid = #{pid} AND del_flag = 0 and gljb = '1'
    </select>

    <select id="findMoonCode" parameterType="String" resultType="com.concise.gen.correctionrisklevel.entity.CorrectionRiskLevel">
        SELECT DATE_FORMAT(update_time, '%Y-%m-%d') AS time, mood_code AS content FROM sqjzzxsjk0.correct_mood_code WHERE third_id = #{pid} and mood_code in('1','2','3') ORDER BY update_time asc
    </select>
    <select id="riskLevel" resultType="com.concise.gen.correctionobjectinformation.entity.ScreenModel">
        SELECT
            COUNT( correction_risk_level.risk_level ) AS amount,
            correction_risk_level.risk_level AS title
        FROM
            correction_risk_level
                INNER JOIN ( SELECT MAX( create_time ) AS create_time, sqjzry_id FROM correction_risk_level WHERE sqjzry_id IN
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
                GROUP BY sqjzry_id ) child ON correction_risk_level.create_time = child.create_time
                AND correction_risk_level.sqjzry_id = child.sqjzry_id
        GROUP BY
            correction_risk_level.risk_level
    </select>
    <select id="riskLevelDetail" resultType="com.concise.gen.correctionrisklevel.entity.CorrectionRiskLevel">
        SELECT
        correction_risk_level.*
        FROM
        correction_risk_level
        INNER JOIN ( SELECT MAX( create_time ) AS create_time, sqjzry_id FROM correction_risk_level WHERE sqjzry_id IN
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="name !='' and name != null">
            and sqjzry_name like  concat('%',#{name},'%')
        </if>
        GROUP BY sqjzry_id ) child ON correction_risk_level.create_time = child.create_time
        AND correction_risk_level.sqjzry_id = child.sqjzry_id
        WHERE correction_risk_level.risk_level=#{title}
    </select>
    <select id="riskAssessmentPage"
            resultType="com.concise.gen.correctionrisklevel.entity.CorrectionRiskLevelDto">
        SELECT
            correction_risk_level.id,
            correction_risk_level.sqjzry_id,
            correction_risk_level.sqjzry_name,
            correction_risk_level.jzjg,
            correction_risk_level.jzjg_name,
            correction_risk_level.risk_level,
            correction_risk_level.create_time AS TIME
        FROM
            correction_risk_level
            ${ew.customSqlSegment}
    </select>
</mapper>
