package com.concise.gen.portraitImage.correctionassesanswer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionobjectinformation.entity.ScreenModel;
import com.concise.gen.portraitImage.correctionassesanswer.entity.CorrectionAssesAnswer;
import com.concise.gen.portraitImage.correctionassesanswer.param.CorrectionAssesAnswerParam;
import com.concise.gen.portraitImage.correctionassespersondtl.entity.CorrectionAssesPersonDtlModel;

import java.util.List;
import java.util.Set;

/**
 * 评估管理--评估人员评估明细答案service接口
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:45
 */
public interface CorrectionAssesAnswerService extends IService<CorrectionAssesAnswer> {

    /**
     * 查询评估管理--评估人员评估明细答案
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:45
     */
    PageResult<CorrectionAssesAnswer> page(CorrectionAssesAnswerParam correctionAssesAnswerParam);

    /**
     * 评估管理--评估人员评估明细答案列表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:45
     */
    List<CorrectionAssesAnswer> list(CorrectionAssesAnswerParam correctionAssesAnswerParam);

    /**
     * 添加评估管理--评估人员评估明细答案
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:45
     */
    void add(CorrectionAssesAnswerParam correctionAssesAnswerParam);

    /**
     * 删除评估管理--评估人员评估明细答案
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:45
     */
    void delete(CorrectionAssesAnswerParam correctionAssesAnswerParam);

    /**
     * 编辑评估管理--评估人员评估明细答案
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:45
     */
    void edit(CorrectionAssesAnswerParam correctionAssesAnswerParam);

    /**
     * 查看评估管理--评估人员评估明细答案
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:45
     */
    CorrectionAssesAnswer detail(CorrectionAssesAnswerParam correctionAssesAnswerParam);

    /**
     * 知法画像
     * @param userIds
     * @return
     */
    List<ScreenModel> law(Set<String> userIds);

    /**
     * 工作状态
     * @param userIds
     * @return
     */
    List<ScreenModel> workingCondition(Set<String> userIds);

    /**
     * 工作类型
     * @param userIds
     * @return
     */
    List<ScreenModel> workingType(Set<String> userIds);

    /**
     * 身份职业
     * @param userIds
     * @return
     */
    List<ScreenModel> identityAndOccupation(Set<String> userIds);

    /**
     * 收入来源
     * @param userIds
     * @return
     */
    List<ScreenModel> sourceOfIncome(Set<String> userIds);

    /**
     * 家庭收入
     * @param userIds
     * @return
     */
    List<ScreenModel> householdIncome(Set<String> userIds);

    /**
     * 家庭负债
     * @param userIds
     * @return
     */
    List<ScreenModel> householdDebt(Set<String> userIds);

    /**
     * 家庭矛盾
     * @param userIds
     * @return
     */
    List<ScreenModel> familyConflicts(Set<String> userIds);

    /**
     * 查询收入列表
     * @param userIds
     * @return
     */
    List<CorrectionAssesPersonDtlModel> latestRevenueList(Set<String> userIds);

    /**
     * 查询负债列表
     * @param userIds
     * @return
     */
    List<CorrectionAssesPersonDtlModel> liabilityList(Set<String> userIds);
}
