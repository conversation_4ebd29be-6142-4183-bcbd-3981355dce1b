package com.concise.gen.correctionstuff.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.PoiUtil;
import com.concise.gen.correctionstuff.entity.CorrectionStuff;
import com.concise.gen.correctionstuff.enums.CorrectionStuffExceptionEnum;
import com.concise.gen.correctionstuff.mapper.CorrectionStuffMapper;
import com.concise.gen.correctionstuff.param.CorrectionStuffParam;
import com.concise.gen.correctionstuff.service.CorrectionStuffService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 矫正小组service接口实现类
 *
 * <AUTHOR>
 * @date 2021-09-16 16:39:00
 */
@DS("slave")
@Service
public class CorrectionStuffServiceImpl extends ServiceImpl<CorrectionStuffMapper, CorrectionStuff> implements CorrectionStuffService {

    @Override
    public PageResult<CorrectionStuff> page(CorrectionStuffParam correctionStuffParam) {
        QueryWrapper<CorrectionStuff> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionStuffParam)) {
            // 根据社区矫正人员标识 查询
            if (ObjectUtil.isNotEmpty(correctionStuffParam.getPid())) {
                queryWrapper.lambda().eq
                        (CorrectionStuff::getPid, correctionStuffParam.getPid());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionStuff> list(CorrectionStuffParam correctionStuffParam) {
        QueryWrapper<CorrectionStuff> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionStuffParam)) {
            // 根据社区矫正人员标识 查询
            if (ObjectUtil.isNotEmpty(correctionStuffParam.getPid())) {
                queryWrapper.lambda().eq
                        (CorrectionStuff::getPid, correctionStuffParam.getPid());
            }
        }
        return this.list(queryWrapper);
    }
}
