package com.concise.gen.correctionestimateportrayaldetail.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 画像明细参数类
 *
 * <AUTHOR>
 * @date 2022-11-30 19:58:01
*/
@Data
public class CorrectionEstimatePortrayalDetailParam extends BaseParam {

    /**
     * 主键id
     */
   // @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 画像类型
     */
    @NotNull(message = "画像类型不能为空，请检查type参数", groups = {add.class, edit.class})
    private Integer type;

    /**
     * 画像id
     */
    @NotBlank(message = "画像id不能为空，请检查portrayalId参数", groups = {add.class, edit.class})
    private String portrayalId;

    /**
     * 画像描述
     */
    @NotBlank(message = "画像描述不能为空，请检查hxms参数", groups = {add.class, edit.class})
    private String hxms;

    /**
     * 轨迹趋势
     */
    @NotBlank(message = "轨迹趋势不能为空，请检查gjqs参数", groups = {add.class, edit.class})
    private String gjqs;

    /**
     * 轨迹风险
     */
    @NotBlank(message = "轨迹风险不能为空，请检查xlhxGjfx参数", groups = {add.class, edit.class})
    private String xlhxGjfx;

    /**
     * 改善建议
     */
    @NotBlank(message = "改善建议不能为空，请检查jy参数", groups = {add.class, edit.class})
    private String jy;

    /**
     * 扣分明细
     */
    @NotBlank(message = "扣分明细不能为空，请检查kfmx参数", groups = {add.class, edit.class})
    private String kfmx;

    /**
     * 权重总分
     */
    @NotBlank(message = "权重总分不能为空，请检查zf参数", groups = {add.class, edit.class})
    private String zf;

    /**
     * 扣分
     */
    @NotBlank(message = "扣分不能为空，请检查kf参数", groups = {add.class, edit.class})
    private String kf;

    /**
     * 得分
     */
    @NotBlank(message = "得分不能为空，请检查df参数", groups = {add.class, edit.class})
    private String df;

}
