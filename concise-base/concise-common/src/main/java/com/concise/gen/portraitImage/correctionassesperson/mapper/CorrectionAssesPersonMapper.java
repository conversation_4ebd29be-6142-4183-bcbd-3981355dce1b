package com.concise.gen.portraitImage.correctionassesperson.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.concise.gen.correctionabilitymanage.entity.CorrectionAbilityManage;
import com.concise.gen.portraitImage.correctionassesperson.entity.CorrectionAssesPerson;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 评估管理--评估人员信息
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:42
 */
public interface CorrectionAssesPersonMapper extends BaseMapper<CorrectionAssesPerson> {

    /**
     * 根据评估管理获取该评估总次数
     * @param baseId
     * @return
     */
    int getNum(String baseId);

    /**
     * 根据评估id删除评估人员数据
     * @param baseId
     */
    void delByBaseId(String baseId);

    /**
     *  列表查询
     * @param page 分页对象
     * @return
     */
    Page<CorrectionAssesPerson> page(@Param("page") Page page, @Param("status") String status, @Param("baseId") String baseId,
                                     @Param("list") Set<String> list, @Param("jzjb") String jzjb, @Param("sqjzryName") String sqjzryName);
}
