package com.concise.gen.correctioncase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctioncase.entity.CorrectionCase;
import com.concise.gen.correctioncase.enums.CorrectionCaseExceptionEnum;
import com.concise.gen.correctioncase.mapper.CorrectionCaseMapper;
import com.concise.gen.correctioncase.param.CorrectionCaseParam;
import com.concise.gen.correctioncase.service.CorrectionCaseService;
import com.concise.gen.correctioncaselable.service.CorrectionCaseLableService;
import com.concise.gen.correctionlabelcorrpsn.mapper.CorrectionLabelCorrpsnMapper;
import com.concise.gen.correctionlabelmanage.entity.CorrectionLabelManage;
import com.concise.gen.correctionlabelmanage.mapper.CorrectionLabelManageMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * 案例库service接口实现类
 *
 * <AUTHOR>
 * @date 2022-03-14 17:18:03
 */
@Service
public class CorrectionCaseServiceImpl extends ServiceImpl<CorrectionCaseMapper, CorrectionCase> implements CorrectionCaseService {

    @Resource
    private CorrectionCaseLableService correctionCaseLableService;

    @Resource
    private CorrectionLabelManageMapper correctionLabelManageMapper;

    @Resource
    private CorrectionLabelCorrpsnMapper correctionLabelCorrpsnMapper;

    @Override
    public PageResult<CorrectionCase> page(CorrectionCaseParam correctionCaseParam) {
        QueryWrapper<CorrectionCase> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionCaseParam)) {

            // 根据标题 查询
            if (ObjectUtil.isNotEmpty(correctionCaseParam.getTitle())) {
                queryWrapper.lambda().like(CorrectionCase::getTitle, correctionCaseParam.getTitle());
            }
            // 根据案例类型,字典值caseType 查询
            if (ObjectUtil.isNotEmpty(correctionCaseParam.getCaseType())) {
                queryWrapper.lambda().eq(CorrectionCase::getCaseType, correctionCaseParam.getCaseType());
            }
            // 根据案例内容 查询
            if (ObjectUtil.isNotEmpty(correctionCaseParam.getCaseContent())) {
                queryWrapper.lambda().eq(CorrectionCase::getCaseContent, correctionCaseParam.getCaseContent());
            }
            // 根据是否删除（0：未删除，1删除） 查询
            if (ObjectUtil.isNotEmpty(correctionCaseParam.getDelFlag())) {
                queryWrapper.lambda().eq(CorrectionCase::getDelFlag, correctionCaseParam.getDelFlag());
            }
        }
        queryWrapper.orderByDesc("create_time");
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionCase> list(CorrectionCaseParam correctionCaseParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionCaseParam correctionCaseParam) {
        CorrectionCase correctionCase = new CorrectionCase();
        BeanUtil.copyProperties(correctionCaseParam, correctionCase);
        this.save(correctionCase);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionCaseParam correctionCaseParam) {
        this.removeById(correctionCaseParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionCaseParam correctionCaseParam) {
        CorrectionCase correctionCase = this.queryCorrectionCase(correctionCaseParam);
        BeanUtil.copyProperties(correctionCaseParam, correctionCase);
        this.updateById(correctionCase);
    }

    @Override
    public CorrectionCase detail(CorrectionCaseParam correctionCaseParam) {
        return this.queryCorrectionCase(correctionCaseParam);
    }

    /**
     * 获取案例库
     *
     * <AUTHOR>
     * @date 2022-03-14 17:18:03
     */
    private CorrectionCase queryCorrectionCase(CorrectionCaseParam correctionCaseParam) {
        CorrectionCase correctionCase = this.getById(correctionCaseParam.getId());
        if (ObjectUtil.isNull(correctionCase)) {
            throw new ServiceException(CorrectionCaseExceptionEnum.NOT_EXIST);
        }
        //查询能力关联的标签list
        List<CorrectionLabelManage> labelList = correctionLabelManageMapper.findByCaseId(correctionCaseParam.getId());
        correctionCase.setLableList(labelList);
        return correctionCase;
    }

    @Override
    public List<CorrectionCase> findBySqjzryId(String sqjzryId) {
        List<CorrectionCase> newList = new ArrayList<>();
        // 查询矫正对象的标签
        List<String> corrpsnLabelList = correctionLabelCorrpsnMapper.findLabelIds(sqjzryId);
        if (corrpsnLabelList.size() == 0) {
            //没有标签直接返回
            return newList;
        }
        String corrpsnLabels = String.join(",", corrpsnLabelList);
        // 根据矫正对象的标签， 查找所有匹配的案例， 再进行过滤
        List<CorrectionCase> caseList = this.baseMapper.findByLabel(new HashSet<>(corrpsnLabelList));
        for (CorrectionCase ca : caseList) {
            // 匹配人员标签，全匹配上则返回前台展示
            // 案例是否适用标志位， true为适用
            boolean tag = true;
            for (String labelId : ca.getLabelIds().split(",")) {
                if (corrpsnLabels.indexOf(labelId) < 0) {
                    tag = false;
                    break;
                };
            }
            if (tag) {
                newList.add(ca);
            }
        }
        return newList;
    }
}
