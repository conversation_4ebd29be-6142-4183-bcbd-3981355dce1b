package com.concise.gen.correctionestimateportrayalparticulars.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 心理画像明细（新）
 *
 * <AUTHOR>
 * @date 2023-01-14 10:38:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_estimate_portrayal_particulars")
public class CorrectionEstimatePortrayalParticulars extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 画像id
     */
    private String portrayalId;

    /**
     * 心理画像描述
     */
    private String xlhxms;

    /**
     * 知法画像描述
     */
    private String zfhxms;

    /**
     * 就业画像描述
     */
    private String jyhxms;

    /**
     * 家庭画像描述
     */
    private String jthxms;

    /**
     * 信用画像描述
     */
    private String xyhxms;

    /**
     * 个人基本画像描述
     */
    private String jbhxms;

    /**
     * 心理画像建议
     */
    private String xlhxjy;

    /**
     * 知法画像建议
     */
    private String zfhxjy;

    /**
     * 就业画像建议
     */
    private String jyhxjy;

    /**
     * 家庭画像建议
     */
    private String jthxjy;

    /**
     * 信用画像建议
     */
    private String xyhxjy;

    /**
     * 个人基本画像建议
     */
    private String jbhxjy;

    /**
     * 心理画像扣分明细
     */
    private String xlhxkfmx;

    /**
     * 知法画像扣分明细
     */
    private String zfhxkfmx;

    /**
     * 就业画像扣分明细
     */
    private String jyhxkfmx;

    /**
     * 家庭画像扣分明细
     */
    private String jthxkfmx;

    /**
     * 信用画像扣分明细
     */
    private String xyhxkfmx;

    /**
     * 个人基本画像扣分明细
     */
    private String jbhxkfmx;

    /**
     * 心理画像轨迹风险分析
     */
    private String xlhxgjfx;

    /**
     * 知法画像轨迹风险分析
     */
    private String zfhxgjfx;

    /**
     * 就业画像轨迹风险分析
     */
    private String jyhxgjfx;

    /**
     * 家庭画像轨迹风险分析
     */
    private String jthxgjfx;

    /**
     * 信用画像轨迹风险分析
     */
    private String xyhxgjfx;

    /**
     * 个人基本画像轨迹风险分析
     */
    private String jbhxgjfx;

    /**
     * 心理画像风险等级
     */
    private String xlhxLevel;

    /**
     * 知法画像风险等级
     */
    private String zfhxLevel;

    /**
     * 就业画像风险等级
     */
    private String jyhxLevel;

    /**
     * 家庭画像风险等级
     */
    private String jthxLevel;

    /**
     * 信用画像风险等级
     */
    private String xyhxLevel;

    /**
     * 个人基本画像风险等级
     */
    private String jbhxLevel;

    /**
     * 心理画像权重总分
     */
    private BigDecimal xlhxZf;

    /**
     * 知法画像权重总分
     */
    private BigDecimal zfhxZf;

    /**
     * 就业画像权重总分
     */
    private BigDecimal jyhxZf;

    /**
     * 家庭画像权重总分
     */
    private BigDecimal jthxZf;

    /**
     * 信用画像权重总分
     */
    private BigDecimal xyhxZf;

    /**
     * 个人基本画像权重总分
     */
    private BigDecimal jbhxZf;

    /**
     * 心理画像扣分
     */
    private BigDecimal xlhxKf;

    /**
     * 知法画像扣分
     */
    private BigDecimal zfhxKf;

    /**
     * 就业画像扣分
     */
    private BigDecimal jyhxKf;

    /**
     * 家庭画像扣分
     */
    private BigDecimal jthxKf;

    /**
     * 信用画像扣分
     */
    private BigDecimal xyhxKf;

    /**
     * 个人基本画像扣分
     */
    private BigDecimal jbhxKf;

    /**
     * 心理画像轨迹风险趋势
     */
    private String gjqsXaxis;

    /**
     * 心理画像轨迹风险趋势
     */
    private String xlhxGjqs;

    /**
     * 知法画像轨迹风险趋势
     */
    private String zfhxGjqs;

    /**
     * 就业画像轨迹风险趋势
     */
    private String jyhxGjqs;

    /**
     * 家庭画像轨迹趋势
     */
    private String jthxGjqs;

    /**
     * 信用画像轨迹风险趋势
     */
    private String xyhxGjqs;

    /**
     * 个人基本画像轨迹风险趋势
     */
    private String jbhxGjqs;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

}
