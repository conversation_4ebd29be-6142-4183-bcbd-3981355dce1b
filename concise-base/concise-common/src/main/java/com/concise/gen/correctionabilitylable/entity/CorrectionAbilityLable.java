package com.concise.gen.correctionabilitylable.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 能力管理、标签关联表
 *
 * <AUTHOR>
 * @date 2022-03-03 11:00:37
 */
@Data
@TableName("correction_ability_lable")
public class CorrectionAbilityLable {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 能力id
     */
    private Long abilityId;

    /**
     * 标签id
     */
    private String lableId;

}
