package com.concise.gen.correctionsocialclassroom.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.file.param.SysFileInfoParam;
import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
* 社矫大讲堂参数类
 *
 * <AUTHOR>
 * @date 2022-03-10 17:45:12
*/
@Data
public class CorrectionSocialClassroomParam extends BaseParam {

    /**
     *
     */
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 来源
     */
    private String fromSource;

    /**
     * 视频分类（大讲堂字典值：DJTSPFL 微电影字典值：WDYSPFL）
     */
    private String type;

    /**
     * 所属模块1： 大讲堂  2： 微电影
     */
    private Integer module;

    /**
     * 文件编号
     */
    private String fileId;

    /**
     * 视频简介
     */
    private String remark;

    /**
     * dept_id 发布部门
     */
    private String deptId;

    /**
     * dept_name 发布部门名称
     */
    private String deptName;

    /**
     * 封面ID
     */
    private String imgId;

    /**
     * 视频时长
     */
    private String videoTime;

    /**
     * 链接地址
     */
    private String linkAddress;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 视频集合
     */
    private List<SysFileInfoParam> fileList;

    /**
     * 封面集合
     */
    private List<SysFileInfoParam> imgList;
}
