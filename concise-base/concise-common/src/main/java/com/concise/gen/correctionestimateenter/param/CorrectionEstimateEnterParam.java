package com.concise.gen.correctionestimateenter.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

/**
* 入矫评估参数类
 *
 * <AUTHOR>
 * @date 2022-05-13 14:52:58
*/
@Data
public class CorrectionEstimateEnterParam extends BaseParam {

    /**
     * 主键id
     */
    private String id;

    /**
     * 矫正对象id
     */
    @NotBlank(message = "矫正对象id不能为空，请检查sqjzryId参数", groups = {add.class, edit.class})
    private String sqjzryId;

    /**
     * 姓名
     */
    private String sqjzryName;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 矫正机构ID
     */
    private String jzjg;

    /**
     * 矫正机构名称
     */
    private String jzjgName;

    /**
     * 总分
     */
    private BigDecimal scoreTotal;

    /**
     * 评估分
     */
    private BigDecimal scoreEstimate;

    /**
     * 判决书犯罪年龄
     */
    private String crimeAge;

    /**
     * 受教育程度
     */
    private String educationLevel;

    /**
     * 婚姻家庭状况
     */
    private String marriageFamily;

    /**
     * 就业态度和状况
     */
    private String workState;

    /**
     * 生活来源
     */
    private String lifeSource;

    /**
     * 房户一体
     */
    private String houseNumberSame;

    /**
     * 自控能力
     */
    private String controlAbility;

    /**
     * 认罪服法态度
     */
    private String guiltyManner;

    /**
     * 对现实社会的心态
     */
    private String mentality;

    /**
     * 法律知识或法制观念
     */
    private String lawKnowledge;

    /**
     * 心理健康状况
     */
    private String psychicHealth;

    /**
     * 有精神病史或精神病遗传史
     */
    private String mentalDisease;

    /**
     * 交友情况
     */
    private String makeFriends;

    /**
     * 个人成长经历
     */
    private String personGrowUp;

    /**
     * 家庭成员犯罪记录
     */
    private String familyCrimeRecord;

    /**
     * 家属配合矫正工作
     */
    private String familyConcertCorrect;

    /**
     * 过去受刑事处罚记录
     */
    private String criminalPenaltyRecord;

    /**
     * 过去受行政处罚记录
     */
    private String administrativePenaltyRecord;

    /**
     * 主观恶性程度
     */
    private String gradeMalignancy;

    /**
     * 累犯惯犯
     */
    private String oldLag;

    /**
     * 违法犯罪案由
     */
    private String crimeReason;

    /**
     * 社区矫正类别
     */
    private String correctType;

    /**
     * 是否数罪并罚
     */
    private String moreCrime;

    /**
     * 是否共同犯罪
     */
    private String togetherCrime;

    /**
     * 是否五独
     */
    private String fivePoisons;

    /**
     * 是否五涉
     */
    private String fiveInvolvement;

    /**
     * 是否四史
     */
    private String fourFamous;

    /**
     * 附加刑及罚金（财产型是否执行毕）
     */
    private String accessoryPunishment;

    /**
     * 状态（0：暂存，1：提交）
     */
    private Integer status;

    /**
     * 评估日期
     */
    private Date estimateTime;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 评估日期_begin
     */
    private String estimateTime_begin;

    /**
     * 评估日期_end
     */
    private String estimateTime_end;
}
