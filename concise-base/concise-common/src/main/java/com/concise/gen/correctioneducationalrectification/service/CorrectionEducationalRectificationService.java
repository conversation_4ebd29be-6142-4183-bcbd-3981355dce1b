package com.concise.gen.correctioneducationalrectification.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctioneducationalrectification.entity.CorrectionEducationalRectification;
import com.concise.gen.correctioneducationalrectification.param.CorrectionEducationalRectificationParam;
import java.util.List;

/**
 * 教育整顿service接口
 *
 * <AUTHOR>
 * @date 2022-03-10 17:37:39
 */
public interface CorrectionEducationalRectificationService extends IService<CorrectionEducationalRectification> {

    /**
     * 查询教育整顿
     *
     * <AUTHOR>
     * @date 2022-03-10 17:37:39
     */
    PageResult<CorrectionEducationalRectification> page(CorrectionEducationalRectificationParam correctionEducationalRectificationParam);

    /**
     * 教育整顿列表
     *
     * <AUTHOR>
     * @date 2022-03-10 17:37:39
     */
    List<CorrectionEducationalRectification> list(CorrectionEducationalRectificationParam correctionEducationalRectificationParam);

    /**
     * 添加教育整顿
     *
     * <AUTHOR>
     * @date 2022-03-10 17:37:39
     */
    void add(CorrectionEducationalRectificationParam correctionEducationalRectificationParam);

    /**
     * 删除教育整顿
     *
     * <AUTHOR>
     * @date 2022-03-10 17:37:39
     */
    void delete(CorrectionEducationalRectificationParam correctionEducationalRectificationParam);

    /**
     * 编辑教育整顿
     *
     * <AUTHOR>
     * @date 2022-03-10 17:37:39
     */
    void edit(CorrectionEducationalRectificationParam correctionEducationalRectificationParam);

    /**
     * 查看教育整顿
     *
     * <AUTHOR>
     * @date 2022-03-10 17:37:39
     */
     CorrectionEducationalRectification detail(CorrectionEducationalRectificationParam correctionEducationalRectificationParam);
}
