package com.concise.gen.correctionresume.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.Date;

/**
 * 个人简历
 *
 * <AUTHOR>
 * @date 2022-02-22 16:07:54
 */
@Data
@TableName("correction_resume")
public class CorrectionResume {

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 社区矫正人员标识
     */
    private String pid;

    /**
     * 起时
     */
    @Excel(name = "起时", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    @ApiModelProperty(value = "起时")
    private Date qs;

    /**
     * 止日
     */
    @Excel(name = "止日", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    @ApiModelProperty(value = "止日")
    private Date zr;

    /**
     * 所在单位
     */
    private String szdw;

    /**
     * 职务
     */
    private String zw;

    /**
     * 职务Name
     */
    private String zwName;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

}
