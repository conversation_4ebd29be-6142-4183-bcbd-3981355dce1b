package com.concise.gen.examquestionuser.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.examquestionuser.entity.ExamQuestionUser;
import com.concise.gen.examquestionuser.param.ExamQuestionUserParam;
import java.util.List;

/**
 * 用户答题表service接口
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:27
 */
public interface ExamQuestionUserService extends IService<ExamQuestionUser> {

    /**
     * 查询用户答题表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:27
     */
    PageResult<ExamQuestionUser> page(ExamQuestionUserParam examQuestionUserParam);

    /**
     * 用户答题表列表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:27
     */
    List<ExamQuestionUser> list(ExamQuestionUserParam examQuestionUserParam);

    /**
     * 添加用户答题表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:27
     */
    void add(ExamQuestionUserParam examQuestionUserParam);

    /**
     * 删除用户答题表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:27
     */
    void delete(ExamQuestionUserParam examQuestionUserParam);

    /**
     * 编辑用户答题表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:27
     */
    void edit(ExamQuestionUserParam examQuestionUserParam);

    /**
     * 查看用户答题表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:27
     */
     ExamQuestionUser detail(ExamQuestionUserParam examQuestionUserParam);
}
