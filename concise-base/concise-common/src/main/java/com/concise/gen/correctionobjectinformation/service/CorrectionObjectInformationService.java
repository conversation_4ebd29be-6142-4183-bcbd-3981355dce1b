package com.concise.gen.correctionobjectinformation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.correctionobjectinformation.entity.ScreenModel;
import com.concise.gen.correctionobjectinformation.param.CorrectionObjectInformationParam;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 矫正对象信息表-service接口
 *
 * <AUTHOR>
 * @date 2021-12-07 14:19:57
 */
public interface CorrectionObjectInformationService extends IService<CorrectionObjectInformation> {

    /**
     * 查询矫正对象信息表-
     *
     * <AUTHOR>
     * @date 2021-12-07 14:19:57
     */
    PageResult<CorrectionObjectInformation> page(CorrectionObjectInformationParam correctionObjectInformationParam, Set<String> org);

    /**
     * 矫正对象信息表-列表
     *
     * <AUTHOR>
     * @date 2021-12-07 14:19:57
     */
    List<CorrectionObjectInformation> list(CorrectionObjectInformationParam correctionObjectInformationParam, Set<String> org);

    /**
     * 添加矫正对象信息表-
     *
     * <AUTHOR>
     * @date 2021-12-07 14:19:57
     */
    void add(CorrectionObjectInformationParam correctionObjectInformationParam);

    /**
     * 删除矫正对象信息表-
     *
     * <AUTHOR>
     * @date 2021-12-07 14:19:57
     */
    void delete(CorrectionObjectInformationParam correctionObjectInformationParam);

    /**
     * 编辑矫正对象信息表-
     *
     * <AUTHOR>
     * @date 2021-12-07 14:19:57
     */
    void edit(CorrectionObjectInformationParam correctionObjectInformationParam);

    /**
     * 查看矫正对象信息表-
     *
     * <AUTHOR>
     * @date 2021-12-07 14:19:57
     */
    CorrectionObjectInformation detail(CorrectionObjectInformationParam correctionObjectInformationParam);

    /**
     * 手动解矫
     * @param id
     */
    void release(String id);

    /**
     * 查询前80分钟有更新的人员，绑定标签并保存至矫正方案表
     */
    void initToCorrectPlan(Set<String> org);

    /**
     * 计算矫正对象矫正阶段 （1：入矫初期 2：矫正中期 3：矫正末期）
     * @param sqjzryId
     * @return
     */
    int getCorrectPhase(String sqjzryId);

    /**
     * 每天凌晨查询矫正结束时间还剩30天的矫正对象， 将数据初始化到解矫总结模块
     * @return
     */
    void createSummaryData(Set<String> org);

    /**
     * 根据搜索文本查询指定矫正对象信息表-
     *
     * <AUTHOR>
     * @date 2022-10-17 14:19:50
     */
    PageResult<CorrectionObjectInformation> page(List<String> label, Set<String> org);

    /**
     * 每天定时更新矫正对象标签
     */
    void initLabelPsn();

    /**
     * 单个矫正对象更新标签
     * @param userIds
     * @return
     */
    void initLabelPsnByPerson(CorrectionObjectInformation correctionObjectInformation);


    List<ScreenModel> moodCode(Set<String> userIds);

    /**
     * 根据矫正对象id获取解矫日期
     * @param id
     * @return
     */
    Date getJiejiaoriqiById(String id);
}
