package com.concise.gen.portraitImage.correctionassesbase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimateenter.entity.CorrectionEstimateEnter;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.gen.portraitImage.correctionassesbase.entity.CorrectionAssesBase;
import com.concise.gen.portraitImage.correctionassesbase.enums.CorrectionAssesBaseExceptionEnum;
import com.concise.gen.portraitImage.correctionassesbase.mapper.CorrectionAssesBaseMapper;
import com.concise.gen.portraitImage.correctionassesbase.param.CorrectionAssesBaseParam;
import com.concise.gen.portraitImage.correctionassesbase.service.CorrectionAssesBaseService;
import com.concise.gen.portraitImage.correctionassesmembers.entity.CorrectionAssesMembers;
import com.concise.gen.portraitImage.correctionassesmembers.service.CorrectionAssesMembersService;
import com.concise.gen.portraitImage.correctionassesperson.entity.CorrectionAssesPerson;
import com.concise.gen.portraitImage.correctionassesperson.service.CorrectionAssesPersonService;
import com.concise.gen.portraitImage.correctionassespersondtl.service.CorrectionAssesPersonDtlService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 评估管理--基本信息service接口实现类
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:40
 */
@Service
public class CorrectionAssesBaseServiceImpl extends ServiceImpl<CorrectionAssesBaseMapper, CorrectionAssesBase> implements CorrectionAssesBaseService {

    @Resource
    private CorrectionAssesMembersService correctionAssesMembersService;

    @Override
    public PageResult<CorrectionAssesBase> page(CorrectionAssesBaseParam correctionAssesBaseParam) {
        QueryWrapper<CorrectionAssesBase> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionAssesBaseParam)) {

            // 根据任务名称 查询
            if (ObjectUtil.isNotEmpty(correctionAssesBaseParam.getTitle())) {
                queryWrapper.lambda().like(CorrectionAssesBase::getTitle, correctionAssesBaseParam.getTitle());
            }
            // 根据量表名称 查询
            if (ObjectUtil.isNotEmpty(correctionAssesBaseParam.getScaleBaseName())) {
                queryWrapper.lambda().like(CorrectionAssesBase::getScaleBaseName, correctionAssesBaseParam.getScaleBaseName());
            }
            // 根据测评开始时间，格式: yyyyMMdd 查询
            if (ObjectUtil.isNotEmpty(correctionAssesBaseParam.getStartTime_begin())) {
                queryWrapper.lambda().ge(CorrectionAssesBase::getStartTime, correctionAssesBaseParam.getStartTime_begin().replaceAll("-", ""));
            }
            if (ObjectUtil.isNotEmpty(correctionAssesBaseParam.getStartTime_end())) {
                queryWrapper.lambda().le(CorrectionAssesBase::getStartTime, correctionAssesBaseParam.getStartTime_end().replaceAll("-", ""));
            }
            // 根据状态：0：未开始 1：进行中 2：已完成 3：停止 查询
            if (ObjectUtil.isNotEmpty(correctionAssesBaseParam.getStatus())) {
                queryWrapper.lambda().eq(CorrectionAssesBase::getStatus, correctionAssesBaseParam.getStatus());
            }
        }
        queryWrapper.lambda().eq(CorrectionAssesBase::getDelFlag, 0);
        queryWrapper.lambda().orderByAsc(CorrectionAssesBase::getStatus).orderByDesc(CorrectionAssesBase::getStartTime);
        queryWrapper.lambda().eq(CorrectionAssesBase::getDelFlag, 0);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionAssesBase> list(CorrectionAssesBaseParam correctionAssesBaseParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionAssesBaseParam correctionAssesBaseParam) {
        CorrectionAssesBase correctionAssesBase = new CorrectionAssesBase();
        BeanUtil.copyProperties(correctionAssesBaseParam, correctionAssesBase);
        this.save(correctionAssesBase);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionAssesBaseParam correctionAssesBaseParam) {
        this.removeById(correctionAssesBaseParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionAssesBaseParam correctionAssesBaseParam) {
        CorrectionAssesBase correctionAssesBase = this.queryCorrectionAssesBase(correctionAssesBaseParam);
        BeanUtil.copyProperties(correctionAssesBaseParam, correctionAssesBase);
        this.updateById(correctionAssesBase);
    }

    @Override
    public CorrectionAssesBase detail(CorrectionAssesBaseParam correctionAssesBaseParam) {
        return this.queryCorrectionAssesBase(correctionAssesBaseParam);
    }

    /**
     * 获取评估管理--基本信息
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:40
     */
    private CorrectionAssesBase queryCorrectionAssesBase(CorrectionAssesBaseParam correctionAssesBaseParam) {
        CorrectionAssesBase correctionAssesBase = this.getById(correctionAssesBaseParam.getId());
        if (ObjectUtil.isNull(correctionAssesBase)) {
            throw new ServiceException(CorrectionAssesBaseExceptionEnum.NOT_EXIST);
        }
        //拼机构、人员
        QueryWrapper<CorrectionAssesMembers> members = new QueryWrapper<>();
        members.lambda().eq(CorrectionAssesMembers::getBaseId, correctionAssesBaseParam.getId());
        members.lambda().eq(CorrectionAssesMembers::getType, 0);
        List<CorrectionAssesMembers> list = correctionAssesMembersService.list(members);
        List<String> deptids = new ArrayList<>();
        for (CorrectionAssesMembers cm : list) {
            deptids.add(cm.getMemberId());
        }
        correctionAssesBase.setDeptIds(deptids);
        members = new QueryWrapper<>();
        members.lambda().eq(CorrectionAssesMembers::getBaseId, correctionAssesBaseParam.getId());
        members.lambda().eq(CorrectionAssesMembers::getType, 1);
        correctionAssesBase.setPersonList(correctionAssesMembersService.list(members));
        return correctionAssesBase;
    }

    @Override
    public int getAssesNum(String scaleBaseId) {
        QueryWrapper<CorrectionAssesBase> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CorrectionAssesBase::getDelFlag, 0);
        queryWrapper.lambda().in(CorrectionAssesBase::getStatus, new int[]{0, 1});
        queryWrapper.lambda().eq(CorrectionAssesBase::getScaleBaseId, scaleBaseId);
        return this.count(queryWrapper);
    }
}
