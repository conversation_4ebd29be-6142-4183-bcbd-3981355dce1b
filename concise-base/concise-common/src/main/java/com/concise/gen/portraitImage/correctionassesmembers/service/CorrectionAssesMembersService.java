package com.concise.gen.portraitImage.correctionassesmembers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.portraitImage.correctionassesmembers.entity.CorrectionAssesMembers;
import com.concise.gen.portraitImage.correctionassesmembers.param.CorrectionAssesMembersParam;

import java.util.List;

/**
 * 评估管理--人员机构信息(只用于评估未开始前的查看)service接口
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:41
 */
public interface CorrectionAssesMembersService extends IService<CorrectionAssesMembers> {

    /**
     * 查询评估管理--人员机构信息(只用于评估未开始前的查看)
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:41
     */
    PageResult<CorrectionAssesMembers> page(CorrectionAssesMembersParam correctionAssesMembersParam);

    /**
     * 评估管理--人员机构信息(只用于评估未开始前的查看)列表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:41
     */
    List<CorrectionAssesMembers> list(CorrectionAssesMembersParam correctionAssesMembersParam);

    /**
     * 添加评估管理--人员机构信息(只用于评估未开始前的查看)
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:41
     */
    void add(CorrectionAssesMembersParam correctionAssesMembersParam);

    /**
     * 删除评估管理--人员机构信息(只用于评估未开始前的查看)
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:41
     */
    void delete(CorrectionAssesMembersParam correctionAssesMembersParam);

    /**
     * 编辑评估管理--人员机构信息(只用于评估未开始前的查看)
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:41
     */
    void edit(CorrectionAssesMembersParam correctionAssesMembersParam);

    /**
     * 查看评估管理--人员机构信息(只用于评估未开始前的查看)
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:41
     */
     CorrectionAssesMembers detail(CorrectionAssesMembersParam correctionAssesMembersParam);
}
