package com.concise.gen.portraitImage.correctionassesbase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.portraitImage.correctionassesbase.entity.CorrectionAssesBase;
import com.concise.gen.portraitImage.correctionassesbase.param.CorrectionAssesBaseParam;

import java.util.List;

/**
 * 评估管理--基本信息service接口
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:40
 */
public interface CorrectionAssesBaseService extends IService<CorrectionAssesBase> {

    /**
     * 查询评估管理--基本信息
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:40
     */
    PageResult<CorrectionAssesBase> page(CorrectionAssesBaseParam correctionAssesBaseParam);

    /**
     * 评估管理--基本信息列表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:40
     */
    List<CorrectionAssesBase> list(CorrectionAssesBaseParam correctionAssesBaseParam);

    /**
     * 添加评估管理--基本信息
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:40
     */
    void add(CorrectionAssesBaseParam correctionAssesBaseParam);

    /**
     * 删除评估管理--基本信息
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:40
     */
    void delete(CorrectionAssesBaseParam correctionAssesBaseParam);

    /**
     * 编辑评估管理--基本信息
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:40
     */
    void edit(CorrectionAssesBaseParam correctionAssesBaseParam);

    /**
     * 查看评估管理--基本信息
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:40
     */
     CorrectionAssesBase detail(CorrectionAssesBaseParam correctionAssesBaseParam);

    /**
     * 根据量表id查询有在使用中的评估数量
     * @param scaleBaseId
     * @return
     */
     int getAssesNum(String scaleBaseId);
}
