package com.concise.gen.correctionlabeldictionary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.correctionlabeldictionary.entity.CorrectionLabelDictionary;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 标签关联字典值表
 *
 * <AUTHOR>
 * @date 2022-03-08 22:24:16
 */
public interface CorrectionLabelDictionaryMapper extends BaseMapper<CorrectionLabelDictionary> {

    /**
     * 根据字典code 查询标签ids
     * @param dictionaryCode
     * @return
     */
    List<String> getLabelIds(@Param("dictionaryCode") Set<String> dictionaryCode);
}
