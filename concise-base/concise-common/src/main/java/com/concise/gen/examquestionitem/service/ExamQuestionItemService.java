package com.concise.gen.examquestionitem.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.examquestionitem.entity.ExamQuestionItem;
import com.concise.gen.examquestionitem.param.ExamQuestionItemParam;
import java.util.List;

/**
 * 问题选项service接口
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:11
 */
public interface ExamQuestionItemService extends IService<ExamQuestionItem> {

    /**
     * 查询问题选项
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:11
     */
    PageResult<ExamQuestionItem> page(ExamQuestionItemParam examQuestionItemParam);

    /**
     * 问题选项列表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:11
     */
    List<ExamQuestionItem> list(ExamQuestionItemParam examQuestionItemParam);

    /**
     * 添加问题选项
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:11
     */
    void add(ExamQuestionItemParam examQuestionItemParam);

    /**
     * 删除问题选项
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:11
     */
    void delete(ExamQuestionItemParam examQuestionItemParam);

    /**
     * 编辑问题选项
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:11
     */
    void edit(ExamQuestionItemParam examQuestionItemParam);

    /**
     * 查看问题选项
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:11
     */
     ExamQuestionItem detail(ExamQuestionItemParam examQuestionItemParam);
}
