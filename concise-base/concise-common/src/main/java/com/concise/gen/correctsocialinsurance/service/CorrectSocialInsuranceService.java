package com.concise.gen.correctsocialinsurance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.correctsocialinsurance.entity.CorrectSocialInsurance;
import com.concise.gen.correctsocialinsurance.param.CorrectSocialInsuranceParam;
import java.util.List;
import java.util.Set;

/**
 * 历史社保主表service接口
 *
 * <AUTHOR>
 * @date 2022-06-07 15:08:49
 */
public interface CorrectSocialInsuranceService extends IService<CorrectSocialInsurance> {

    /**
     * 查询历史社保主表
     *
     * <AUTHOR>
     * @date 2022-06-07 15:08:49
     */
    PageResult<CorrectSocialInsurance> page(CorrectSocialInsuranceParam correctSocialInsuranceParam, List<String> sqjzryIdList);

    /**
     * 历史社保主表列表
     *
     * <AUTHOR>
     * @date 2022-06-07 15:08:49
     */
    List<CorrectSocialInsurance> list(CorrectSocialInsuranceParam correctSocialInsuranceParam);

    /**
     * 添加历史社保主表
     *
     * <AUTHOR>
     * @date 2022-06-07 15:08:49
     */
    void add(CorrectSocialInsuranceParam correctSocialInsuranceParam);

    /**
     * 删除历史社保主表
     *
     * <AUTHOR>
     * @date 2022-06-07 15:08:49
     */
    void delete(CorrectSocialInsuranceParam correctSocialInsuranceParam);

    /**
     * 编辑历史社保主表
     *
     * <AUTHOR>
     * @date 2022-06-07 15:08:49
     */
    void edit(CorrectSocialInsuranceParam correctSocialInsuranceParam);

    /**
     * 查看历史社保主表
     *
     * <AUTHOR>
     * @date 2022-06-07 15:08:49
     */
     CorrectSocialInsurance detail(CorrectSocialInsuranceParam correctSocialInsuranceParam);
}
