package com.concise.gen.correctsocialinsurancedtl.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 历史社保明细表
 *
 * <AUTHOR>
 * @date 2022-06-07 15:08:51
 */
@Data
@TableName("sqjzzxsjk0.correct_social_insurance_dtl")
public class CorrectSocialInsuranceDtl {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 历史社保主表id(该字段咱未使用)
     */
    private String socialInsuranceId;

    /**
     * 矫正对象id
     */
    private String sqjzryId;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 单位名称
     */
    private String dwmc;

    /**
     * 统一社会信用代码
     */
    private String tydm;

    /**
     * 险种
     */
    private String xz;

    /**
     * 所属月
     */
    private String month;

    /**
     * 缴费类型
     */
    private String jflx;

    /**
     * 统筹区
     */
    private String tcq;

    /**
     * 统筹区名称
     */
    private String tcqName;

    /**
     * 行政区划
     */
    private String xzqh;

    /**
     * 行政区划名称
     */
    private String xzqhName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Excel(name = "创建时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    @Excel(name = "更新时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date updateTime;
}
