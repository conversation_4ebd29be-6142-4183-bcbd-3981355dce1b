package com.concise.gen.correctionlabelthirdpart.param;

import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.correctionlabelthirdpart.entity.CorrectionLabelThirdPart;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
* 标签管理-第三方对接参数类
 *
 * <AUTHOR>
 * @date 2023-04-23 14:35:27
*/
@Data
public class CorrectionLabelThirdPartParam extends BaseParam {

    /**
     * 主键
     */
//    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 标签id
     */
//    @NotBlank(message = "标签id不能为空，请检查labelId参数", groups = {add.class, edit.class})
    private String labelId;

    /**
     * 标签名称
     */
//    @NotBlank(message = "标签名称不能为空，请检查name参数", groups = {add.class, edit.class})
    private String name;

    /**
     * 性质
     */
//    @NotBlank(message = "性质不能为空，请检查naturl参数", groups = {add.class, edit.class})
    private String naturl;

    /**
     * 影响时间
     */
//    @NotBlank(message = "影响时间不能为空，请检查affectDate参数", groups = {add.class, edit.class})
    private String affectDate;

    /**
     * 分值
     */
//    @NotBlank(message = "分值不能为空，请检查score参数", groups = {add.class, edit.class})
    private String score;

    /**
     * 是否系统内置 0是，1否
     */
//    @NotBlank(message = "是否系统内置 0是，1否不能为空，请检查builtFlag参数", groups = {add.class, edit.class})
    private String builtFlag;

    /**
     * 状态 0启用，1禁用
     */
//    @NotBlank(message = "状态 0启用，1禁用不能为空，请检查status参数", groups = {add.class, edit.class})
    private String status;

    /**
     * 是否分类 0是，1否
     */
    @NotBlank(message = "是否分类 0是，1否不能为空，请检查classify参数", groups = {add.class, edit.class})
    private String classify;

    /**
     * 备注
     */
//    @NotBlank(message = "备注不能为空，请检查remark参数", groups = {add.class, edit.class})
    private String remark;

    /**
     * 删除状态 0-正常 1-删除
     */
//    @NotNull(message = "删除状态 0-正常 1-删除不能为空，请检查delFlag参数", groups = {add.class, edit.class})
    private Integer delFlag;

    private Integer pageNo;

    private Integer pageSize;

    private String appKey;

    private String sign;

    private List<CorrectionLabelThirdPart> list;

}
