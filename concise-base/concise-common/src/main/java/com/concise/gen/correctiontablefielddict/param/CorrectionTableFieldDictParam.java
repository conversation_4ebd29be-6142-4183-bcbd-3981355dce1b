package com.concise.gen.correctiontablefielddict.param;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.concise.common.pojo.base.param.BaseParam;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* 表字段与字典关联表参数类
 *
 * <AUTHOR>
 * @date 2024-11-27 16:36:14
*/
@Data
public class CorrectionTableFieldDictParam extends BaseParam {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称")
    @NotBlank(message = "数据库名称不能为空，请检查databaseName参数", groups = {add.class, edit.class})
    private String databaseName;

    /**
     * 表名称
     */
    @ApiModelProperty(value = "表名称")
    @NotBlank(message = "表名称不能为空，请检查tableName参数", groups = {add.class, edit.class})
    private String tableName;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    @NotBlank(message = "字段名称不能为空，请检查fieldName参数", groups = {add.class, edit.class})
    private String fieldName;

    /**
     * 字典类型id
     */
    @ApiModelProperty(value = "字典类型id")
    @NotBlank(message = "字典类型id不能为空，请检查dictTypeId参数", groups = {add.class, edit.class})
    private String dictTypeId;

    /**
     * 字典类型
     */
    @ApiModelProperty(value = "字典类型")
    @NotBlank(message = "字典类型不能为空，请检查dictType参数", groups = {add.class, edit.class})
    private String dictType;

    /**
     * 字典类型名称
     */
    @ApiModelProperty(value = "字典类型名称")
    @NotBlank(message = "字典类型名称不能为空，请检查dictTypeName参数", groups = {add.class, edit.class})
    private String dictTypeName;

    /**
     * 表名称中文
     */
    @ApiModelProperty(value = "表名称中文")
    private String tableNameCn;

    /**
     * 字段名称中文
     */
    @ApiModelProperty(value = "字段名称中文")
    private String fieldNameCn;
}
