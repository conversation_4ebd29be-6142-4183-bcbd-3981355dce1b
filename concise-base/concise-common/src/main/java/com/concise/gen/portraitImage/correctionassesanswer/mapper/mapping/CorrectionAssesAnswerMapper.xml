<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.portraitImage.correctionassesanswer.mapper.CorrectionAssesAnswerMapper">

    <select id="getQuestionIds" parameterType="String" resultType="String">
        select question_id from correction_asses_answer where dtl_id = #{dtlId}
    </select>

    <select id="get" parameterType="String" resultType="String">
        select answer_ids from correction_asses_answer where dtl_id = #{dtlId} and question_id = #{questionId}
    </select>
    <select id="law" resultType="com.concise.gen.correctionobjectinformation.entity.ScreenModel">
        SELECT
            exam_question_item.content AS item,
            count( DISTINCT correction_asses_person_dtl.sqjzry_id ) AS amount
        FROM
            exam_question_item
                LEFT JOIN correction_asses_answer ON exam_question_item.id = correction_asses_answer.answer_ids
                LEFT JOIN correction_asses_person_dtl ON correction_asses_answer.dtl_id = correction_asses_person_dtl.id
        WHERE
            exam_question_item.question_id = '1622418542673616903'
        and correction_asses_person_dtl.sqjzry_id in
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
            exam_question_item.id
    </select>
    <select id="workingCondition" resultType="com.concise.gen.correctionobjectinformation.entity.ScreenModel">
        SELECT
        exam_question_item.content AS item,
        count( DISTINCT correction_asses_person_dtl.sqjzry_id ) AS amount
        FROM
        exam_question_item
        LEFT JOIN correction_asses_answer ON exam_question_item.id = correction_asses_answer.answer_ids
        LEFT JOIN correction_asses_person_dtl ON correction_asses_answer.dtl_id = correction_asses_person_dtl.id
        WHERE
        exam_question_item.question_id = '1622418542744920071'
        and correction_asses_person_dtl.sqjzry_id in
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        exam_question_item.id
    </select>
    <select id="workingType" resultType="com.concise.gen.correctionobjectinformation.entity.ScreenModel">
        SELECT
        exam_question_item.content AS item,
        count( DISTINCT correction_asses_person_dtl.sqjzry_id ) AS amount
        FROM
        exam_question_item
        LEFT JOIN correction_asses_answer ON exam_question_item.id = correction_asses_answer.answer_ids
        LEFT JOIN correction_asses_person_dtl ON correction_asses_answer.dtl_id = correction_asses_person_dtl.id
        WHERE
        exam_question_item.question_id = '1622418542782668804'
        and correction_asses_person_dtl.sqjzry_id in
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        exam_question_item.id
    </select>
    <select id="identityAndOccupation"
            resultType="com.concise.gen.correctionobjectinformation.entity.ScreenModel">
        SELECT
        exam_question_item.content AS item,
        count( DISTINCT correction_asses_person_dtl.sqjzry_id ) AS amount
        FROM
        exam_question_item
        LEFT JOIN correction_asses_answer ON exam_question_item.id = correction_asses_answer.answer_ids
        LEFT JOIN correction_asses_person_dtl ON correction_asses_answer.dtl_id = correction_asses_person_dtl.id
        WHERE
        exam_question_item.question_id = '1622418542845583362'
        and correction_asses_person_dtl.sqjzry_id in
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        exam_question_item.id
    </select>
    <select id="sourceOfIncome" resultType="com.concise.gen.correctionobjectinformation.entity.ScreenModel">
        SELECT
        exam_question_item.content AS item,
        count( DISTINCT correction_asses_person_dtl.sqjzry_id ) AS amount
        FROM
        exam_question_item
        LEFT JOIN correction_asses_answer ON exam_question_item.id = correction_asses_answer.answer_ids
        LEFT JOIN correction_asses_person_dtl ON correction_asses_answer.dtl_id = correction_asses_person_dtl.id
        WHERE
        exam_question_item.question_id = '1661917581592051713'
        and correction_asses_person_dtl.sqjzry_id in
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        exam_question_item.id
    </select>
    <select id="householdIncome" resultType="com.concise.gen.correctionobjectinformation.entity.ScreenModel">
        SELECT
        exam_question_item.content AS item,
        count( DISTINCT correction_asses_person_dtl.sqjzry_id ) AS amount
        FROM
        exam_question_item
        LEFT JOIN correction_asses_answer ON exam_question_item.id = correction_asses_answer.answer_ids
        LEFT JOIN correction_asses_person_dtl ON correction_asses_answer.dtl_id = correction_asses_person_dtl.id
        WHERE
        exam_question_item.question_id = '1622418542988189700'
        and correction_asses_person_dtl.sqjzry_id in
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        exam_question_item.id
    </select>
    <select id="householdDebt" resultType="com.concise.gen.correctionobjectinformation.entity.ScreenModel">
        SELECT
        exam_question_item.content AS item,
        count( DISTINCT correction_asses_person_dtl.sqjzry_id ) AS amount
        FROM
        exam_question_item
        LEFT JOIN correction_asses_answer ON exam_question_item.id = correction_asses_answer.answer_ids
        LEFT JOIN correction_asses_person_dtl ON correction_asses_answer.dtl_id = correction_asses_person_dtl.id
        WHERE
        exam_question_item.question_id = '1622418542451318792'
        and correction_asses_person_dtl.sqjzry_id in
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        exam_question_item.id
    </select>
    <select id="familyConflicts" resultType="com.concise.gen.correctionobjectinformation.entity.ScreenModel">
        SELECT
            exam_question_item.content AS item,
            count( DISTINCT correction_asses_person_dtl.sqjzry_id ) AS amount
        FROM
            exam_question_item
                LEFT JOIN correction_asses_answer ON exam_question_item.id = correction_asses_answer.answer_ids
                LEFT JOIN correction_asses_person_dtl ON correction_asses_answer.dtl_id = correction_asses_person_dtl.id
        WHERE
            exam_question_item.question_id = '1622418542883332099'
          AND exam_question_item.id IN ( '1622418542916886532', '1622418542916886533' )
        and correction_asses_person_dtl.sqjzry_id in
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
            exam_question_item.id
    </select>
    <select id="latestRevenueList"
            resultType="com.concise.gen.portraitImage.correctionassespersondtl.entity.CorrectionAssesPersonDtlModel">
        SELECT
            correction_asses_person_dtl.*,
            correction_asses_answer.answer_ids
        FROM
            correction_asses_person_dtl
                INNER JOIN (
                SELECT
                    MAX( correction_asses_person_dtl.end_time ) AS end_time,
                    sqjzry_id
                FROM
                    correction_asses_answer
                        INNER JOIN correction_asses_person_dtl ON correction_asses_answer.dtl_id = correction_asses_person_dtl.id
                WHERE
                    correction_asses_answer.question_id = '1622418542988189700'
                GROUP BY
                    correction_asses_person_dtl.sqjzry_id
            ) child ON correction_asses_person_dtl.end_time = child.end_time
                AND correction_asses_person_dtl.sqjzry_id = child.sqjzry_id
                INNER JOIN correction_asses_answer ON correction_asses_person_dtl.id = correction_asses_answer.dtl_id
        WHERE
            correction_asses_answer.question_id = '1622418542988189700'
    </select>
    <select id="liabilityList"
            resultType="com.concise.gen.portraitImage.correctionassespersondtl.entity.CorrectionAssesPersonDtlModel">
        SELECT
            correction_asses_person_dtl.*,
            correction_asses_answer.answer_ids
        FROM
            correction_asses_person_dtl
                INNER JOIN (
                SELECT
                    MAX( correction_asses_person_dtl.end_time ) AS end_time,
                    sqjzry_id
                FROM
                    correction_asses_answer
                        INNER JOIN correction_asses_person_dtl ON correction_asses_answer.dtl_id = correction_asses_person_dtl.id
                WHERE
                    correction_asses_answer.question_id = '1661924570707304450'
                GROUP BY
                    correction_asses_person_dtl.sqjzry_id
            ) child ON correction_asses_person_dtl.end_time = child.end_time
                AND correction_asses_person_dtl.sqjzry_id = child.sqjzry_id
                INNER JOIN correction_asses_answer ON correction_asses_person_dtl.id = correction_asses_answer.dtl_id
        WHERE
            correction_asses_answer.question_id = '1661924570707304450'
    </select>

</mapper>
