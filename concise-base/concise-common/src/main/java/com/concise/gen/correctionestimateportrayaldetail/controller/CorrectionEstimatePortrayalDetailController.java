package com.concise.gen.correctionestimateportrayaldetail. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.correctionestimateportrayaldetail. param.CorrectionEstimatePortrayalDetailParam;
import com.concise.gen.correctionestimateportrayaldetail. service.CorrectionEstimatePortrayalDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 画像明细控制器
 *
 * <AUTHOR>
 * @date 2022-11-30 19:58:01
 */
@Api(tags = "画像明细")
@RestController
public class CorrectionEstimatePortrayalDetailController {

    @Resource
    private CorrectionEstimatePortrayalDetailService correctionEstimatePortrayalDetailService;

    /**
     * 查询画像明细
     *
     * <AUTHOR>
     * @date 2022-11-30 19:58:01
     */
    @Permission
    @GetMapping("/correctionEstimatePortrayalDetail/page")
    @ApiOperation("画像明细_分页查询")
    @BusinessLog(title = "画像明细_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam) {
        return new SuccessResponseData(correctionEstimatePortrayalDetailService.page(correctionEstimatePortrayalDetailParam));
    }

    /**
     * 添加画像明细
     *
     * <AUTHOR>
     * @date 2022-11-30 19:58:01
     */
    @Permission
    @PostMapping("/correctionEstimatePortrayalDetail/add")
    @ApiOperation("画像明细_增加")
    @BusinessLog(title = "画像明细_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(CorrectionEstimatePortrayalDetailParam.add.class) CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam) {
        correctionEstimatePortrayalDetailService.add(correctionEstimatePortrayalDetailParam);
        return new SuccessResponseData();
    }

    /**
     * 删除画像明细
     *
     * <AUTHOR>
     * @date 2022-11-30 19:58:01
     */
    @Permission
    @PostMapping("/correctionEstimatePortrayalDetail/delete")
    @ApiOperation("画像明细_删除")
    @BusinessLog(title = "画像明细_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(CorrectionEstimatePortrayalDetailParam.delete.class) CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam) {
        correctionEstimatePortrayalDetailService.delete(correctionEstimatePortrayalDetailParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑画像明细
     *
     * <AUTHOR>
     * @date 2022-11-30 19:58:01
     */
    @Permission
    @PostMapping("/correctionEstimatePortrayalDetail/edit")
    @ApiOperation("画像明细_编辑")
    @BusinessLog(title = "画像明细_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(CorrectionEstimatePortrayalDetailParam.edit.class) CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam) {
        correctionEstimatePortrayalDetailService.edit(correctionEstimatePortrayalDetailParam);
        return new SuccessResponseData();
    }

    /**
     * 查看画像明细
     *
     * <AUTHOR>
     * @date 2022-11-30 19:58:01
     */
    @Permission
    @GetMapping("/correctionEstimatePortrayalDetail/detail")
    @ApiOperation("画像明细_查看")
    @BusinessLog(title = "画像明细_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(CorrectionEstimatePortrayalDetailParam.detail.class) CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam) {
        return new SuccessResponseData(correctionEstimatePortrayalDetailService.detail(correctionEstimatePortrayalDetailParam));
    }



    /**
     * 画像明细列表
     *
     * <AUTHOR>
     * @date 2022-11-30 19:58:01
     */
    @Permission
    @GetMapping("/correctionEstimatePortrayalDetail/list")
    @ApiOperation("画像明细_列表")
    @BusinessLog(title = "画像明细_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam) {
        return new SuccessResponseData(correctionEstimatePortrayalDetailService.list(correctionEstimatePortrayalDetailParam));
    }

}
