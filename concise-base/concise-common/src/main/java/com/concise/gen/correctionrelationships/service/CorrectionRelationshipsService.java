package com.concise.gen.correctionrelationships.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionrelationships.entity.CorrectionRelationships;
import com.concise.gen.correctionrelationships.param.CorrectionRelationshipsParam;

import java.util.List;

/**
 * 家庭成员service接口
 *
 * <AUTHOR>
 * @date 2022-02-22 16:07:50
 */
public interface CorrectionRelationshipsService extends IService<CorrectionRelationships> {

    /**
     * 查询家庭成员
     *
     * <AUTHOR>
     * @date 2022-02-22 16:07:50
     */
    PageResult<CorrectionRelationships> page(CorrectionRelationshipsParam correctionRelationshipsParam);

    /**
     * 家庭成员列表
     *
     * <AUTHOR>
     * @date 2022-02-22 16:07:50
     */
    List<CorrectionRelationships> list(CorrectionRelationshipsParam correctionRelationshipsParam);
}
