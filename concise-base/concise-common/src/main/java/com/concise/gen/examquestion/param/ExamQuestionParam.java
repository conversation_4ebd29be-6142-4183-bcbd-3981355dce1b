package com.concise.gen.examquestion.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 考试题目参数类
 *
 * <AUTHOR>
 * @date 2023-01-03 15:52:58
*/
@Data
public class ExamQuestionParam extends BaseParam {

    /**
     *
     */
    @NotNull(message = "不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     *
     */
    @NotBlank(message = "不能为空，请检查typeId参数", groups = {add.class, edit.class})
    private String typeId;

    /**
     *
     */
    @NotBlank(message = "不能为空，请检查typeName参数", groups = {add.class, edit.class})
    private String typeName;

    /**
     * 题型
     */
    @NotNull(message = "题型不能为空，请检查questionType参数", groups = {add.class, edit.class})
    private String questionType;

    /**
     *
     */
    @NotBlank(message = "不能为空，请检查questionTypeName参数", groups = {add.class, edit.class})
    private String questionTypeName;

    /**
     * 排序字段
     */
    @NotNull(message = "排序字段不能为空，请检查sort参数", groups = {add.class, edit.class})
    private Double sort;

    /**
     * 题干（长度不够改为text）
     */
    @NotBlank(message = "题干（长度不够改为text）不能为空，请检查stem参数", groups = {add.class, edit.class})
    private String stem;

    /**
     * 答案（多选题用逗号隔开）
     */
    @NotBlank(message = "答案（多选题用逗号隔开）不能为空，请检查answer参数", groups = {add.class, edit.class})
    private String answer;

    /**
     *
     */
    @NotBlank(message = "不能为空，请检查createBy参数", groups = {add.class, edit.class})
    private String createBy;

    /**
     *
     */
    @NotBlank(message = "不能为空，请检查updateBy参数", groups = {add.class, edit.class})
    private String updateBy;

    /**
     * 创建所属人
     */
    @NotBlank(message = "创建所属人不能为空，请检查createDepts参数", groups = {add.class, edit.class})
    private String createDepts;

}
