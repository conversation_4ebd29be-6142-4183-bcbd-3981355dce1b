package com.concise.gen.correctionsocialclassroom.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionsocialclassroom.entity.CorrectionSocialClassroom;
import com.concise.gen.correctionsocialclassroom.param.CorrectionSocialClassroomParam;
import java.util.List;

/**
 * 社矫大讲堂service接口
 *
 * <AUTHOR>
 * @date 2022-03-10 17:45:12
 */
public interface CorrectionSocialClassroomService extends IService<CorrectionSocialClassroom> {

    /**
     * 查询社矫大讲堂
     *
     * <AUTHOR>
     * @date 2022-03-10 17:45:12
     */
    PageResult<CorrectionSocialClassroom> page(CorrectionSocialClassroomParam correctionSocialClassroomParam);

    /**
     * 社矫大讲堂列表
     *
     * <AUTHOR>
     * @date 2022-03-10 17:45:12
     */
    List<CorrectionSocialClassroom> list(CorrectionSocialClassroomParam correctionSocialClassroomParam);

    /**
     * 添加社矫大讲堂
     *
     * <AUTHOR>
     * @date 2022-03-10 17:45:12
     */
    void add(CorrectionSocialClassroomParam correctionSocialClassroomParam);

    /**
     * 删除社矫大讲堂
     *
     * <AUTHOR>
     * @date 2022-03-10 17:45:12
     */
    void delete(CorrectionSocialClassroomParam correctionSocialClassroomParam);

    /**
     * 编辑社矫大讲堂
     *
     * <AUTHOR>
     * @date 2022-03-10 17:45:12
     */
    void edit(CorrectionSocialClassroomParam correctionSocialClassroomParam);

    /**
     * 查看社矫大讲堂
     *
     * <AUTHOR>
     * @date 2022-03-10 17:45:12
     */
     CorrectionSocialClassroom detail(CorrectionSocialClassroomParam correctionSocialClassroomParam);
}
