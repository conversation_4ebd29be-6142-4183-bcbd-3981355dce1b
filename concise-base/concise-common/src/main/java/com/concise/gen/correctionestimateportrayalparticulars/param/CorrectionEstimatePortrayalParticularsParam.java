package com.concise.gen.correctionestimateportrayalparticulars.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
* 心理画像明细（新）参数类
 *
 * <AUTHOR>
 * @date 2023-01-14 10:38:47
*/
@Data
public class CorrectionEstimatePortrayalParticularsParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 画像id
     */
    @NotBlank(message = "画像id不能为空，请检查portrayalId参数", groups = {add.class, edit.class})
    private String portrayalId;

    /**
     * 心理画像描述
     */
    @NotBlank(message = "心理画像描述不能为空，请检查xlhxms参数", groups = {add.class, edit.class})
    private String xlhxms;



    /**
     * 知法画像描述
     */
    @NotBlank(message = "知法画像描述不能为空，请检查zfhxms参数", groups = {add.class, edit.class})
    private String zfhxms;

    /**
     * 就业画像描述
     */
    @NotBlank(message = "就业画像描述不能为空，请检查jyhxms参数", groups = {add.class, edit.class})
    private String jyhxms;

    /**
     * 家庭画像描述
     */
    @NotBlank(message = "家庭画像描述不能为空，请检查jthxms参数", groups = {add.class, edit.class})
    private String jthxms;

    /**
     * 信用画像描述
     */
    @NotBlank(message = "信用画像描述不能为空，请检查xyhxms参数", groups = {add.class, edit.class})
    private String xyhxms;

    /**
     * 个人基本画像描述
     */
    @NotBlank(message = "个人基本画像描述不能为空，请检查jbhxms参数", groups = {add.class, edit.class})
    private String jbhxms;

    /**
     * 心理画像建议
     */
    @NotBlank(message = "心理画像建议不能为空，请检查xlhxjy参数", groups = {add.class, edit.class})
    private String xlhxjy;

    /**
     * 知法画像建议
     */
    @NotBlank(message = "知法画像建议不能为空，请检查zfhxjy参数", groups = {add.class, edit.class})
    private String zfhxjy;

    /**
     * 就业画像建议
     */
    @NotBlank(message = "就业画像建议不能为空，请检查jyhxjy参数", groups = {add.class, edit.class})
    private String jyhxjy;

    /**
     * 家庭画像建议
     */
    @NotBlank(message = "家庭画像建议不能为空，请检查jthxjy参数", groups = {add.class, edit.class})
    private String jthxjy;

    /**
     * 信用画像建议
     */
    @NotBlank(message = "信用画像建议不能为空，请检查xyhxjy参数", groups = {add.class, edit.class})
    private String xyhxjy;

    /**
     * 个人基本画像建议
     */
    @NotBlank(message = "个人基本画像建议不能为空，请检查jbhxjy参数", groups = {add.class, edit.class})
    private String jbhxjy;

    /**
     * 心理画像扣分明细
     */
    @NotBlank(message = "心理画像扣分明细不能为空，请检查xlhxkfmx参数", groups = {add.class, edit.class})
    private String xlhxkfmx;

    /**
     * 知法画像扣分明细
     */
    @NotBlank(message = "知法画像扣分明细不能为空，请检查zfhxkfmx参数", groups = {add.class, edit.class})
    private String zfhxkfmx;

    /**
     * 就业画像扣分明细
     */
    @NotBlank(message = "就业画像扣分明细不能为空，请检查jyhxkfmx参数", groups = {add.class, edit.class})
    private String jyhxkfmx;

    /**
     * 家庭画像扣分明细
     */
    @NotBlank(message = "家庭画像扣分明细不能为空，请检查jthxkfmx参数", groups = {add.class, edit.class})
    private String jthxkfmx;

    /**
     * 信用画像扣分明细
     */
    @NotBlank(message = "信用画像扣分明细不能为空，请检查xyhxkfmx参数", groups = {add.class, edit.class})
    private String xyhxkfmx;

    /**
     * 个人基本画像扣分明细
     */
    @NotBlank(message = "个人基本画像扣分明细不能为空，请检查jbhxkfmx参数", groups = {add.class, edit.class})
    private String jbhxkfmx;

    /**
     * 心理画像轨迹风险分析
     */
    @NotBlank(message = "心理画像轨迹风险分析不能为空，请检查xlhxgjfx参数", groups = {add.class, edit.class})
    private String xlhxgjfx;

    /**
     * 知法画像轨迹风险分析
     */
    @NotBlank(message = "知法画像轨迹风险分析不能为空，请检查zfhxgjfx参数", groups = {add.class, edit.class})
    private String zfhxgjfx;

    /**
     * 就业画像轨迹风险分析
     */
    @NotBlank(message = "就业画像轨迹风险分析不能为空，请检查jyhxgjfx参数", groups = {add.class, edit.class})
    private String jyhxgjfx;

    /**
     * 家庭画像轨迹风险分析
     */
    @NotBlank(message = "家庭画像轨迹风险分析不能为空，请检查jthxgjfx参数", groups = {add.class, edit.class})
    private String jthxgjfx;

    /**
     * 信用画像轨迹风险分析
     */
    @NotBlank(message = "信用画像轨迹风险分析不能为空，请检查xyhxgjfx参数", groups = {add.class, edit.class})
    private String xyhxgjfx;

    /**
     * 个人基本画像轨迹风险分析
     */
    @NotBlank(message = "个人基本画像轨迹风险分析不能为空，请检查jbhxgjfx参数", groups = {add.class, edit.class})
    private String jbhxgjfx;

    /**
     * 心理画像风险等级
     */
    @NotBlank(message = "心理画像风险等级不能为空，请检查xlhxLevel参数", groups = {add.class, edit.class})
    private String xlhxLevel;

    /**
     * 知法画像风险等级
     */
    @NotBlank(message = "知法画像风险等级不能为空，请检查zfhxLevel参数", groups = {add.class, edit.class})
    private String zfhxLevel;

    /**
     * 就业画像风险等级
     */
    @NotBlank(message = "就业画像风险等级不能为空，请检查jyhxLevel参数", groups = {add.class, edit.class})
    private String jyhxLevel;

    /**
     * 家庭画像风险等级
     */
    @NotBlank(message = "家庭画像风险等级不能为空，请检查jthxLevel参数", groups = {add.class, edit.class})
    private String jthxLevel;

    /**
     * 信用画像风险等级
     */
    @NotBlank(message = "信用画像风险等级不能为空，请检查xyhxLevel参数", groups = {add.class, edit.class})
    private String xyhxLevel;

    /**
     * 个人基本画像风险等级
     */
    @NotBlank(message = "个人基本画像风险等级不能为空，请检查jbhxLevel参数", groups = {add.class, edit.class})
    private String jbhxLevel;

    /**
     * 心理画像权重总分
     */
    @NotNull(message = "心理画像权重总分不能为空，请检查xlhxZf参数", groups = {add.class, edit.class})
    private BigDecimal xlhxZf;

    /**
     * 知法画像权重总分
     */
    @NotNull(message = "知法画像权重总分不能为空，请检查zfhxZf参数", groups = {add.class, edit.class})
    private BigDecimal zfhxZf;

    /**
     * 就业画像权重总分
     */
    @NotNull(message = "就业画像权重总分不能为空，请检查jyhxZf参数", groups = {add.class, edit.class})
    private BigDecimal jyhxZf;

    /**
     * 家庭画像权重总分
     */
    @NotNull(message = "家庭画像权重总分不能为空，请检查jthxZf参数", groups = {add.class, edit.class})
    private BigDecimal jthxZf;

    /**
     * 信用画像权重总分
     */
    @NotNull(message = "信用画像权重总分不能为空，请检查xyhxZf参数", groups = {add.class, edit.class})
    private BigDecimal xyhxZf;

    /**
     * 个人基本画像权重总分
     */
    @NotNull(message = "个人基本画像权重总分不能为空，请检查jbhxZf参数", groups = {add.class, edit.class})
    private BigDecimal jbhxZf;

    /**
     * 心理画像扣分
     */
    @NotNull(message = "心理画像扣分不能为空，请检查xlhxKf参数", groups = {add.class, edit.class})
    private BigDecimal xlhxKf;

    /**
     * 知法画像扣分
     */
    @NotNull(message = "知法画像扣分不能为空，请检查zfhxKf参数", groups = {add.class, edit.class})
    private BigDecimal zfhxKf;

    /**
     * 就业画像扣分
     */
    @NotNull(message = "就业画像扣分不能为空，请检查jyhxKf参数", groups = {add.class, edit.class})
    private BigDecimal jyhxKf;

    /**
     * 家庭画像扣分
     */
    @NotNull(message = "家庭画像扣分不能为空，请检查jthxKf参数", groups = {add.class, edit.class})
    private BigDecimal jthxKf;

    /**
     * 信用画像扣分
     */
    @NotNull(message = "信用画像扣分不能为空，请检查xyhxKf参数", groups = {add.class, edit.class})
    private BigDecimal xyhxKf;

    /**
     * 个人基本画像扣分
     */
    @NotNull(message = "个人基本画像扣分不能为空，请检查jbhxKf参数", groups = {add.class, edit.class})
    private BigDecimal jbhxKf;

    /**
     * 心理画像轨迹风险分析
     */
    @NotBlank(message = "心理画像轨迹风险分析不能为空，请检查xlhxGjqs参数", groups = {add.class, edit.class})
    private String xlhxGjqs;

    /**
     * 知法画像轨迹风险分析
     */
    @NotBlank(message = "知法画像轨迹风险分析不能为空，请检查zfhxGjqs参数", groups = {add.class, edit.class})
    private String zfhxGjqs;

    /**
     * 就业画像轨迹风险分析
     */
    @NotBlank(message = "就业画像轨迹风险分析不能为空，请检查jyhxGjqs参数", groups = {add.class, edit.class})
    private String jyhxGjqs;

    /**
     * 家庭画像轨迹风险分析
     */
    @NotBlank(message = "家庭画像轨迹风险分析不能为空，请检查jthxGjqs参数", groups = {add.class, edit.class})
    private String jthxGjqs;

    /**
     * 信用画像轨迹风险分析
     */
    @NotBlank(message = "信用画像轨迹风险分析不能为空，请检查xyhxGjqs参数", groups = {add.class, edit.class})
    private String xyhxGjqs;

    /**
     * 个人基本画像轨迹风险分析
     */
    @NotBlank(message = "个人基本画像轨迹风险分析不能为空，请检查jbhxGjqs参数", groups = {add.class, edit.class})
    private String jbhxGjqs;

    /**
     * 是否删除（0：未删除，1删除）
     */
    @NotNull(message = "是否删除（0：未删除，1删除）不能为空，请检查delFlag参数", groups = {add.class, edit.class})
    private Integer delFlag;

}
