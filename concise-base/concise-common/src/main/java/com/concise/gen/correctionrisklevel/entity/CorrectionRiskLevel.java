package com.concise.gen.correctionrisklevel.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 风险等级
 *
 * <AUTHOR>
 * @date 2022-05-18 18:29:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_risk_level")
public class CorrectionRiskLevel extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 评分月度
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer scoringTime;

    /**
     * 评分模型ID
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String scoringModelId;

    /**
     * 矫正对象id
     */
    private String sqjzryId;

    /**
     * 姓名
     */
    private String sqjzryName;

    /**
     * 身份证号
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String sfzh;

    /**
     * 矫正机构ID
     */
    private String jzjg;

    /**
     * 矫正机构名称
     */
    private String jzjgName;

    /**
     * 基础分
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal scoreBase;

    /**
     * 加分项
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal scoreBonus;

    /**
     * 评估分(当月)
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal scoreEstimate;

    /**
     * 风险等级(当月)
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String riskLevel;

    /**
     * 评估总分(矫正期)
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal scoreEstimateAverage;

    /**
     * 风险等级(矫正期)
     */
    private String riskLevelAverage;

    /**
     * 日常监管_实时监控情况
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String dailySupervision;

    /**
     * 处罚_实时监控情况
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String punish;

    /**
     * 行为动态_实时监控情况
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String actionTrends;

    /**
     * 心理动态_实时监控情况
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String psychicAction;

    /**
     * 入矫评估_得分占比
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String estimateEnterPercent;

    /**
     * 日常监管_得分占比
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String dailySupervisionPercent;

    /**
     * 处罚_得分占比
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String punishPercent;

    /**
     * 行为动态_得分占比
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String actionTrendsPercent;

    /**
     * 心理状态_得分占比
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String psychicActionPercent;

    /**
     * 日常监管_普管降严管
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String dailySupervisionDetail;

    /**
     * 日常监管_信息化违规次数
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer yqViolate;

    /**
     * 处罚_训诫_次
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer advise;

    /**
     * 处罚_警告_次
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer warn;

    /**
     * 处罚_治安处罚_次
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer publicSecurity;

    /**
     * 处罚_提请逮捕_次
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer askArrest;

    /**
     * 处罚_提请撤缓_次
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer cancelProbation;

    /**
     * 处罚_提请撤销假释_次
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer cancelParole;

    /**
     * 处罚_提请收监执行_次
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer committedToPrison;

    /**
     * 处罚_行政处罚_次
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer xzPunish;

    /**
     * 行为动态_工作变动_次
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer workChange;

    /**
     * 行为动态_夜不归宿_次
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer nightOut;

    /**
     * 心理状态
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String psychicStatus;

    /**
     * 总结分析
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String summary;

    /**
     * 是否删除（0：未删除，1删除）
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer delFlag;

    /**
     * 时间
     */
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String time;

    /**
     * 内容
     */
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String content;

    /**
     * 日常监管得分(该字段暂未使用)
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal rcjgScore;

    /**
     * 处罚得分(该字段暂未使用)
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal cfScore;

    /**
     * 行为动态得分(该字段暂未使用)
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal xwdtScore;

    /**
     * 心理状态得分(该字段暂未使用)
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal xlztScore;
}
