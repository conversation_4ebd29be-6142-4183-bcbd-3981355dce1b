package com.concise.gen.portraitImage.correctionassespersondtl.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.concise.gen.examquestion.entity.ExamQuestion;
import com.concise.gen.portraitImage.correctionassesanswer.entity.CorrectionAssesAnswer;
import com.concise.gen.portraitImage.correctionquestion.entity.CorrectionQuestion;
import com.concise.gen.portraitImage.correctionquestionitem.entity.CorrectionQuestionItem;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.List;

/**
 * 评估管理--评估人员评估明细（问卷）
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:43
 */
@Data
@TableName("correction_asses_person_dtl")
public class CorrectionAssesPersonDtl {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 评估人员信息ID
     */
    private String assesPersonId;
    /**
     * 矫正人员信息ID
     */
    private String sqjzryId;
    /**
     * 任务名称
     */
    private String title;

    /**
     * 量表id，冗余，方便查找题目
     */
    private String scaleBaseId;

    /**
     * 测评开始时间，格式: yyyyMMdd
     */
    private String startTime;

    /**
     * 测评结束时间，格式: yyyyMMdd
     */
    private String endTime;

    /**
     * 填写状态：0：未填写 1: 已填写
     */
    private Integer status;

    /**
     * 序号
     */
    private Integer orderIndex;

    /**
     * 是否临期提醒：0：否  1：是
     */
    private Integer needWarn;

    /**
     * 临期天数
     */
    private Integer dayNum;

    /**
     * 移动端是否显示提醒： 0：否  1:是
     */
    private Integer tag;

    /**
     * 问卷答案
     */
    @TableField(exist = false)
    private List<CorrectionAssesAnswer> answerList;

    /**
     * 问题&答案(查看时使用)
     */
    @TableField(exist = false)
    private List<CorrectionQuestion> questionList;

    @TableField(exist = false)
    private List<ExamQuestion> examQuestionList;
}
