<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.correctionlabelmanage.mapper.CorrectionLabelManageMapper">

    <resultMap id="lableResult" type="com.concise.gen.correctionlabelmanage.entity.CorrectionLabelManage">
        <result column="id" property="id" />
        <result column="label" property="label" />
        <result column="label_attribute" property="labelAttribute" />
        <result column="label_attribute_code" property="labelAttributeCode" />
        <result column="attribute_type" property="attributeType" />
        <result column="attribute_type_code" property="attributeTypeCode" />
        <result column="remark" property="remark" />
        <result column="sqjzry_num" property="sqjzryNum" />
        <result column="dict_num" property="dictNum" />
        <result column="belong_sys" property="belongSys" />
        <result column="order_index" property="orderIndex" />
    </resultMap>

    <select id="page" resultMap="lableResult">
        select lm.parent_id,lm.level,lm.id, lm.label, lm.label_attribute, lm.label_attribute_code, lm.attribute_type,
         lm.attribute_type_code, lm.remark, lm.belong_sys, lm.order_index,
        (select count(c.id) from correction_label_corrpsn c where c.label_id = lm.id) as sqjzry_num,
        (select count(d.id) from correction_label_dictionary d where d.label_id = lm.id) as dict_num
        FROM correction_label_manage lm ${ew.customSqlSegment} ORDER BY lm.attribute_type_code, lm.label_attribute_code asc
    </select>

    <select id="findByAbilityId" parameterType="Long" resultMap="lableResult">
        SELECT id, label FROM correction_label_manage WHERE id
            in(SELECT lable_id FROM correction_ability_lable WHERE ability_id = #{abilityId}) AND del_flag = 0
    </select>

    <select id="findByCaseId" parameterType="Long" resultMap="lableResult">
        SELECT id, label FROM correction_label_manage WHERE id
            in(SELECT lable_id FROM correction_case_lable WHERE case_id = #{caseId}) AND del_flag = 0
    </select>
</mapper>
