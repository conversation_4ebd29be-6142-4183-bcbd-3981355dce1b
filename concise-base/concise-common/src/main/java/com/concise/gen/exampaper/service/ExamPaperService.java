package com.concise.gen.exampaper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.exampaper.entity.ExamPaper;
import com.concise.gen.exampaper.param.ExamPaperParam;
import com.concise.gen.examquestion.entity.ExamQuestion;

import java.util.List;

/**
 * 新量表管理service接口
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:03
 */
public interface ExamPaperService extends IService<ExamPaper> {

    /**
     * 查询新量表管理
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:03
     */
    PageResult<ExamPaper> page(ExamPaperParam examPaperParam);

    /**
     * 新量表管理列表
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:03
     */
    List<ExamPaper> list(ExamPaperParam examPaperParam);

    /**
     * 添加新量表管理
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:03
     */
    void add(ExamPaperParam examPaperParam);

    /**
     * 删除新量表管理
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:03
     */
    void delete(ExamPaperParam examPaperParam);

    /**
     * 编辑新量表管理
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:03
     */
    void edit(ExamPaperParam examPaperParam);

    /**
     * 查看新量表管理
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:03
     */
    ExamPaper detail(ExamPaperParam examPaperParam);


    /**
     * 量表包含题目的树
     * @param examPaperParam
     * @return
     */
    List<ExamQuestion> examPaperTreeSelected(String id);
}
