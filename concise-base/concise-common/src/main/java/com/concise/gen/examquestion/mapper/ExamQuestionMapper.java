package com.concise.gen.examquestion.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.examquestion.entity.ExamQuestion;
import com.concise.gen.portraitImage.correctionassesanswer.entity.CorrectionAssesAnswer;

import java.util.List;

/**
 * 考试题目
 *
 * <AUTHOR>
 * @date 2023-01-03 15:52:58
 */
public interface ExamQuestionMapper extends BaseMapper<ExamQuestion> {
    List<CorrectionAssesAnswer> householdDebtList();
}
