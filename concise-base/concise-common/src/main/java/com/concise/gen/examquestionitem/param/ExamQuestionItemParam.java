package com.concise.gen.examquestionitem.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 问题选项参数类
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:11
*/
@Data
public class ExamQuestionItemParam extends BaseParam {

    /**
     * 
     */
    @NotNull(message = "不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 关联exam_question id
     */
    @NotBlank(message = "关联exam_question id不能为空，请检查questionId参数", groups = {add.class, edit.class})
    private String questionId;

    /**
     * 选项内容
     */
    @NotBlank(message = "选项内容不能为空，请检查content参数", groups = {add.class, edit.class})
    private String content;

    /**
     * 排序字段
     */
    @NotNull(message = "排序字段不能为空，请检查sort参数", groups = {add.class, edit.class})
    private Double sort;

}
