package com.concise.gen.portraitImage.correctionassesbase.param;

import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
* 评估管理--基本信息参数类
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:40
*/
@Data
public class CorrectionAssesBaseParam extends BaseParam {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String title;

    /**
     * 量表id
     */
    @ApiModelProperty(value = "量表id")
    private String scaleBaseId;

    /**
     * 量表名称
     */
    @ApiModelProperty(value = "量表名称")
    private String scaleBaseName;

    /**
     * 测评阶段：0：入矫 1: 在矫
     */
    @ApiModelProperty(value = "测评阶段：0：入矫 1: 在矫")
    private Integer phase;

    /**
     * 测评周期：0：单词 1：一月一次
     */
    @ApiModelProperty(value = "测评周期：0：单词 1：一月一次")
    private Integer cycle;

    /**
     * 测评开始时间，格式: yyyyMMdd
     */
    @ApiModelProperty(value = "测评开始时间，格式: yyyyMMdd")
    private String startTime;

    /**
     * 测评结束时间，格式: yyyyMMdd
     */
    @ApiModelProperty(value = "测评结束时间，格式: yyyyMMdd")
    private String endTime;

    /**
     * 测评范围：0：部门  1：人员 (需求变更，同时可选部门和人员，该字段失效)
     */
    @ApiModelProperty(value = "测评范围：0：部门  1：人员 (需求变更，同时可选部门和人员，该字段失效)")
    private Integer assesScope;

    /**
     * 是否临期提醒：0：否  1：是
     */
    @ApiModelProperty(value = "是否临期提醒：0：否  1：是")
    private Integer needWarn;

    /**
     * 临期天数
     */
    @ApiModelProperty(value = "临期天数")
    private Integer dayNum;

    /**
     * 进度--总次数
     */
    @ApiModelProperty(value = "进度--总次数")
    private Integer totalNum;

    /**
     * 进度--完成次数
     */
    @ApiModelProperty(value = "进度--完成次数")
    private Integer completeNum;

    /**
     * 状态：0：未开始 1：进行中 2：已完成 3：停止
     */
    @ApiModelProperty(value = "状态：0：未开始 1：进行中 2：已完成 3：停止")
    private Integer status;

    /**
     * 是否删除（0：未删除，1删除）
     */
    @ApiModelProperty(value = "是否删除（0：未删除，1删除）")
    private Integer delFlag;

    /**
     * 测评开始时间_begin
     */
    @ApiModelProperty(value = "测评开始时间_begin")
    private String startTime_begin;

    /**
     * 测评开始时间_end
     */
    @ApiModelProperty(value = "测评开始时间_end")
    private String startTime_end;

    /**
     * 机构ids
     */
    @ApiModelProperty(value = "机构ids")
    private List<String> deptIds;

    /**
     * 矫正对象集合
     */
    @ApiModelProperty(value = "矫正对象集合")
    private List<CorrectionObjectInformation> sqjzryList;
}
