package com.concise.gen.correctmoodcode.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctmoodcode.entity.CorrectMoodCode;
import com.concise.gen.correctmoodcode.enums.CorrectMoodCodeExceptionEnum;
import com.concise.gen.correctmoodcode.mapper.CorrectMoodCodeMapper;
import com.concise.gen.correctmoodcode.param.CorrectMoodCodeParam;
import com.concise.gen.correctmoodcode.service.CorrectMoodCodeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 心情码service接口实现类
 *
 * <AUTHOR>
 * @date 2022-05-26 18:01:13
 */
@Service
public class CorrectMoodCodeServiceImpl extends ServiceImpl<CorrectMoodCodeMapper, CorrectMoodCode> implements CorrectMoodCodeService {

    @Override
    public PageResult<CorrectMoodCode> page(CorrectMoodCodeParam correctMoodCodeParam) {
        QueryWrapper<CorrectMoodCode> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctMoodCodeParam)) {

            // 根据矫正对象id(万达平台) 查询
            if (ObjectUtil.isNotEmpty(correctMoodCodeParam.getThirdId())) {
                queryWrapper.lambda().eq(CorrectMoodCode::getThirdId, correctMoodCodeParam.getThirdId());
            }
            // 根据心情码 5：绿码、4：蓝码、3：黄码、2：橙码、1：红码 查询
            if (ObjectUtil.isNotEmpty(correctMoodCodeParam.getMoodCode())) {
                queryWrapper.lambda().eq(CorrectMoodCode::getMoodCode, correctMoodCodeParam.getMoodCode());
            }
            // 根据矫正对象姓名 查询
            if (ObjectUtil.isNotEmpty(correctMoodCodeParam.getUserName())) {
                queryWrapper.lambda().eq(CorrectMoodCode::getUserName, correctMoodCodeParam.getUserName());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectMoodCode> list(CorrectMoodCodeParam correctMoodCodeParam) {
        return this.list();
    }

    @Override
    public void add(CorrectMoodCodeParam correctMoodCodeParam) {
        CorrectMoodCode correctMoodCode = new CorrectMoodCode();
        BeanUtil.copyProperties(correctMoodCodeParam, correctMoodCode);
        this.save(correctMoodCode);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectMoodCodeParam correctMoodCodeParam) {
        this.removeById(correctMoodCodeParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectMoodCodeParam correctMoodCodeParam) {
        CorrectMoodCode correctMoodCode = this.queryCorrectMoodCode(correctMoodCodeParam);
        BeanUtil.copyProperties(correctMoodCodeParam, correctMoodCode);
        this.updateById(correctMoodCode);
    }

    @Override
    public CorrectMoodCode detail(CorrectMoodCodeParam correctMoodCodeParam) {
        return this.queryCorrectMoodCode(correctMoodCodeParam);
    }

    /**
     * 获取心情码
     *
     * <AUTHOR>
     * @date 2022-05-26 18:01:13
     */
    private CorrectMoodCode queryCorrectMoodCode(CorrectMoodCodeParam correctMoodCodeParam) {
        CorrectMoodCode correctMoodCode = this.getById(correctMoodCodeParam.getId());
        if (ObjectUtil.isNull(correctMoodCode)) {
            throw new ServiceException(CorrectMoodCodeExceptionEnum.NOT_EXIST);
        }
        return correctMoodCode;
    }

    @Override
    public String getMoodCode(String thirdId) {
        String moodCode = "";
        QueryWrapper<CorrectMoodCode> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CorrectMoodCode::getThirdId, thirdId);
        queryWrapper.lambda().select(CorrectMoodCode::getMoodCode);
        queryWrapper.lambda().orderByDesc(CorrectMoodCode::getUpdateTime);
        List<CorrectMoodCode> list = this.list(queryWrapper);
        if (list.size() > 0) {
            moodCode = list.get(0).getMoodCode();
        }
        return moodCode;
    }
}
