package com.concise.gen.portraitImage.correctionassespersondtl.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.exampaper.entity.ExamPaper;
import com.concise.gen.exampaper.param.ExamPaperParam;
import com.concise.gen.exampaper.service.ExamPaperService;
import com.concise.gen.examquestion.entity.ExamQuestion;
import com.concise.gen.examquestion.service.ExamQuestionService;
import com.concise.gen.examquestionitem.entity.ExamQuestionItem;
import com.concise.gen.portraitImage.correctionassesanswer.entity.CorrectionAssesAnswer;
import com.concise.gen.portraitImage.correctionassesanswer.mapper.CorrectionAssesAnswerMapper;
import com.concise.gen.portraitImage.correctionassespersondtl.entity.CorrectionAssesPersonDtl;
import com.concise.gen.portraitImage.correctionassespersondtl.enums.CorrectionAssesPersonDtlExceptionEnum;
import com.concise.gen.portraitImage.correctionassespersondtl.mapper.CorrectionAssesPersonDtlMapper;
import com.concise.gen.portraitImage.correctionassespersondtl.param.CorrectionAssesPersonDtlParam;
import com.concise.gen.portraitImage.correctionassespersondtl.service.CorrectionAssesPersonDtlService;
import com.concise.gen.portraitImage.correctionquestion.entity.CorrectionQuestion;
import com.concise.gen.portraitImage.correctionquestion.service.CorrectionQuestionService;
import com.concise.gen.portraitImage.correctionquestionitem.entity.CorrectionQuestionItem;
import com.concise.gen.portraitImage.correctionquestionitem.service.CorrectionQuestionItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 评估管理--评估人员评估明细service接口实现类
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:43
 */
@Service
public class CorrectionAssesPersonDtlServiceImpl extends ServiceImpl<CorrectionAssesPersonDtlMapper, CorrectionAssesPersonDtl> implements CorrectionAssesPersonDtlService {

    @Resource
    private CorrectionQuestionService correctionQuestionService;

    @Resource
    private CorrectionQuestionItemService correctionQuestionItemService;

    @Resource
    private CorrectionAssesAnswerMapper correctionAssesAnswerMapper;

    @Resource
    private ExamPaperService examPaperService;

    @Override
    public PageResult<CorrectionAssesPersonDtl> page(CorrectionAssesPersonDtlParam correctionAssesPersonDtlParam) {
        QueryWrapper<CorrectionAssesPersonDtl> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionAssesPersonDtlParam)) {

            // 根据评估人员信息ID 查询
            if (ObjectUtil.isNotEmpty(correctionAssesPersonDtlParam.getAssesPersonId())) {
                queryWrapper.lambda().eq(CorrectionAssesPersonDtl::getAssesPersonId, correctionAssesPersonDtlParam.getAssesPersonId());
            }
            // 根据任务名称 查询
            if (ObjectUtil.isNotEmpty(correctionAssesPersonDtlParam.getTitle())) {
                queryWrapper.lambda().eq(CorrectionAssesPersonDtl::getTitle, correctionAssesPersonDtlParam.getTitle());
            }
            // 根据量表id，冗余，方便查找题目 查询
            if (ObjectUtil.isNotEmpty(correctionAssesPersonDtlParam.getScaleBaseId())) {
                queryWrapper.lambda().eq(CorrectionAssesPersonDtl::getScaleBaseId, correctionAssesPersonDtlParam.getScaleBaseId());
            }
            // 根据测评开始时间，格式: yyyyMMdd 查询
            if (ObjectUtil.isNotEmpty(correctionAssesPersonDtlParam.getStartTime())) {
                queryWrapper.lambda().eq(CorrectionAssesPersonDtl::getStartTime, correctionAssesPersonDtlParam.getStartTime());
            }
            // 根据测评结束时间，格式: yyyyMMdd 查询
            if (ObjectUtil.isNotEmpty(correctionAssesPersonDtlParam.getEndTime())) {
                queryWrapper.lambda().eq(CorrectionAssesPersonDtl::getEndTime, correctionAssesPersonDtlParam.getEndTime());
            }
            // 根据填写状态：0：未填写 1: 已填写 查询
            if (ObjectUtil.isNotEmpty(correctionAssesPersonDtlParam.getStatus())) {
                queryWrapper.lambda().eq(CorrectionAssesPersonDtl::getStatus, correctionAssesPersonDtlParam.getStatus());
            }
            // 根据序号 查询
            if (ObjectUtil.isNotEmpty(correctionAssesPersonDtlParam.getOrderIndex())) {
                queryWrapper.lambda().eq(CorrectionAssesPersonDtl::getOrderIndex, correctionAssesPersonDtlParam.getOrderIndex());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionAssesPersonDtl> list(CorrectionAssesPersonDtlParam correctionAssesPersonDtlParam) {
        QueryWrapper<CorrectionAssesPersonDtl> queryWrapper = new QueryWrapper<>();
        // 根据评估人员信息ID 查询
        if (ObjectUtil.isNotEmpty(correctionAssesPersonDtlParam.getAssesPersonId())) {
            queryWrapper.lambda().eq(CorrectionAssesPersonDtl::getAssesPersonId, correctionAssesPersonDtlParam.getAssesPersonId());
        }
        return this.list(queryWrapper);
    }

    @Override
    public void add(CorrectionAssesPersonDtlParam correctionAssesPersonDtlParam) {
        CorrectionAssesPersonDtl correctionAssesPersonDtl = new CorrectionAssesPersonDtl();
        BeanUtil.copyProperties(correctionAssesPersonDtlParam, correctionAssesPersonDtl);
        this.save(correctionAssesPersonDtl);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionAssesPersonDtlParam correctionAssesPersonDtlParam) {
        this.removeById(correctionAssesPersonDtlParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionAssesPersonDtlParam correctionAssesPersonDtlParam) {
        CorrectionAssesPersonDtl correctionAssesPersonDtl = this.queryCorrectionAssesPersonDtl(correctionAssesPersonDtlParam);
        BeanUtil.copyProperties(correctionAssesPersonDtlParam, correctionAssesPersonDtl);
        this.updateById(correctionAssesPersonDtl);
    }

    @Override
    public CorrectionAssesPersonDtl detail(CorrectionAssesPersonDtlParam correctionAssesPersonDtlParam) {
        return this.newQueryCorrectionAssesPersonDtl(correctionAssesPersonDtlParam);
    }

    /**
     * 新量表构建题目和答案
     * @param correctionAssesPersonDtlParam
     * @return
     */
    private CorrectionAssesPersonDtl newQueryCorrectionAssesPersonDtl(CorrectionAssesPersonDtlParam correctionAssesPersonDtlParam) {
        CorrectionAssesPersonDtl correctionAssesPersonDtl = this.getById(correctionAssesPersonDtlParam.getId());
        if (ObjectUtil.isNull(correctionAssesPersonDtl)) {
            throw new ServiceException(CorrectionAssesPersonDtlExceptionEnum.NOT_EXIST);
        }
        ExamPaperParam examPaperParam=new ExamPaperParam();
        examPaperParam.setId(correctionAssesPersonDtl.getScaleBaseId());
        List<ExamQuestion> examQuestionList = examPaperService.detail(examPaperParam).getQuestionList();
        List<CorrectionAssesAnswer> correctionAssesAnswers = correctionAssesAnswerMapper.selectList(new QueryWrapper<CorrectionAssesAnswer>().lambda().eq(CorrectionAssesAnswer::getDtlId,correctionAssesPersonDtl.getId()));
        if (CollectionUtil.isNotEmpty(correctionAssesAnswers)){
            Map<String, String> answerMap = correctionAssesAnswers.stream().collect(Collectors.toMap(CorrectionAssesAnswer::getQuestionId, CorrectionAssesAnswer::getAnswerIds));
            List<String> answerIds = correctionAssesAnswers.stream().map(CorrectionAssesAnswer::getAnswerIds).collect(Collectors.toList());
            for (ExamQuestion examQuestion : examQuestionList) {
                examQuestion.setAnswerIds(answerMap.get(examQuestion.getId()));
                List<ExamQuestionItem> examQuestionItemList = examQuestion.getExamQuestionItemList();
                if (CollectionUtil.isNotEmpty(examQuestionItemList)){
                    for (ExamQuestionItem examQuestionItem : examQuestionItemList) {
                        //遍历选项，如果有选中的选项，赋值
                        //单选情况
                        if ("1".equals(examQuestion.getQuestionType())){
                            if (answerIds.contains(examQuestionItem.getId())){
                                examQuestionItem.setChecked(true);
                            }else {
                                examQuestionItem.setChecked(false);
                            }
                        }
                        //多选情况
                        if ("2".equals(examQuestion.getQuestionType())){
                            for (String answerId : answerIds) {
                                if (answerId.contains(examQuestionItem.getId())){
                                    examQuestionItem.setChecked(true);
                                }
                            }
                            if (ObjectUtil.isEmpty(examQuestionItem.isChecked())){
                                examQuestionItem.setChecked(false);
                            }
                        }

                        //看有没有关联子选项，并对选中的选项进行赋值
                        if (CollectionUtil.isNotEmpty(examQuestionItem.getExamQuestion())){
                            for (ExamQuestion question : examQuestionItem.getExamQuestion()) {
                                question.setAnswerIds(answerMap.get(question.getId()));
                                List<ExamQuestionItem> questionItemList = question.getExamQuestionItemList();
                                if (CollectionUtil.isNotEmpty(questionItemList)){
                                    for (ExamQuestionItem questionItem : questionItemList) {
                                        //遍历选项，如果有选中的选项，赋值
                                        //单选情况
                                        if ("1".equals(question.getQuestionType())){
                                            questionItem.setChecked(answerIds.contains(questionItem.getId()));
                                        }
                                        //多选情况
                                        if ("2".equals(question.getQuestionType())){
                                            for (String answerId : answerIds) {
                                                if (answerId.contains(questionItem.getId())){
                                                    questionItem.setChecked(true);
                                                }
                                            }
                                            if (ObjectUtil.isEmpty(questionItem.isChecked())){
                                                questionItem.setChecked(false);
                                            }
                                        }

                                    }
                                }

                            }
                        }
                    }
                }

            }
        }
        correctionAssesPersonDtl.setExamQuestionList(examQuestionList);
        return correctionAssesPersonDtl;
    }


    /**
     * 获取评估管理--评估人员评估明细
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:43
     */
    private CorrectionAssesPersonDtl queryCorrectionAssesPersonDtl(CorrectionAssesPersonDtlParam correctionAssesPersonDtlParam) {
        CorrectionAssesPersonDtl correctionAssesPersonDtl = this.getById(correctionAssesPersonDtlParam.getId());
        if (ObjectUtil.isNull(correctionAssesPersonDtl)) {
            throw new ServiceException(CorrectionAssesPersonDtlExceptionEnum.NOT_EXIST);
        }
        //根据量表找出所有题目
        //拼试题
        QueryWrapper<CorrectionQuestion> queryQuestion = new QueryWrapper<>();
        queryQuestion.lambda().eq(CorrectionQuestion::getScaleBaseId, correctionAssesPersonDtl.getScaleBaseId());
        queryQuestion.lambda().eq(CorrectionQuestion::getDelFlag, 0);
        queryQuestion.lambda().orderByAsc(CorrectionQuestion::getOrderIndex);
        List<CorrectionQuestion> questionList = correctionQuestionService.list(queryQuestion);
        QueryWrapper<CorrectionQuestionItem> queryQuestionItem = null;
        List<CorrectionQuestionItem> itemList = null;
        for (CorrectionQuestion question : questionList) {
            //赋值答案
            question.setChooseItemIds(correctionAssesAnswerMapper.get(correctionAssesPersonDtl.getId(), question.getId()));
            queryQuestionItem = new QueryWrapper<>();
            queryQuestionItem.lambda().eq(CorrectionQuestionItem::getQuestionId, question.getId());
            queryQuestionItem.lambda().eq(CorrectionQuestionItem::getDelFlag, 0);
            queryQuestionItem.lambda().orderByAsc(CorrectionQuestionItem::getOrderIndex);
            itemList = correctionQuestionItemService.list(queryQuestionItem);
            for (CorrectionQuestionItem questionItem : itemList) {
                if (question.getChooseItemIds().indexOf(questionItem.getId()) > -1) {
                    questionItem.setChecked(true);
                }
                if (1 == questionItem.getHaveHigherQuestion() && question.getChooseItemIds().indexOf(questionItem.getId()) > -1) {
                    //选项是关联问题且选择了才执行
                    //有关联问题
                    queryQuestion = new QueryWrapper<>();
                    queryQuestion.lambda().eq(CorrectionQuestion::getRelevanceItemId, questionItem.getId());
                    queryQuestion.lambda().orderByAsc(CorrectionQuestion::getOrderIndex);
                    List<CorrectionQuestion> questionListOne = correctionQuestionService.list(queryQuestion);
                    for (CorrectionQuestion questionOne : questionListOne) {
                        questionOne.setChooseItemIds(correctionAssesAnswerMapper.get(correctionAssesPersonDtl.getId(), questionOne.getId()));
                        QueryWrapper<CorrectionQuestionItem> queryQuestionItemOne = new QueryWrapper<>();
                        queryQuestionItemOne.lambda().eq(CorrectionQuestionItem::getQuestionId, questionOne.getId());
                        queryQuestionItemOne.lambda().orderByAsc(CorrectionQuestionItem::getOrderIndex);
                        List<CorrectionQuestionItem> itemOneList = correctionQuestionItemService.list(queryQuestionItemOne);
                        for (CorrectionQuestionItem itemOne : itemOneList) {
                            if (questionOne.getChooseItemIds().indexOf(itemOne.getId()) > -1) {
                                itemOne.setChecked(true);
                            }
                            if (1 == itemOne.getHaveHigherQuestion() && questionOne.getChooseItemIds().indexOf(itemOne.getId()) > -1) {
                                //选项是关联问题且选择了才执行
                                //有关联问题
                                queryQuestion = new QueryWrapper<>();
                                queryQuestion.lambda().eq(CorrectionQuestion::getRelevanceItemId, itemOne.getId());
                                queryQuestion.lambda().orderByAsc(CorrectionQuestion::getOrderIndex);
                                List<CorrectionQuestion> questionListTwoList = correctionQuestionService.list(queryQuestion);
                                for (CorrectionQuestion questionTwo : questionListTwoList) {
                                    questionTwo.setChooseItemIds(correctionAssesAnswerMapper.get(correctionAssesPersonDtl.getId(), questionTwo.getId()));
                                    QueryWrapper<CorrectionQuestionItem> queryQuestionItemTwo = new QueryWrapper<>();
                                    queryQuestionItemTwo.lambda().eq(CorrectionQuestionItem::getQuestionId, questionTwo.getId());
                                    queryQuestionItemTwo.lambda().orderByAsc(CorrectionQuestionItem::getOrderIndex);
                                    List<CorrectionQuestionItem> itemTwoList = correctionQuestionItemService.list(queryQuestionItemTwo);
                                    for (CorrectionQuestionItem itemTwo : itemTwoList) {
                                        if (questionTwo.getChooseItemIds().indexOf(itemTwo.getId()) > -1) {
                                            itemTwo.setChecked(true);
                                        }
                                    }
                                    questionTwo.setQuestionItemList(itemTwoList);
                                }
                                itemOne.setQuestionList(questionListTwoList);
                            }
                        }
                        questionOne.setQuestionItemList(itemOneList);
                    }
                    questionItem.setQuestionList(questionListOne);
                }
            }
            question.setQuestionItemList(itemList);
        }
        // 试题&答案赋值给问卷
        correctionAssesPersonDtl.setQuestionList(questionList);
        return correctionAssesPersonDtl;
    }
}
