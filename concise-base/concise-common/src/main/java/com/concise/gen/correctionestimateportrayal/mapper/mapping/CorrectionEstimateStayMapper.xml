<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.correctionestimateportrayal.mapper.CorrectionEstimatePortrayalMapper">

    <select id="getCorrectionObjectId" parameterType="String" resultType="String">
        SELECT id
        FROM ccgf0.ccgf_correction_object_basic
        WHERE correction_aid = #{sqjzryId}
          and del_flag = 0
          and status = 200
    </select>

    <select id="getNums" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM ccgf0.ccgf_abnormal_handle
        WHERE correction_object_id = #{correctionObjectId}
          AND date_format(alert_time, '%Y%m') = #{estimateMonth}
          and handle_result &lt; 205
        UNION ALL
        SELECT COUNT(*)
        FROM sqjzzxsjk0.correction_xj
        WHERE pid = #{pid}
          AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT count(*)
        FROM sqjzzxsjk0.correction_warning
        WHERE pid = #{pid}
          AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT count(*)
        FROM sqjzzxsjk0.correction_penalty
        WHERE pid = #{pid}
          AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT count(*)
        FROM sqjzzxsjk0.correction_arrest
        WHERE pid = #{pid}
          AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT count(*)
        FROM sqjzzxsjk0.correction_probation
        WHERE pid = #{pid}
          AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT count(*)
        FROM sqjzzxsjk0.correction_parole
        WHERE pid = #{pid}
          AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT count(*)
        FROM sqjzzxsjk0.correction_prison
        WHERE pid = #{pid}
          AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT COUNT(*)
        FROM ccgf0.correction_analyse_workplace
        WHERE correction_object_id = #{correctionObjectId}
          AND time = #{estimateMonth}
        UNION ALL
        SELECT COUNT(*)
        FROM ccgf0.correction_night_out
        WHERE correction_object_id = #{correctionObjectId}
          AND date_format(time, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT COUNT(*)
        FROM sqjzzxsjk0.correction_praise
        WHERE pid = #{pid}
          AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT COUNT(*)
        FROM sqjzzxsjk0.correction_commutation
        WHERE pid = #{pid}
          AND date_format(sfssqsj, '%Y%m') = #{estimateMonth}
        UNION ALL
        SELECT COUNT(*)
        FROM sqjzzxsjk0.correct_move_person
        WHERE sqjzry_id = #{pid}
    </select>

    <select id="getNumsAll" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM ccgf0.ccgf_abnormal_handle
        WHERE correction_object_id = #{correctionObjectId}
          and handle_result &lt; 205
        UNION ALL
        SELECT COUNT(*)
        FROM sqjzzxsjk0.correction_xj
        WHERE pid = #{pid}
        UNION ALL
        SELECT count(*)
        FROM sqjzzxsjk0.correction_warning
        WHERE pid = #{pid}
        UNION ALL
        SELECT count(*)
        FROM sqjzzxsjk0.correction_penalty
        WHERE pid = #{pid}
        UNION ALL
        SELECT count(*)
        FROM sqjzzxsjk0.correction_arrest
        WHERE pid = #{pid}
        UNION ALL
        SELECT count(*)
        FROM sqjzzxsjk0.correction_probation
        WHERE pid = #{pid}
        UNION ALL
        SELECT count(*)
        FROM sqjzzxsjk0.correction_parole
        WHERE pid = #{pid}
        UNION ALL
        SELECT count(*)
        FROM sqjzzxsjk0.correction_prison
        WHERE pid = #{pid}
        UNION ALL
        SELECT COUNT(*)
        FROM ccgf0.correction_analyse_workplace
        WHERE correction_object_id = #{correctionObjectId}
        UNION ALL
        SELECT COUNT(*)
        FROM ccgf0.correction_night_out
        WHERE correction_object_id = #{correctionObjectId}
        UNION ALL
        SELECT COUNT(*)
        FROM sqjzzxsjk0.correction_praise
        WHERE pid = #{pid}
        UNION ALL
        SELECT COUNT(*)
        FROM sqjzzxsjk0.correction_commutation
        WHERE pid = #{pid}
    </select>

    <select id="getMoonCode" resultType="String">
        SELECT mood_code
        FROM sqjzzxsjk0.correct_mood_code
        WHERE third_id = #{thirdId}
        ORDER BY update_time DESC LIMIT 1
    </select>

    <select id="getGljb" resultType="String">
        SELECT gljb
        FROM sqjzzxsjk0.correction_level
        WHERE pid = #{pid}
          AND del_flag = 0
          AND date_format(bdrq, '%Y%m') = #{estimateMonth}
          and gljb = '1' LIMIT 1
    </select>

    <select id="portrayalGjqs"
            resultType="com.concise.gen.correctionestimateportrayal.entity.vo.PortrayalGjqsVO">
        select date_table.lastDays       as monthValue,
               IFNULL(temp.xlhxScore, 0) as xlhxScore,
               IFNULL(temp.zfhxScore, 0) as zfhxScore,
               IFNULL(temp.jyhxScore, 0) as jyhxScore,
               IFNULL(temp.jthxScore, 0) as jthxScore,
               IFNULL(temp.xyhxScore, 0) as xyhxScore,
               IFNULL(temp.jbhxScore, 0) as jbhxScore
        from (
                 SELECT date_format(@lastDay := last_day( date_add(@lastDay,interval 1 month ) ), '%Y%m') lastDays
                 from (SELECT @lastDay := date_add(curdate(),interval -6 month) from mysql.help_topic limit 6) a) date_table
                 left join(
            select DATE_FORMAT(sc.create_time, '%Y%m') as monthValue,
                   xlhx_score                          as xlhxScore,
                   zfhx_score                          as zfhxScore,
                   jyhx_score                          as jyhxScore,
                   jthx_score                          as jthxScore,
                   xyhx_score                          as xyhxScore,
                   jbhx_score                          as jbhxScore
            from correction_estimate_portrayal sc
            WHERE id = #{id}
        ) temp on temp.monthValue = date_table.lastDays
        ORDER BY monthValue
    </select>


</mapper>
