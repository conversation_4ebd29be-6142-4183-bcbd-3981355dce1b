package com.concise.gen.correctionportrayalassignment.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 画像首页待处理任务参数类
 *
 * <AUTHOR>
 * @date 2023-01-09 09:14:33
*/
@Data
public class CorrectionPortrayalAssignmentParam extends BaseParam {

    /**
     * 
     */
    @NotNull(message = "不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;


    /**
     * 系统类型
     */
    @NotBlank(message = "系统类型不能为空，systemType", groups = {add.class, edit.class})
    private String systemType;


    /**
     * 系统类型名称
     */
    @NotBlank(message = "系统类型名称不能为空，请检查systemName参数", groups = {add.class, edit.class})
    private String systemName;

    /**
     * 处理类型
     */
    @NotBlank(message = "处理类型不能为空，请检查modelName参数", groups = {add.class, edit.class})
    private String modelName;

    /**
     * 跳转路径
     */
    @NotBlank(message = "跳转路径不能为空，请检查modelName参数", groups = {add.class, edit.class})
    private String modelUri;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 处理标志（0处理、1已处理）
     */
    @NotNull(message = "处理标志（0处理、1已处理）不能为空，请检查status参数", groups = {add.class, edit.class})
    private Integer status;

}
