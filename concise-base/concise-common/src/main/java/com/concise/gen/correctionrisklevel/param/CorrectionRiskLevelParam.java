package com.concise.gen.correctionrisklevel.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
* 风险等级参数类
 *
 * <AUTHOR>
 * @date 2022-05-18 18:29:16
*/
@Data
public class CorrectionRiskLevelParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 评分月度
     */
    private Integer scoringTime;

    /**
     * 评分模型ID
     */
    private String scoringModelId;

    /**
     * 矫正对象id
     */
    private String sqjzryId;

    /**
     * 姓名
     */
    private String sqjzryName;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 矫正机构ID
     */
    private String jzjg;

    /**
     * 矫正机构名称
     */
    private String jzjgName;

    /**
     * 基础分
     */
    private BigDecimal scoreBase;

    /**
     * 加分项
     */
    private BigDecimal scoreBonus;

    /**
     * 评估分(当月)
     */
    private BigDecimal scoreEstimate;

    /**
     * 风险等级(当月)
     */
    private String riskLevel;

    /**
     * 评估总分(矫正期)
     */
    private BigDecimal scoreEstimateAverage;

    /**
     * 风险等级(矫正期)
     */
    private String riskLevelAverage;

    /**
     * 日常监管_实时监控情况
     */
    private String dailySupervision;

    /**
     * 处罚_实时监控情况
     */
    private String punish;

    /**
     * 行为动态_实时监控情况
     */
    private String actionTrends;

    /**
     * 心理动态_实时监控情况
     */
    private String psychicAction;

    /**
     * 入矫评估_得分占比
     */
    private String estimateEnterPercent;

    /**
     * 日常监管_得分占比
     */
    private String dailySupervisionPercent;

    /**
     * 处罚_得分占比
     */
    private String punishPercent;

    /**
     * 行为动态_得分占比
     */
    private String actionTrendsPercent;

    /**
     * 心理状态_得分占比
     */
    private String psychicActionPercent;

    /**
     * 日常监管_普管降严管
     */
    private String dailySupervisionDetail;

    /**
     * 日常监管_信息化违规次数
     */
    private Integer yqViolate;

    /**
     * 处罚_训诫_次
     */
    private Integer advise;

    /**
     * 处罚_警告_次
     */
    private Integer warn;

    /**
     * 处罚_治安处罚_次
     */
    private Integer publicSecurity;

    /**
     * 处罚_提请逮捕_次
     */
    private Integer askArrest;

    /**
     * 处罚_提请撤缓_次
     */
    private Integer cancelProbation;

    /**
     * 处罚_提请撤销假释_次
     */
    private Integer cancelParole;

    /**
     * 处罚_提请收监执行_次
     */
    private Integer committedToPrison;

    /**
     * 处罚_行政处罚_次
     */
    private Integer xzPunish;

    /**
     * 行为动态_工作变动_次
     */
    private Integer workChange;

    /**
     * 行为动态_夜不归宿_次
     */
    private Integer nightOut;

    /**
     * 心理状态
     */
    private String psychicStatus;

    /**
     * 总结分析
     */
    private String summary;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 日常监管得分
     */
    private BigDecimal rcjgScore;

    /**
     * 处罚得分
     */
    private BigDecimal cfScore;

    /**
     * 行为动态得分
     */
    private BigDecimal xwdtScore;

    /**
     * 心理状态得分
     */
    private BigDecimal xlztScore;

}
