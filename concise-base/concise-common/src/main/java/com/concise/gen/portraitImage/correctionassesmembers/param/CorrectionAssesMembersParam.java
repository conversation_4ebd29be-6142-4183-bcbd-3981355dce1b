package com.concise.gen.portraitImage.correctionassesmembers.param;

import com.concise.common.pojo.base.param.BaseParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 评估管理--人员机构信息(只用于评估未开始前的查看)参数类
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:41
*/
@Data
public class CorrectionAssesMembersParam extends BaseParam {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 评估管理ID
     */
    @ApiModelProperty(value = "评估管理ID")
    private String baseId;

    /**
     * 类型：0：机构 1: 人员
     */
    @ApiModelProperty(value = "类型：0：机构 1: 人员")
    private Integer type;

    /**
     * 人员或机构id
     */
    @ApiModelProperty(value = "人员或机构id")
    private String memberId;

    /**
     * 人员或机构名称
     */
    @ApiModelProperty(value = "人员或机构名称")
    private String name;

}
