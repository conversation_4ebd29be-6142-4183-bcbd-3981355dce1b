package com.concise.gen.examquestion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.util.ExcelUtils;
import com.concise.gen.examquestion.entity.ExamQuestion;
import com.concise.gen.examquestion.enums.ExamQuestionExceptionEnum;
import com.concise.gen.examquestion.mapper.ExamQuestionMapper;
import com.concise.gen.examquestion.param.ExamQuestionParam;
import com.concise.gen.examquestion.service.ExamQuestionService;
import com.concise.gen.examquestionitem.entity.ExamQuestionItem;
import com.concise.gen.examquestionitem.service.ExamQuestionItemService;
import com.concise.gen.questioncategory.entity.QuestionCategory;
import com.concise.gen.questioncategory.service.QuestionCategoryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 考试题目service接口实现类
 *
 * <AUTHOR>
 * @date 2023-01-03 15:52:58
 */
@Service
public class ExamQuestionServiceImpl extends ServiceImpl<ExamQuestionMapper, ExamQuestion> implements ExamQuestionService {

    @Resource
    private QuestionCategoryService questionCategoryService;
    @Resource
    private ExamQuestionItemService examQuestionItemService;

    @Override
    public PageResult<ExamQuestion> page(ExamQuestionParam examQuestionParam) {
        QueryWrapper<ExamQuestion> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(ExamQuestion::getCreateTime);
        if (ObjectUtil.isNotNull(examQuestionParam)) {

            // 根据 查询
            if (ObjectUtil.isNotEmpty(examQuestionParam.getTypeId())) {
                queryWrapper.lambda().eq(ExamQuestion::getTypeId, examQuestionParam.getTypeId());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(examQuestionParam.getTypeName())) {
                queryWrapper.lambda().eq(ExamQuestion::getTypeName, examQuestionParam.getTypeName());
            }
            // 根据题型 查询
            if (ObjectUtil.isNotEmpty(examQuestionParam.getQuestionType())) {
                queryWrapper.lambda().eq(ExamQuestion::getQuestionType, examQuestionParam.getQuestionType());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(examQuestionParam.getQuestionTypeName())) {
                queryWrapper.lambda().eq(ExamQuestion::getQuestionTypeName, examQuestionParam.getQuestionTypeName());
            }
            // 根据排序字段 查询
            if (ObjectUtil.isNotEmpty(examQuestionParam.getSort())) {
                queryWrapper.lambda().eq(ExamQuestion::getSort, examQuestionParam.getSort());
            }
            // 根据题干（长度不够改为text） 查询
            if (ObjectUtil.isNotEmpty(examQuestionParam.getStem())) {
                queryWrapper.lambda().like(ExamQuestion::getStem, examQuestionParam.getStem());
            }
            // 根据答案（多选题用逗号隔开） 查询
            if (ObjectUtil.isNotEmpty(examQuestionParam.getAnswer())) {
                queryWrapper.lambda().eq(ExamQuestion::getAnswer, examQuestionParam.getAnswer());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(examQuestionParam.getCreateBy())) {
                queryWrapper.lambda().eq(ExamQuestion::getCreateBy, examQuestionParam.getCreateBy());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(examQuestionParam.getUpdateBy())) {
                queryWrapper.lambda().eq(ExamQuestion::getUpdateBy, examQuestionParam.getUpdateBy());
            }
            // 根据创建所属人 查询
            if (ObjectUtil.isNotEmpty(examQuestionParam.getCreateDepts())) {
                queryWrapper.lambda().eq(ExamQuestion::getCreateDepts, examQuestionParam.getCreateDepts());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<ExamQuestion> list(ExamQuestionParam examQuestionParam) {
        return this.list(new QueryWrapper<ExamQuestion>().lambda().eq(ExamQuestion::getTypeId, examQuestionParam.getTypeId()).notLike(ExamQuestion::getSort, "-"));
    }

    @Override
    public void add(ExamQuestion examQuestion) {
        this.save(examQuestion);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(ExamQuestionParam examQuestionParam) {
        this.removeById(examQuestionParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ExamQuestion examQuestion, String name) {
        editExamQuestion(examQuestion, name);
    }

    private void editExamQuestion(ExamQuestion examQuestion, String name) {
        this.updateById(examQuestion);
        //编辑选项
        List<ExamQuestionItem> examQuestionItemList = examQuestion.getExamQuestionItemList();
        if (CollectionUtil.isNotEmpty(examQuestionItemList)) {
            for (int i = 0; i < examQuestionItemList.size(); i++) {
                ExamQuestionItem examQuestionItem = examQuestionItemList.get(i);
                examQuestionItem.setItem("选项" + Convert.numberToChinese(i + 1, false));
                examQuestionItem.setSort(i + 1);
                examQuestionItem.setQuestionId(examQuestion.getId());
            }
            examQuestionItemService.remove(new QueryWrapper<ExamQuestionItem>().lambda().eq(ExamQuestionItem::getQuestionId, examQuestion.getId()));
            examQuestionItemService.saveBatch(examQuestionItemList);
            List<ExamQuestion> examQuestionList = new ArrayList<>();
            for (ExamQuestionItem examQuestionItem : examQuestionItemList) {
                List<ExamQuestion> questionList = examQuestionItem.getExamQuestion();
                if (CollectionUtil.isNotEmpty(questionList)) {
                    for (ExamQuestion question : questionList) {
                        QuestionCategory category = questionCategoryService.getById(question.getTypeId());
                        question.setTypeName(category.getTypeName());
                        if ("1".equals(question.getQuestionType())) {
                            question.setQuestionTypeName("单选题");
                        }
                        if ("2".equals(question.getQuestionType())) {
                            question.setQuestionTypeName("多选题");
                        }
                        /*if (StringUtils.isEmpty(question.getId())){
                            question.setId(IdWorker.getIdStr());
                        }*/
                        question.setCreateBy(name);
                        question.setCreateTime(DateUtil.date());
                    }
                    examQuestionList.addAll(questionList);

                }
            }
            if (CollectionUtil.isNotEmpty(examQuestionList)) {
                List<ExamQuestion> list = examQuestionList.stream().distinct().collect(Collectors.toList());
                this.saveOrUpdateBatch(list);
                for (ExamQuestion question : list) {
                    editExamQuestion(question, name);
                }
            }

        }

    }

    @Override
    public ExamQuestion detail(ExamQuestionParam examQuestionParam) {
        return this.queryExamQuestion(examQuestionParam);
    }

    /**
     * 获取考试题目
     *
     * <AUTHOR>
     * @date 2023-01-03 15:52:58
     */
    private ExamQuestion queryExamQuestion(ExamQuestionParam examQuestionParam) {
        ExamQuestion examQuestion = this.getById(examQuestionParam.getId());
        if (ObjectUtil.isNull(examQuestion)) {
            throw new ServiceException(ExamQuestionExceptionEnum.NOT_EXIST);
        }
        return examQuestion;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseData importExcel(File file, String name) {
        List<QuestionCategory> questionCategoryList = questionCategoryService.list(new QueryWrapper<QuestionCategory>().lambda().eq(QuestionCategory::getIsDel, "0"));
        Set<String> paperNameSet = questionCategoryList.stream().map(QuestionCategory::getTypeName).collect(Collectors.toSet());
        Map<String, String> questionCategoryMap = new HashMap<>();
        questionCategoryList.forEach(e -> {
            questionCategoryMap.put(e.getTypeName(), e.getId());
        });

        List<ExamQuestion> examQuestionList = new ArrayList<>();
        List<ExamQuestionItem> examQuestionItemList = new ArrayList<>();

        List<ArrayList<String>> list = ExcelUtils.readXlsx(file);
        String a = list.get(0).get(0);
        if (ObjectUtil.isEmpty(a) || !"测试题库导入模板".equals(a)) {
            return ResponseData.error("请导入正确的模板!");
        }
        for (int i = 3; i < list.size(); i++) {
            int k = i + 1;
            ExamQuestion examQuestion = new ExamQuestion();
            String idStr = IdWorker.getIdStr();
            examQuestion.setId(idStr);
            ArrayList<String> strings = list.get(i);
            //题库分类不能为空，且需要已经建好的题库
            if (StringUtils.isEmpty(strings.get(0)) || !paperNameSet.contains(strings.get(0))) {
                return ResponseData.error("第" + k + "行题库分类为空或者题库分类不存在！");
            }
            examQuestion.setTypeName(strings.get(0));
            examQuestion.setTypeId(questionCategoryMap.get(strings.get(0)));
            //序号不能重复
            int count = this.count(new QueryWrapper<ExamQuestion>().lambda().eq(ExamQuestion::getTypeName, strings.get(0)).eq(ExamQuestion::getSort, strings.get(1)));
            List<ExamQuestion> sortList = examQuestionList.stream().filter(e -> e.getTypeName().equals(strings.get(0))).filter(e -> e.getSort().equals(strings.get(1))).collect(Collectors.toList());
            if (count > 0 || CollectionUtil.isNotEmpty(sortList)) {
                return ResponseData.error("第" + k + "行序号重复！");
            }
            examQuestion.setSort(strings.get(1));
            //题型限制单选和多选
            if (StringUtils.isEmpty(strings.get(2))) {
                return ResponseData.error("第" + k + "行题型必填！");
            }
            if (!"单选题".equals(strings.get(2)) && !"多选题".equals(strings.get(2))) {
                return ResponseData.error("第" + k + "行题型必须为单选题或者多选题！");
            }
            if ("单选题".equals(strings.get(2))) {
                examQuestion.setQuestionType("1");
            } else {
                examQuestion.setQuestionType("2");
            }
            examQuestion.setQuestionTypeName(strings.get(2));
            //题干不能重复
            int term = this.count(new QueryWrapper<ExamQuestion>().lambda().eq(ExamQuestion::getTypeName, strings.get(0)).eq(ExamQuestion::getStem, strings.get(3)));
            List<ExamQuestion> termList = examQuestionList.stream().filter(e -> e.getTypeName().equals(strings.get(0))).filter(e -> e.getStem().equals(strings.get(3))).collect(Collectors.toList());
            if (term > 0 || CollectionUtil.isNotEmpty(termList)) {
                return ResponseData.error("第" + k + "行题干重复！");
            }
            examQuestion.setStem(strings.get(3));
            //处理选项
            for (int j = 5; j < strings.size(); j++) {
                if (StringUtils.isNotEmpty(strings.get(j))) {
                    ExamQuestionItem examQuestionItem = new ExamQuestionItem();
                    examQuestionItem.setId(IdWorker.getIdStr());
                    examQuestionItem.setQuestionId(idStr);
                    examQuestionItem.setContent(strings.get(j));
                    examQuestionItem.setSort(j - 4);
                    examQuestionItem.setItem("选项" + Convert.numberToChinese(j - 4, false));
                    //如果是子级，找到父级
                    String parentSelection = strings.get(4);
                    if (StringUtils.isNotEmpty(parentSelection)) {
                        String sort = strings.get(1);
                        if (!sort.contains("-")) {
                            return ResponseData.error("第" + k + "行子集题目无法找到父级测试题！");
                        }
                        int idx = sort.lastIndexOf("-");
                        String psort = sort.substring(0, idx);
                        ExamQuestion question = this.getOne(new QueryWrapper<ExamQuestion>().lambda().eq(ExamQuestion::getTypeName, strings.get(0)).eq(ExamQuestion::getSort, psort));
                        String[] selections = parentSelection.split("、");
                        //已经在库的题作为父级
                        if (ObjectUtil.isNotEmpty(question)) {
                            List<ExamQuestionItem> questionItemList = examQuestionItemService.list(new QueryWrapper<ExamQuestionItem>().lambda().eq(ExamQuestionItem::getQuestionId, question.getId()).orderByAsc(ExamQuestionItem::getSort));
                            for (String selection : selections) {
                                for (ExamQuestionItem questionItem : questionItemList) {
                                    if (questionItem.getItem().equals(selection)) {
                                        if (StringUtils.isNotEmpty(examQuestionItem.getPid())) {
                                            examQuestionItem.setPid(examQuestionItem.getPid() + "," + questionItem.getId());
                                        } else {
                                            examQuestionItem.setPid(questionItem.getId());
                                        }
                                    }
                                }
                            }
                        } else {
                            //本次导入的题作为父级
                            List<ExamQuestion> collect = examQuestionList.stream().filter(e -> e.getTypeName().equals(strings.get(0))).filter(e -> e.getSort().equals(psort)).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(collect)) {
                                List<ExamQuestionItem> questionItemList = examQuestionItemList.stream().filter(e -> e.getQuestionId().equals(collect.get(0).getId())).collect(Collectors.toList());
                                for (String selection : selections) {
                                    for (ExamQuestionItem questionItem : questionItemList) {
                                        if (questionItem.getItem().equals(selection)) {
                                            if (StringUtils.isNotEmpty(examQuestionItem.getPid())) {
                                                examQuestionItem.setPid(examQuestionItem.getPid() + "," + questionItem.getId());
                                            } else {
                                                examQuestionItem.setPid(questionItem.getId());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    examQuestionItemList.add(examQuestionItem);

                }
            }

            examQuestion.setCreateBy(name);
            examQuestion.setCreateTime(DateUtil.date());
            examQuestionList.add(examQuestion);

        }
        this.saveBatch(examQuestionList);
        examQuestionItemService.saveBatch(examQuestionItemList);

        return ResponseData.success("导入成功！");
    }

    @Override
    public List<ExamQuestion> questionTree(ExamQuestionParam examQuestionParam) {
        List<ExamQuestionItem> list = examQuestionItemService.list(new QueryWrapper<ExamQuestionItem>().lambda().isNotNull(ExamQuestionItem::getPid));
        List<ExamQuestion> questionList = this.list(examQuestionParam);
        if (CollectionUtil.isNotEmpty(questionList)) {
            for (ExamQuestion examQuestion : questionList) {
                buildChildren(examQuestion, list);
            }
        }
        return questionList;
    }

    /**
     * 递归找子集
     *
     * @param examQuestion
     * @param list
     */
    @Override
    public void buildChildren(ExamQuestion examQuestion, List<ExamQuestionItem> list) {
        //找本级题目选项
        List<ExamQuestionItem> examQuestionItemList = examQuestionItemService.list(new QueryWrapper<ExamQuestionItem>().lambda().eq(ExamQuestionItem::getQuestionId, examQuestion.getId()));
        if (CollectionUtil.isNotEmpty(examQuestionItemList)) {
            examQuestion.setExamQuestionItemList(examQuestionItemList);
            //遍历找选项关联子题目选项
            for (ExamQuestionItem examQuestionItem : examQuestionItemList) {
                if (CollectionUtil.isNotEmpty(list)) {
                    List<ExamQuestionItem> itemList = list.stream().filter(e -> e.getPid().contains(examQuestionItem.getId())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(itemList)) {
                        Set<String> questionIds = itemList.stream().map(ExamQuestionItem::getQuestionId).collect(Collectors.toSet());
                        List<ExamQuestion> questionList = this.list(new QueryWrapper<ExamQuestion>().lambda().in(ExamQuestion::getId, questionIds));
                        examQuestionItem.setExamQuestion(questionList);
                        for (ExamQuestion question : questionList) {
                            buildChildren(question, list);
                        }

                    }
                }
            }
        }
    }

    @Override
    public ExamQuestion getDetails(ExamQuestionParam examQuestionParam) {
        List<ExamQuestionItem> list = examQuestionItemService.list(new QueryWrapper<ExamQuestionItem>().lambda().isNotNull(ExamQuestionItem::getPid));
        ExamQuestion examQuestion = this.getById(examQuestionParam.getId());
        if (examQuestion.getSort().contains("-")){
            String sort=examQuestion.getSort().substring(0,examQuestion.getSort().lastIndexOf("-"));
            ExamQuestion question = this.getOne(new QueryWrapper<ExamQuestion>().lambda().eq(ExamQuestion::getTypeId, examQuestion.getTypeId()).eq(ExamQuestion::getSort, sort));
            if (ObjectUtil.isNotEmpty(question)){
                buildChildren(question,list);
                List<ExamQuestionItem> examQuestionItemList = question.getExamQuestionItemList();
                if (CollectionUtil.isNotEmpty(examQuestionItemList)){
                    for (ExamQuestionItem questionItem : examQuestionItemList) {
                        List<ExamQuestion> questionList = questionItem.getExamQuestion();
                        if (CollectionUtil.isNotEmpty(questionList)){
                            for (ExamQuestion questionOne : questionList) {
                                List<ExamQuestionItem> examQuestionItemList1 = questionOne.getExamQuestionItemList();
                                for (ExamQuestionItem examQuestionItem : examQuestionItemList1) {
                                    if (examQuestionItem.getPid().contains(questionItem.getId())){
                                        questionItem.setChecked(true);
                                    }
                                }
                            }
                        }

                    }
                }
                examQuestion.setParentQuestion(question);
            }
        }
        buildChildren(examQuestion, list);
        return examQuestion;
    }

    @Override
    public List<ExamQuestion> examPaperTree(ExamQuestionParam examQuestionParam) {
        List<ExamQuestion> list = this.list(examQuestionParam);
        if (CollectionUtil.isNotEmpty(list)){
            for (ExamQuestion examQuestion : list) {
                List<ExamQuestion> questionList = this.list(new QueryWrapper<ExamQuestion>().lambda().eq(ExamQuestion::getTypeId, examQuestion.getTypeId()).likeRight(ExamQuestion::getSort, examQuestion.getSort() + "-").orderByAsc(ExamQuestion::getSort));
                if (CollectionUtil.isNotEmpty(questionList)){
                    for (ExamQuestion question : questionList) {
                        question.setPid(examQuestion.getId());
                    }
                    examQuestion.setChildQuestion(questionList);
                }
            }
        }
        return list;
    }

    public void saveQuestion(){
        ExamQuestion examQuestion = new ExamQuestion();
        examQuestion.setTypeId("1621439372795666433");
        examQuestion.setTypeName("CT的分类");
        examQuestion.setQuestionType("1");
        examQuestion.setQuestionTypeName("单选题");
        examQuestion.setSort("20");
        examQuestion.setStem("您现在的收入来源是？");
        this.save(examQuestion);
        ExamQuestionItem one = new ExamQuestionItem();
        ExamQuestionItem two = new ExamQuestionItem();
        ExamQuestionItem three = new ExamQuestionItem();
        ExamQuestionItem four = new ExamQuestionItem();
        ExamQuestionItem five = new ExamQuestionItem();
        one.setQuestionId(examQuestion.getId());
        two.setQuestionId(examQuestion.getId());
        three.setQuestionId(examQuestion.getId());
        four.setQuestionId(examQuestion.getId());
        five.setQuestionId(examQuestion.getId());
        one.setContent("工作收入");
        two.setContent("房租收入");
        three.setContent("投资收入");
        four.setContent("收入来自家庭成员");
        five.setContent("低保户");
        one.setSort(1);
        two.setSort(2);
        three.setSort(3);
        four.setSort(4);
        five.setSort(5);
        one.setItem("选项一");
        two.setItem("选项二");
        three.setItem("选项三");
        four.setItem("选项四");
        five.setItem("选项五");
        examQuestionItemService.save(one);
        examQuestionItemService.save(two);
        examQuestionItemService.save(three);
        examQuestionItemService.save(four);
        examQuestionItemService.save(five);



    }

    public void main(String[] args) {
        this.saveQuestion();
    }

}
