package com.concise.gen.correctioncaselable.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctioncaselable.entity.CorrectionCaseLable;
import com.concise.gen.correctioncaselable.enums.CorrectionCaseLableExceptionEnum;
import com.concise.gen.correctioncaselable.mapper.CorrectionCaseLableMapper;
import com.concise.gen.correctioncaselable.param.CorrectionCaseLableParam;
import com.concise.gen.correctioncaselable.service.CorrectionCaseLableService;
import com.concise.gen.correctionlabelcorrpsn.entity.CorrectionLabelCorrpsn;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 案例与标签关联表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-03-14 17:18:07
 */
@Service
public class CorrectionCaseLableServiceImpl extends ServiceImpl<CorrectionCaseLableMapper, CorrectionCaseLable> implements CorrectionCaseLableService {

    @Override
    public PageResult<CorrectionCaseLable> page(CorrectionCaseLableParam correctionCaseLableParam) {
        QueryWrapper<CorrectionCaseLable> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionCaseLableParam)) {

            // 根据案例id 查询
            if (ObjectUtil.isNotEmpty(correctionCaseLableParam.getCaseId())) {
                queryWrapper.lambda().eq(CorrectionCaseLable::getCaseId, correctionCaseLableParam.getCaseId());
            }
            // 根据标签id 查询
            if (ObjectUtil.isNotEmpty(correctionCaseLableParam.getLableId())) {
                queryWrapper.lambda().eq(CorrectionCaseLable::getLableId, correctionCaseLableParam.getLableId());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionCaseLable> list(CorrectionCaseLableParam correctionCaseLableParam) {
        QueryWrapper<CorrectionCaseLable> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionCaseLableParam)) {

            // 根据案例id 查询
            if (ObjectUtil.isNotEmpty(correctionCaseLableParam.getCaseId())) {
                queryWrapper.lambda().eq(CorrectionCaseLable::getCaseId, correctionCaseLableParam.getCaseId());
            }
            // 根据标签id 查询
            if (ObjectUtil.isNotEmpty(correctionCaseLableParam.getLableId())) {
                queryWrapper.lambda().eq(CorrectionCaseLable::getLableId, correctionCaseLableParam.getLableId());
            }
        }
        return this.list(queryWrapper);
    }

    @Override
    @Transactional
    public void add(CorrectionCaseLableParam correctionCaseLableParam) {
        List<CorrectionCaseLable> list = new ArrayList<>();
        CorrectionCaseLable cc = null;
        for (String caseId : correctionCaseLableParam.getCaseIds()) {
            for (String labelId : correctionCaseLableParam.getLabelIds()) {
                // 先根据标签删除已绑定的标签
                this.remove(new QueryWrapper<CorrectionCaseLable>().lambda().eq(CorrectionCaseLable::getCaseId, caseId));

                    cc = new CorrectionCaseLable();
                    cc.setCaseId(Long.parseLong(caseId));
                    cc.setLableId(labelId);
                    list.add(cc);

            }
        }
        if (list.size() > 0) {
            this.saveBatch(list);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionCaseLableParam correctionCaseLableParam) {
        this.removeById(correctionCaseLableParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionCaseLableParam correctionCaseLableParam) {
        CorrectionCaseLable correctionCaseLable = this.queryCorrectionCaseLable(correctionCaseLableParam);
        BeanUtil.copyProperties(correctionCaseLableParam, correctionCaseLable);
        this.updateById(correctionCaseLable);
    }

    @Override
    public CorrectionCaseLable detail(CorrectionCaseLableParam correctionCaseLableParam) {
        return this.queryCorrectionCaseLable(correctionCaseLableParam);
    }

    /**
     * 获取案例与标签关联表
     *
     * <AUTHOR>
     * @date 2022-03-14 17:18:07
     */
    private CorrectionCaseLable queryCorrectionCaseLable(CorrectionCaseLableParam correctionCaseLableParam) {
        CorrectionCaseLable correctionCaseLable = this.getById(correctionCaseLableParam.getId());
        if (ObjectUtil.isNull(correctionCaseLable)) {
            throw new ServiceException(CorrectionCaseLableExceptionEnum.NOT_EXIST);
        }
        return correctionCaseLable;
    }
}
