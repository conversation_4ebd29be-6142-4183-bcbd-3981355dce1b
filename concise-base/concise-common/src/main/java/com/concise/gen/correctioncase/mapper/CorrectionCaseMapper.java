package com.concise.gen.correctioncase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.correctioncase.entity.CorrectionCase;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 案例库
 *
 * <AUTHOR>
 * @date 2022-03-14 17:18:03
 */
public interface CorrectionCaseMapper extends BaseMapper<CorrectionCase> {

    /**
     * 根据标签id查找案例
     * @param lableIds
     * @return
     */
    List<CorrectionCase> findByLabel(@Param("lableIds") Set<String> lableIds);
}
