package com.concise.gen.correctmoveperson.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctmoveperson.entity.CorrectMovePerson;
import com.concise.gen.correctmoveperson.param.CorrectMovePersonParam;
import java.util.List;
import java.util.Set;

/**
 * 流动人口service接口
 *
 * <AUTHOR>
 * @date 2022-05-24 16:51:04
 */
public interface CorrectMovePersonService extends IService<CorrectMovePerson> {

    /**
     * 查询流动人口
     *
     * <AUTHOR>
     * @date 2022-05-24 16:51:04
     */
    PageResult<CorrectMovePerson> page(CorrectMovePersonParam correctMovePersonParam, List<String> sqjzryIdList);

    /**
     * 流动人口列表
     *
     * <AUTHOR>
     * @date 2022-05-24 16:51:04
     */
    List<CorrectMovePerson> list(CorrectMovePersonParam correctMovePersonParam);

    /**
     * 添加流动人口
     *
     * <AUTHOR>
     * @date 2022-05-24 16:51:04
     */
    void add(CorrectMovePersonParam correctMovePersonParam);

    /**
     * 删除流动人口
     *
     * <AUTHOR>
     * @date 2022-05-24 16:51:04
     */
    void delete(CorrectMovePersonParam correctMovePersonParam);

    /**
     * 编辑流动人口
     *
     * <AUTHOR>
     * @date 2022-05-24 16:51:04
     */
    void edit(CorrectMovePersonParam correctMovePersonParam);

    /**
     * 查看流动人口
     *
     * <AUTHOR>
     * @date 2022-05-24 16:51:04
     */
     CorrectMovePerson detail(CorrectMovePersonParam correctMovePersonParam);
}
