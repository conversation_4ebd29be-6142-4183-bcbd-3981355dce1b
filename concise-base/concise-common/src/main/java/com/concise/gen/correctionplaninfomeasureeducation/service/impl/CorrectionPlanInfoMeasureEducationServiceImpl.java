package com.concise.gen.correctionplaninfomeasureeducation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionplaninfomeasureeducation.entity.CorrectionPlanInfoMeasureEducation;
import com.concise.gen.correctionplaninfomeasureeducation.enums.CorrectionPlanInfoMeasureEducationExceptionEnum;
import com.concise.gen.correctionplaninfomeasureeducation.mapper.CorrectionPlanInfoMeasureEducationMapper;
import com.concise.gen.correctionplaninfomeasureeducation.param.CorrectionPlanInfoMeasureEducationParam;
import com.concise.gen.correctionplaninfomeasureeducation.service.CorrectionPlanInfoMeasureEducationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 矫正方案2.0监管措施_教育帮扶每月课件分类学习记录service接口实现类
 *
 * <AUTHOR>
 * @date 2024-11-21 16:55:24
 */
@Service
public class CorrectionPlanInfoMeasureEducationServiceImpl extends ServiceImpl<CorrectionPlanInfoMeasureEducationMapper, CorrectionPlanInfoMeasureEducation> implements CorrectionPlanInfoMeasureEducationService {

    @Override
    public PageResult<CorrectionPlanInfoMeasureEducation> page(CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam) {
        QueryWrapper<CorrectionPlanInfoMeasureEducation> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionPlanInfoMeasureEducationParam)) {

            // 根据矫正对象id 查询
            if (ObjectUtil.isNotEmpty(correctionPlanInfoMeasureEducationParam.getJzdxId())) {
                queryWrapper.lambda().eq(CorrectionPlanInfoMeasureEducation::getJzdxId, correctionPlanInfoMeasureEducationParam.getJzdxId());
            }
            // 根据月份 查询
            if (ObjectUtil.isNotEmpty(correctionPlanInfoMeasureEducationParam.getMonth())) {
                queryWrapper.lambda().eq(CorrectionPlanInfoMeasureEducation::getMonth, correctionPlanInfoMeasureEducationParam.getMonth());
            }
            // 根据课件分类id 查询
            if (ObjectUtil.isNotEmpty(correctionPlanInfoMeasureEducationParam.getCategoryId())) {
                queryWrapper.lambda().eq(CorrectionPlanInfoMeasureEducation::getCategoryId, correctionPlanInfoMeasureEducationParam.getCategoryId());
            }
            // 根据课件分类 查询
            if (ObjectUtil.isNotEmpty(correctionPlanInfoMeasureEducationParam.getCategoryName())) {
                queryWrapper.lambda().eq(CorrectionPlanInfoMeasureEducation::getCategoryName, correctionPlanInfoMeasureEducationParam.getCategoryName());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionPlanInfoMeasureEducation> list(CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam) {
        CorrectionPlanInfoMeasureEducation correctionPlanInfoMeasureEducation = new CorrectionPlanInfoMeasureEducation();
        BeanUtil.copyProperties(correctionPlanInfoMeasureEducationParam, correctionPlanInfoMeasureEducation);
        this.save(correctionPlanInfoMeasureEducation);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam) {
        this.removeById(correctionPlanInfoMeasureEducationParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam) {
        CorrectionPlanInfoMeasureEducation correctionPlanInfoMeasureEducation = this.queryCorrectionPlanInfoMeasureEducation(correctionPlanInfoMeasureEducationParam);
        BeanUtil.copyProperties(correctionPlanInfoMeasureEducationParam, correctionPlanInfoMeasureEducation);
        this.updateById(correctionPlanInfoMeasureEducation);
    }

    @Override
    public CorrectionPlanInfoMeasureEducation detail(CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam) {
        return this.queryCorrectionPlanInfoMeasureEducation(correctionPlanInfoMeasureEducationParam);
    }

    /**
     * 获取矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
     *
     * <AUTHOR>
     * @date 2024-11-21 16:55:24
     */
    private CorrectionPlanInfoMeasureEducation queryCorrectionPlanInfoMeasureEducation(CorrectionPlanInfoMeasureEducationParam correctionPlanInfoMeasureEducationParam) {
        CorrectionPlanInfoMeasureEducation correctionPlanInfoMeasureEducation = this.getById(correctionPlanInfoMeasureEducationParam.getId());
        if (ObjectUtil.isNull(correctionPlanInfoMeasureEducation)) {
            throw new ServiceException(CorrectionPlanInfoMeasureEducationExceptionEnum.NOT_EXIST);
        }
        return correctionPlanInfoMeasureEducation;
    }
}
