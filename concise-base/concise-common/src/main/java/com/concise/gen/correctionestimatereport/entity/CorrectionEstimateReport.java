package com.concise.gen.correctionestimatereport.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 评估报告
 *
 * <AUTHOR>
 * @date 2023-01-09 10:57:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_estimate_report")
public class CorrectionEstimateReport extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 矫正对象id
     */
    private String sqjzryId;

    /**
     * 姓名
     */
    private String sqjzryName;

    /**
     *  性别
     */
    private String xb;

    /**
     * 性别中文值
     */
    private String xbName;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 是否成年
     */
    private String sfcn;

    /**
     * 婚姻状况
     */
    private String hyzk;

    /**
     * 婚姻状况中文值
     */
    private String hyzkName;

    /**
     * 文化程度
     */
    private String whcd;

    /**
     * 文化程度中文值
     */
    private String whcdName;

    /**
     * 矫正机构ID
     */
    private String jzjg;

    /**
     * 矫正机构名称
     */
    private String jzjgName;

    /**
     * 入娇评估分
     */
    private BigDecimal enterScore;

    /**
     * 上月评估分
     */
    private BigDecimal lastMonthScore;

    /**
     * 本月评估分
     */
    private BigDecimal scoreEstimate;

    /**
     * 评估月份
     */
    private Integer estimateMonth;

    /**
     * 风险等级
     */
    private String level;

    /**
     * 是否累犯
     */
    private String sflf;

    /**
     * 是否有前科
     */
    private String sfyqk;

    /**
     * 是否“五独”
     */
    private String sfwd;

    /**
     * 是否“五涉”
     */
    private String sfws;

    /**
     * 是否有“四史”
     */
    private String sfyss;

    /**
     * 矫正类别
     */
    private String jzlb;

    /**
     * 矫正类别中文值
     */
    private String jzlbName;

    /**
     * 是否成年中文值
     */
    private String sfcnName;

    /**
     * 就业就学情况
     */
    private String jyjxqk;

    /**
     * 就业就学情况中文值
     */
    private String jyjxqkName;

    /**
     * 具体罪名
     */
    private String jtzm;

    /**
     * 具体罪名中文值
     */
    private String jtzmName;

    /**
     * 基础总结
     */
    private String basicsConclusion;

    /**
     * 动态总结
     */
    private String dynamicConclusion;

    /**
     * 最终总结
     */
    private String finalConclusion;

    /**
     * 风险条目
     */
    private String risks;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

}
