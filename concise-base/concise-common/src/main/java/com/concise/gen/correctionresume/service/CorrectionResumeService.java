package com.concise.gen.correctionresume.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionresume.entity.CorrectionResume;
import com.concise.gen.correctionresume.param.CorrectionResumeParam;

import java.util.List;

/**
 * 个人简历service接口
 *
 * <AUTHOR>
 * @date 2022-02-22 16:07:54
 */
public interface CorrectionResumeService extends IService<CorrectionResume> {

    /**
     * 查询个人简历
     *
     * <AUTHOR>
     * @date 2022-02-22 16:07:54
     */
    PageResult<CorrectionResume> page(CorrectionResumeParam correctionResumeParam);

    /**
     * 个人简历列表
     *
     * <AUTHOR>
     * @date 2022-02-22 16:07:54
     */
    List<CorrectionResume> list(CorrectionResumeParam correctionResumeParam);
}
