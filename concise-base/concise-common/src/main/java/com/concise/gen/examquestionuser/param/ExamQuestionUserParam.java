package com.concise.gen.examquestionuser.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 用户答题表参数类
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:27
*/
@Data
public class ExamQuestionUserParam extends BaseParam {

    /**
     * 
     */
    @NotNull(message = "不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 
     */
    @NotBlank(message = "不能为空，请检查userId参数", groups = {add.class, edit.class})
    private String userId;

    /**
     * 
     */
    @NotBlank(message = "不能为空，请检查manageId参数", groups = {add.class, edit.class})
    private String manageId;

    /**
     * 
     */
    @NotBlank(message = "不能为空，请检查questionId参数", groups = {add.class, edit.class})
    private String questionId;

    /**
     * 
     */
    @NotBlank(message = "不能为空，请检查answer参数", groups = {add.class, edit.class})
    private String answer;

    /**
     * 分数
     */
    @NotNull(message = "分数不能为空，请检查score参数", groups = {add.class, edit.class})
    private Double score;

}
