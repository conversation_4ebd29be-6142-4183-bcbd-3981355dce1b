package com.concise.gen.correctionlabelsystem.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 标签所属应用
 *
 * <AUTHOR>
 * @date 2022-08-22 09:04:42
 */
@Data
@TableName("correction_label_system")
public class CorrectionLabelSystem {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 标签ID
     */
    private String labelId;

    /**
     * 所属应用,字典值: SSYY
     */
    private String sysCode;

}
