package com.concise.gen.correctionestimateportrayalparticulars.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimateportrayaldetail.entity.CorrectionEstimatePortrayalDetail;
import com.concise.gen.correctionestimateportrayaldetail.enums.CorrectionEstimatePortrayalDetailExceptionEnum;
import com.concise.gen.correctionestimateportrayalparticulars.entity.CorrectionEstimatePortrayalParticulars;
import com.concise.gen.correctionestimateportrayalparticulars.enums.CorrectionEstimatePortrayalParticularsExceptionEnum;
import com.concise.gen.correctionestimateportrayalparticulars.mapper.CorrectionEstimatePortrayalParticularsMapper;
import com.concise.gen.correctionestimateportrayalparticulars.param.CorrectionEstimatePortrayalParticularsParam;
import com.concise.gen.correctionestimateportrayalparticulars.service.CorrectionEstimatePortrayalParticularsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 心理画像明细（新）service接口实现类
 *
 * <AUTHOR>
 * @date 2023-01-14 10:38:47
 */
@Service
public class CorrectionEstimatePortrayalParticularsServiceImpl extends ServiceImpl<CorrectionEstimatePortrayalParticularsMapper, CorrectionEstimatePortrayalParticulars> implements CorrectionEstimatePortrayalParticularsService {

    @Override
    public PageResult<CorrectionEstimatePortrayalParticulars> page(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam) {
        QueryWrapper<CorrectionEstimatePortrayalParticulars> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionEstimatePortrayalParticularsParam)) {

            // 根据心理画像描述 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXlhxms())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXlhxms, correctionEstimatePortrayalParticularsParam.getXlhxms());
            }
            // 根据知法画像描述 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getZfhxms())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getZfhxms, correctionEstimatePortrayalParticularsParam.getZfhxms());
            }
            // 根据就业画像描述 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJyhxms())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJyhxms, correctionEstimatePortrayalParticularsParam.getJyhxms());
            }
            // 根据家庭画像描述 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJthxms())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJthxms, correctionEstimatePortrayalParticularsParam.getJthxms());
            }
            // 根据信用画像描述 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXyhxms())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXyhxms, correctionEstimatePortrayalParticularsParam.getXyhxms());
            }
            // 根据个人基本画像描述 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJbhxms())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJbhxms, correctionEstimatePortrayalParticularsParam.getJbhxms());
            }
            // 根据心理画像建议 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXlhxjy())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXlhxjy, correctionEstimatePortrayalParticularsParam.getXlhxjy());
            }
            // 根据知法画像建议 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getZfhxjy())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getZfhxjy, correctionEstimatePortrayalParticularsParam.getZfhxjy());
            }
            // 根据就业画像建议 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJyhxjy())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJyhxjy, correctionEstimatePortrayalParticularsParam.getJyhxjy());
            }
            // 根据家庭画像建议 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJthxjy())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJthxjy, correctionEstimatePortrayalParticularsParam.getJthxjy());
            }
            // 根据信用画像建议 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXyhxjy())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXyhxjy, correctionEstimatePortrayalParticularsParam.getXyhxjy());
            }
            // 根据个人基本画像建议 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJbhxjy())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJbhxjy, correctionEstimatePortrayalParticularsParam.getJbhxjy());
            }
            // 根据心理画像扣分明细 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXlhxkfmx())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXlhxkfmx, correctionEstimatePortrayalParticularsParam.getXlhxkfmx());
            }
            // 根据知法画像扣分明细 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getZfhxkfmx())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getZfhxkfmx, correctionEstimatePortrayalParticularsParam.getZfhxkfmx());
            }
            // 根据就业画像扣分明细 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJyhxkfmx())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJyhxkfmx, correctionEstimatePortrayalParticularsParam.getJyhxkfmx());
            }
            // 根据家庭画像扣分明细 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJthxkfmx())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJthxkfmx, correctionEstimatePortrayalParticularsParam.getJthxkfmx());
            }
            // 根据信用画像扣分明细 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXyhxkfmx())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXyhxkfmx, correctionEstimatePortrayalParticularsParam.getXyhxkfmx());
            }
            // 根据个人基本画像扣分明细 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJbhxkfmx())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJbhxkfmx, correctionEstimatePortrayalParticularsParam.getJbhxkfmx());
            }
            // 根据心理画像轨迹风险分析 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXlhxgjfx())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXlhxgjfx, correctionEstimatePortrayalParticularsParam.getXlhxgjfx());
            }
            // 根据知法画像轨迹风险分析 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getZfhxgjfx())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getZfhxgjfx, correctionEstimatePortrayalParticularsParam.getZfhxgjfx());
            }
            // 根据就业画像轨迹风险分析 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJyhxgjfx())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJyhxgjfx, correctionEstimatePortrayalParticularsParam.getJyhxgjfx());
            }
            // 根据家庭画像轨迹风险分析 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJthxgjfx())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJthxgjfx, correctionEstimatePortrayalParticularsParam.getJthxgjfx());
            }
            // 根据信用画像轨迹风险分析 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXyhxgjfx())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXyhxgjfx, correctionEstimatePortrayalParticularsParam.getXyhxgjfx());
            }
            // 根据个人基本画像轨迹风险分析 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJbhxgjfx())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJbhxgjfx, correctionEstimatePortrayalParticularsParam.getJbhxgjfx());
            }
            // 根据心理画像风险等级 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXlhxLevel())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXlhxLevel, correctionEstimatePortrayalParticularsParam.getXlhxLevel());
            }
            // 根据知法画像风险等级 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getZfhxLevel())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getZfhxLevel, correctionEstimatePortrayalParticularsParam.getZfhxLevel());
            }
            // 根据就业画像风险等级 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJyhxLevel())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJyhxLevel, correctionEstimatePortrayalParticularsParam.getJyhxLevel());
            }
            // 根据家庭画像风险等级 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJthxLevel())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJthxLevel, correctionEstimatePortrayalParticularsParam.getJthxLevel());
            }
            // 根据信用画像风险等级 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXyhxLevel())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXyhxLevel, correctionEstimatePortrayalParticularsParam.getXyhxLevel());
            }
            // 根据个人基本画像风险等级 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJbhxLevel())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJbhxLevel, correctionEstimatePortrayalParticularsParam.getJbhxLevel());
            }
            // 根据心理画像权重总分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXlhxZf())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXlhxZf, correctionEstimatePortrayalParticularsParam.getXlhxZf());
            }
            // 根据知法画像权重总分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getZfhxZf())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getZfhxZf, correctionEstimatePortrayalParticularsParam.getZfhxZf());
            }
            // 根据就业画像权重总分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJyhxZf())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJyhxZf, correctionEstimatePortrayalParticularsParam.getJyhxZf());
            }
            // 根据家庭画像权重总分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJthxZf())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJthxZf, correctionEstimatePortrayalParticularsParam.getJthxZf());
            }
            // 根据信用画像权重总分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXyhxZf())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXyhxZf, correctionEstimatePortrayalParticularsParam.getXyhxZf());
            }
            // 根据个人基本画像权重总分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJbhxZf())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJbhxZf, correctionEstimatePortrayalParticularsParam.getJbhxZf());
            }
            // 根据心理画像扣分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXlhxKf())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXlhxKf, correctionEstimatePortrayalParticularsParam.getXlhxKf());
            }
            // 根据知法画像扣分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getZfhxKf())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getZfhxKf, correctionEstimatePortrayalParticularsParam.getZfhxKf());
            }
            // 根据就业画像扣分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJyhxKf())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJyhxKf, correctionEstimatePortrayalParticularsParam.getJyhxKf());
            }
            // 根据家庭画像扣分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJthxKf())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJthxKf, correctionEstimatePortrayalParticularsParam.getJthxKf());
            }
            // 根据信用画像扣分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXyhxKf())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXyhxKf, correctionEstimatePortrayalParticularsParam.getXyhxKf());
            }
            // 根据个人基本画像扣分 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJbhxKf())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJbhxKf, correctionEstimatePortrayalParticularsParam.getJbhxKf());
            }
            // 根据心理画像轨迹风险分析 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXlhxGjqs())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXlhxGjqs, correctionEstimatePortrayalParticularsParam.getXlhxGjqs());
            }
            // 根据知法画像轨迹风险分析 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getZfhxGjqs())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getZfhxGjqs, correctionEstimatePortrayalParticularsParam.getZfhxGjqs());
            }
            // 根据就业画像轨迹风险分析 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJyhxGjqs())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJyhxGjqs, correctionEstimatePortrayalParticularsParam.getJyhxGjqs());
            }
            // 根据家庭画像轨迹风险分析 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJthxGjqs())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJthxGjqs, correctionEstimatePortrayalParticularsParam.getJthxGjqs());
            }
            // 根据信用画像轨迹风险分析 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getXyhxGjqs())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getXyhxGjqs, correctionEstimatePortrayalParticularsParam.getXyhxGjqs());
            }
            // 根据个人基本画像轨迹风险分析 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getJbhxGjqs())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getJbhxGjqs, correctionEstimatePortrayalParticularsParam.getJbhxGjqs());
            }
            // 根据是否删除（0：未删除，1删除） 查询
            if (ObjectUtil.isNotEmpty(correctionEstimatePortrayalParticularsParam.getDelFlag())) {
                queryWrapper.lambda().eq(CorrectionEstimatePortrayalParticulars::getDelFlag, correctionEstimatePortrayalParticularsParam.getDelFlag());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionEstimatePortrayalParticulars> list(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam) {
        CorrectionEstimatePortrayalParticulars correctionEstimatePortrayalParticulars = new CorrectionEstimatePortrayalParticulars();
        BeanUtil.copyProperties(correctionEstimatePortrayalParticularsParam, correctionEstimatePortrayalParticulars);
        this.save(correctionEstimatePortrayalParticulars);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam) {
        this.removeById(correctionEstimatePortrayalParticularsParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam) {
        CorrectionEstimatePortrayalParticulars correctionEstimatePortrayalParticulars = this.queryCorrectionEstimatePortrayalParticulars(correctionEstimatePortrayalParticularsParam);
        BeanUtil.copyProperties(correctionEstimatePortrayalParticularsParam, correctionEstimatePortrayalParticulars);
        this.updateById(correctionEstimatePortrayalParticulars);
    }

    @Override
    public CorrectionEstimatePortrayalParticulars detail(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam) {
        return this.queryCorrectionEstimatePortrayalParticulars(correctionEstimatePortrayalParticularsParam);
    }

    /**
     * 获取心理画像明细（新）
     *
     * <AUTHOR>
     * @date 2023-01-14 10:38:47
     */
    private CorrectionEstimatePortrayalParticulars queryCorrectionEstimatePortrayalParticulars(CorrectionEstimatePortrayalParticularsParam correctionEstimatePortrayalParticularsParam) {
        //CorrectionEstimatePortrayalParticulars correctionEstimatePortrayalParticulars = this.getById(correctionEstimatePortrayalParticularsParam.getId());
        LambdaQueryWrapper<CorrectionEstimatePortrayalParticulars> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CorrectionEstimatePortrayalParticulars::getPortrayalId, correctionEstimatePortrayalParticularsParam.getPortrayalId());
        CorrectionEstimatePortrayalParticulars correctionEstimatePortrayalParticulars = this.getOne(lambdaQueryWrapper);
        if (ObjectUtil.isNull(correctionEstimatePortrayalParticulars)) {
            throw new ServiceException(CorrectionEstimatePortrayalDetailExceptionEnum.NOT_EXIST);
        }
        return correctionEstimatePortrayalParticulars;
    }

    @Override
    public CorrectionEstimatePortrayalParticulars getByPortrayalId(String portrayalId) {
        LambdaQueryWrapper<CorrectionEstimatePortrayalParticulars> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CorrectionEstimatePortrayalParticulars::getDelFlag, 0);
        lambdaQueryWrapper.eq(CorrectionEstimatePortrayalParticulars::getPortrayalId, portrayalId);
        return getOne(lambdaQueryWrapper);
    }
}
