package com.concise.gen.correctionrelationships.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 家庭成员
 *
 * <AUTHOR>
 * @date 2022-02-22 16:07:50
 */
@Data
@TableName("correction_relationships")
public class CorrectionRelationships {

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 社区矫正人员标识
     */
    private String pid;

    /**
     * 称谓
     */
    private String gx;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 所在单位
     */
    private String szdw;

    /**
     * 居住地址
     */
    private String jtzz;

    /**
     * 联系电话
     */
    private String lxdh;

    /**
     * 称谓name
     */
    private String gxName;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

}
