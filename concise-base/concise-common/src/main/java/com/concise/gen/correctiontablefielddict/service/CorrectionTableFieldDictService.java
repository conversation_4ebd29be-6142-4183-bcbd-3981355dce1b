package com.concise.gen.correctiontablefielddict.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctiontablefielddict.entity.CorrectionTableFieldDict;
import com.concise.gen.correctiontablefielddict.param.CorrectionTableFieldDictParam;
import java.util.List;

/**
 * 表字段与字典关联表service接口
 *
 * <AUTHOR>
 * @date 2024-11-27 16:36:14
 */
public interface CorrectionTableFieldDictService extends IService<CorrectionTableFieldDict> {

    /**
     * 查询表字段与字典关联表
     *
     * <AUTHOR>
     * @date 2024-11-27 16:36:14
     */
    PageResult<CorrectionTableFieldDict> page(CorrectionTableFieldDictParam correctionTableFieldDictParam);

    /**
     * 表字段与字典关联表列表
     *
     * <AUTHOR>
     * @date 2024-11-27 16:36:14
     */
    List<CorrectionTableFieldDict> list(CorrectionTableFieldDictParam correctionTableFieldDictParam);

    /**
     * 添加表字段与字典关联表
     *
     * <AUTHOR>
     * @date 2024-11-27 16:36:14
     */
    void add(CorrectionTableFieldDictParam correctionTableFieldDictParam);

    /**
     * 删除表字段与字典关联表
     *
     * <AUTHOR>
     * @date 2024-11-27 16:36:14
     */
    void delete(CorrectionTableFieldDictParam correctionTableFieldDictParam);

    /**
     * 编辑表字段与字典关联表
     *
     * <AUTHOR>
     * @date 2024-11-27 16:36:14
     */
    void edit(CorrectionTableFieldDictParam correctionTableFieldDictParam);

    /**
     * 查看表字段与字典关联表
     *
     * <AUTHOR>
     * @date 2024-11-27 16:36:14
     */
    CorrectionTableFieldDict detail(CorrectionTableFieldDictParam correctionTableFieldDictParam);

    /**
     * 批量新增表字段与字典关联表
     *
     * <AUTHOR>
     * @date 2024-11-27 16:36:14
     */
    void batchAdd(List<CorrectionTableFieldDictParam> correctionTableFieldDictParamList);
}
