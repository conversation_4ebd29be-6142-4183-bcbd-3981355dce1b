package com.concise.gen.correctioncrimepartner.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctioncrimepartner.entity.CorrectionCrimePartner;
import com.concise.gen.correctioncrimepartner.mapper.CorrectionCrimePartnerMapper;
import com.concise.gen.correctioncrimepartner.param.CorrectionCrimePartnerParam;
import com.concise.gen.correctioncrimepartner.service.CorrectionCrimePartnerService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 同案犯service接口实现类
 *
 * <AUTHOR>
 * @date 2022-02-22 16:07:46
 */
@DS("slave")
@Service
public class CorrectionCrimePartnerServiceImpl extends ServiceImpl<CorrectionCrimePartnerMapper, CorrectionCrimePartner> implements CorrectionCrimePartnerService {

    @Override
    public PageResult<CorrectionCrimePartner> page(CorrectionCrimePartnerParam correctionCrimePartnerParam) {
        QueryWrapper<CorrectionCrimePartner> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionCrimePartnerParam)) {

            // 根据社区矫正人员标识 查询
            if (ObjectUtil.isNotEmpty(correctionCrimePartnerParam.getPid())) {
                queryWrapper.lambda().eq
                        (CorrectionCrimePartner::getPid, correctionCrimePartnerParam.getPid());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionCrimePartner> list(CorrectionCrimePartnerParam correctionCrimePartnerParam) {
        return this.list();
    }

}
