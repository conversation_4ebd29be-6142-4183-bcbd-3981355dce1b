package com.concise.gen.correctionlabelcorrpsn.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionabilitymanage.entity.CorrectionAbilityManage;
import com.concise.gen.correctionlabelcorrpsn.entity.CorrectionLabelCorrpsn;
import com.concise.gen.correctionlabelcorrpsn.param.CorrectionLabelCorrpsnParam;
import com.concise.gen.correctionlabelmanage.entity.CorrectionLabelManage;
import com.concise.gen.correctionobjectinformation.entity.ScreenModel;

import java.util.List;
import java.util.Set;

/**
 * 标签关联矫正对象service接口
 *
 * <AUTHOR>
 * @date 2022-03-04 14:58:19
 */
public interface CorrectionLabelCorrpsnService extends IService<CorrectionLabelCorrpsn> {

    /**
     * 查询标签关联矫正对象
     *
     * <AUTHOR>
     * @date 2022-03-04 14:58:19
     */
    PageResult<CorrectionLabelCorrpsn> page(CorrectionLabelCorrpsnParam correctionLabelCorrpsnParam);

    /**
     * 标签关联矫正对象列表
     *
     * <AUTHOR>
     * @date 2022-03-04 14:58:19
     */
    List<CorrectionLabelCorrpsn> list(CorrectionLabelCorrpsnParam correctionLabelCorrpsnParam);

    /**
     * 添加标签关联矫正对象
     *
     * <AUTHOR>
     * @date 2022-03-04 14:58:19
     */
    void add(CorrectionLabelCorrpsnParam correctionLabelCorrpsnParam);

    /**
     * 删除标签关联矫正对象
     *
     * <AUTHOR>
     * @date 2022-03-04 14:58:19
     */
    void delete(CorrectionLabelCorrpsnParam correctionLabelCorrpsnParam);

    /**
     * 编辑标签关联矫正对象
     *
     * <AUTHOR>
     * @date 2022-03-04 14:58:19
     */
    void edit(CorrectionLabelCorrpsnParam correctionLabelCorrpsnParam);

    /**
     * 查看标签关联矫正对象
     *
     * <AUTHOR>
     * @date 2022-03-04 14:58:19
     */
     CorrectionLabelCorrpsn detail(CorrectionLabelCorrpsnParam correctionLabelCorrpsnParam);

    /**
     * 保存信息
     * @param sqjzryId 矫正对象ID
     * @param labelIdList 标签集合
     * @param sfzh 身份证号
     */
     void saveInfo(String sqjzryId, List<String> labelIdList, String sfzh);

    /**
     * 批量异步保存标签
     */
    void saveData(List<CorrectionLabelCorrpsn> correctionLabelCorrpsnList);

    /**
     * 余杭大屏-家庭状态-查婚姻标签
     * @return
     */
    List<ScreenModel> familyStatus(Set<String> userIds);

    /**
     * 根据矫正对象和id获取已绑定的id
     * @param jzdxId
     * @param labelIds
     * @return
     */
    List<String> filterLabelsByPersonAndLabels(String jzdxId, List<String> labelIds);

    /**
     * 根据标签ID获取方案库信息
     * @param labelIds
     * @return
     */
    List<CorrectionLabelCorrpsn> getLabelMeasures(Set<String> labelIds);


    /**
     * 根据标签ID获取能力信息
     * @param labelManageList
     * @return
     */
    List<CorrectionAbilityManage> getLabelAbility(Set<String> labelManageList);
}
