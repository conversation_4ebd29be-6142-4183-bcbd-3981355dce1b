package com.concise.gen.correctioncrimepartner.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.Date;

/**
 * 同案犯
 *
 * <AUTHOR>
 * @date 2022-02-22 16:07:46
 */
@Data
@TableName("correction_crime_partner")
public class CorrectionCrimePartner {

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 社区矫正人员标识
     */
    private String pid;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    private String xb;

    /**
     * 性别name
     */
    private String xbName;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    @ApiModelProperty(value = "出生日期")
    private Date csrq;

    /**
     * 罪名
     */
    private String szdw;

    /**
     * 罪名name
     */
    private String szdwName;

    /**
     * 被判处刑罚及所在监所
     */
    private String bpcxzjszjs;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

}
