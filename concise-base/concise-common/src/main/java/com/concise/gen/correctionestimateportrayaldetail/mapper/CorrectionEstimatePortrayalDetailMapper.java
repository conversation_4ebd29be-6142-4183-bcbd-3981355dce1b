package com.concise.gen.correctionestimateportrayaldetail.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.correctionestimateportrayaldetail.entity.CorrectionEstimatePortrayalDetail;
import com.concise.gen.correctionestimateportrayaldetail.entity.vo.RiskAssessmentVo;
import com.concise.gen.correctionestimateportrayaldetail.entity.vo.RiskStatisticsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 画像明细
 *
 * <AUTHOR>
 * @date 2022-11-30 19:58:01
 */
public interface CorrectionEstimatePortrayalDetailMapper extends BaseMapper<CorrectionEstimatePortrayalDetail> {

    List<RiskAssessmentVo> riskAssessment(@Param("riskLevel") String riskLevel);

    RiskStatisticsVo riskStatistics(@Param("riskLevel") String riskLevel);
}
