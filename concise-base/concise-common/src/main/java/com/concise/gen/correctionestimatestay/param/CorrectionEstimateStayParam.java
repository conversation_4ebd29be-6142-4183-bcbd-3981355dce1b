package com.concise.gen.correctionestimatestay.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
* 在矫评估参数类
 *
 * <AUTHOR>
 * @date 2022-05-17 11:48:38
*/
@Data
public class CorrectionEstimateStayParam extends BaseParam {

    /**
     * 主键id
     */
    private String id;

    /**
     * 矫正对象id
     */
    private String sqjzryId;

    /**
     * 姓名
     */
    private String sqjzryName;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 矫正机构ID
     */
    private String jzjg;

    /**
     * 矫正机构名称
     */
    private String jzjgName;

    /**
     * 总分
     */
    private BigDecimal scoreTotal;

    /**
     * 评估分
     */
    private BigDecimal scoreEstimate;

    /**
     * 评估月份
     */
    private Integer estimateMonth;

    /**
     * 是否等级降到严管（0：否，1: 是）
     */
    private Integer levelDown;

    /**
     * 信息化监管违规
     */
    private Integer yqViolate;

    /**
     * 训诫
     */
    private Integer advise;

    /**
     * 警告
     */
    private Integer warn;

    /**
     * 治安处罚
     */
    private Integer publicSecurity;

    /**
     * 提请逮捕
     */
    private Integer askArrest;

    /**
     * 提请撤缓
     */
    private Integer cancelProbation;

    /**
     * 提请撤销假释
     */
    private Integer cancelParole;

    /**
     * 提请收监执行
     */
    private Integer committedToPrison;

    /**
     * 工作变动
     */
    private Integer workChange;

    /**
     * 夜不归宿
     */
    private Integer nightOut;

    /**
     * 心情码状态（1：红码，2: 橙码，3：黄码）
     */
    private Integer heartCode;

    /**
     * 受到表扬
     */
    private Integer praise;

    /**
     * 获得减刑
     */
    private Integer penaltyDown;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 日常监管得分
     */
    private BigDecimal rcjgScore;

    /**
     * 处罚得分
     */
    private BigDecimal cfScore;

    /**
     * 行为动态得分
     */
    private BigDecimal xwdtScore;

    /**
     * 心理状态得分
     */
    private BigDecimal xlztScore;

}
