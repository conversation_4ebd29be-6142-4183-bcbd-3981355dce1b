package com.concise.gen.correctionabilitymanage.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.concise.gen.correctionlabelmanage.entity.CorrectionLabelManage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.List;

/**
 * 能力管理
 *
 * <AUTHOR>
 * @date 2022-03-03 10:57:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_ability_manage")
public class CorrectionAbilityManage extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 日常监管措施
     */
    private String dailySupervisionStep;

    /**
     * 教育帮扶措施
     */
    private String educationalAssistanceStep;

    /**
     * 心理矫正措施
     */
    private String psychologicalCorrectionStep;

    /**
     * 矫正阶段（1：入矫初期 2：矫正中期 3：矫正末期）
     */
    private Integer correctPhase;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 标签ids
     */
    @TableField(exist = false)
    private String labelIds;

    /**
     * 标签名称s
     */
    @TableField(exist = false)
    private String labelNames;

    /**
     * 标签list
     */
    @TableField(exist = false)
    private List<CorrectionLabelManage> lableList;

    @TableField(exist = false)
    private List<String> lableIdList;

    /**
     * 来源
     */
    private String source;

    /**
     * 第三方-方案id
     */
    private String schemeId;

    /**
     * 父id
     */
    private Long pid;

    @TableField(exist = false)
    private List<CorrectionAbilityManage> childrenList;
}
