package com.concise.gen.correctionrisklevel.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimateenter.service.CorrectionEstimateEnterService;
import com.concise.gen.correctionestimatestay.entity.CorrectionEstimateStay;
import com.concise.gen.correctionestimatestay.mapper.CorrectionEstimateStayMapper;
import com.concise.gen.correctionestimatestay.service.CorrectionEstimateStayService;
import com.concise.gen.correctionobjectinformation.entity.ScreenModel;
import com.concise.gen.correctionrisklevel.entity.CorrectionRiskLevel;
import com.concise.gen.correctionrisklevel.entity.CorrectionRiskLevelDto;
import com.concise.gen.correctionrisklevel.enums.CorrectionRiskLevelExceptionEnum;
import com.concise.gen.correctionrisklevel.mapper.CorrectionRiskLevelMapper;
import com.concise.gen.correctionrisklevel.param.CorrectionRiskLevelParam;
import com.concise.gen.correctionrisklevel.service.CorrectionRiskLevelService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 风险等级service接口实现类
 *
 * <AUTHOR>
 * @date 2022-05-18 18:29:16
 */
@Service
public class CorrectionRiskLevelServiceImpl extends ServiceImpl<CorrectionRiskLevelMapper, CorrectionRiskLevel> implements CorrectionRiskLevelService {

    @Resource
    private CorrectionEstimateStayService correctionEstimateStayService;

    @Resource
    private CorrectionEstimateStayMapper correctionEstimateStayMapper;

    @Resource
    private CorrectionEstimateEnterService correctionEstimateEnterService;

    @Override
    public PageResult<CorrectionRiskLevel> page(CorrectionRiskLevelParam correctionRiskLevelParam, Set<String> org) {
        QueryWrapper<CorrectionRiskLevel> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionRiskLevelParam)) {
            // 根据矫正机构id 查询
            queryWrapper.lambda().in(CorrectionRiskLevel::getJzjg, org);
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionRiskLevelParam.getSqjzryName())) {
                queryWrapper.lambda().like(CorrectionRiskLevel::getSqjzryName, correctionRiskLevelParam.getSqjzryName());
            }
            // 根据风险等级(当月) 查询
            if (ObjectUtil.isNotEmpty(correctionRiskLevelParam.getRiskLevel())) {
                queryWrapper.lambda().eq(CorrectionRiskLevel::getRiskLevel, correctionRiskLevelParam.getRiskLevel());
            }
            // 根据风险等级(矫正期) 查询
            if (ObjectUtil.isNotEmpty(correctionRiskLevelParam.getRiskLevelAverage())) {
                queryWrapper.lambda().eq(CorrectionRiskLevel::getRiskLevelAverage, correctionRiskLevelParam.getRiskLevelAverage());
            }
        }
        queryWrapper.lambda().eq(CorrectionRiskLevel::getDelFlag, 0);
        queryWrapper.lambda().orderByDesc(CorrectionRiskLevel::getCreateTime);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionRiskLevel> list(CorrectionRiskLevelParam correctionRiskLevelParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionRiskLevelParam correctionRiskLevelParam) {
        CorrectionRiskLevel correctionRiskLevel = new CorrectionRiskLevel();
        BeanUtil.copyProperties(correctionRiskLevelParam, correctionRiskLevel);
        this.save(correctionRiskLevel);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionRiskLevelParam correctionRiskLevelParam) {
        this.removeById(correctionRiskLevelParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionRiskLevelParam correctionRiskLevelParam) {
        CorrectionRiskLevel correctionRiskLevel = this.queryCorrectionRiskLevel(correctionRiskLevelParam);
        BeanUtil.copyProperties(correctionRiskLevelParam, correctionRiskLevel);
        this.updateById(correctionRiskLevel);
    }

    @Override
    public CorrectionRiskLevel detail(CorrectionRiskLevelParam correctionRiskLevelParam) {
        return this.queryCorrectionRiskLevel(correctionRiskLevelParam);
    }

    /**
     * 获取风险等级
     *
     * <AUTHOR>
     * @date 2022-05-18 18:29:16
     */
    private CorrectionRiskLevel queryCorrectionRiskLevel(CorrectionRiskLevelParam correctionRiskLevelParam) {
        CorrectionRiskLevel correctionRiskLevel = this.getById(correctionRiskLevelParam.getId());
        if (ObjectUtil.isNull(correctionRiskLevel)) {
            throw new ServiceException(CorrectionRiskLevelExceptionEnum.NOT_EXIST);
        }
        return correctionRiskLevel;
    }

    @Override
    public void sycnData() {
        try {
            SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
            int month = Integer.parseInt(sf.format(new Date()).substring(0, 6));
            int day = Integer.parseInt(sf.format(new Date()).substring(6, 8));
            CorrectionRiskLevel riskLevel = null;
            //查询在矫评估本月的数据
            QueryWrapper<CorrectionEstimateStay> queryWrapper = new QueryWrapper();
            queryWrapper.lambda().eq(CorrectionEstimateStay::getEstimateMonth, month);
            queryWrapper.lambda().eq(CorrectionEstimateStay::getDelFlag, 0);
            List<CorrectionEstimateStay> list = correctionEstimateStayService.list(queryWrapper);
            List<CorrectionRiskLevel> listContent = null;
            CorrectionRiskLevel levelObj = null;
            String moonCodeStr = "";
            for (CorrectionEstimateStay estimateStay : list) {
                moonCodeStr = "";
                levelObj = this.baseMapper.getInfo(estimateStay.getSqjzryId(), String.valueOf(month));
                // 先根据万达平台的社区矫正人员id 查找云雀平台的人员id
                String correctionObjectId = correctionEstimateStayMapper.getCorrectionObjectId(estimateStay.getSqjzryId());
                riskLevel = new CorrectionRiskLevel();
                riskLevel.setScoringTime(month);
                riskLevel.setSqjzryId(estimateStay.getSqjzryId());
                riskLevel.setSqjzryName(estimateStay.getSqjzryName());
                riskLevel.setSfzh(estimateStay.getSfzh());
                riskLevel.setJzjg(estimateStay.getJzjg());
                riskLevel.setJzjgName(estimateStay.getJzjgName());
                //TODO 从模型读取
                riskLevel.setScoreBase(new BigDecimal(1000));
                riskLevel.setScoreBonus(new BigDecimal(50));
                riskLevel.setScoreEstimate(estimateStay.getScoreEstimate());
                riskLevel.setRiskLevel(getRiskLevel(estimateStay.getScoreEstimate()));
                //本月实时风险-日常监管 【普管降严管、信息化监管违规】
                listContent = this.baseMapper.findRcjg(estimateStay.getSqjzryId(), correctionObjectId, String.valueOf(month));
                riskLevel.setDailySupervision(linkContent(listContent));
                //本月实时风险_处罚 【训诫*警告*治安处罚*提请逮捕*提请撤缓*提请撤销假释*提请收监执行】
                listContent = this.baseMapper.findListCf(estimateStay.getSqjzryId(), String.valueOf(month));
                riskLevel.setPunish(linkContent(listContent));
                //本月实时风险_行为动态 【工作变动*夜不归宿】
                listContent = this.baseMapper.findListXwdt(correctionObjectId, String.valueOf(month));
                riskLevel.setActionTrends(linkContent(listContent));
                //本月实时风险_心理状态
                riskLevel.setPsychicAction(getMoonCode(correctionEstimateStayMapper.getMoonCode(estimateStay.getSqjzryId())));

                if (1 == day) {
                    //历史风险分析相关项 只在1号的时候更新
                    //TODO 平均的要跟往年计算
                    riskLevel.setScoreEstimateAverage(estimateStay.getScoreEstimate());
                    riskLevel.setRiskLevelAverage(getRiskLevel(estimateStay.getScoreEstimate()));
                    riskLevel.setEstimateEnterPercent(correctionEstimateEnterService.getPercent(estimateStay.getSqjzryId()));
                    /*riskLevel.setRcjgScore(estimateStay.getRcjgScore());
                    riskLevel.setCfScore(estimateStay.getCfScore());
                    riskLevel.setXwdtScore(estimateStay.getXwdtScore());
                    riskLevel.setXlztScore(estimateStay.getXlztScore());*/
                    riskLevel.setDailySupervisionPercent(getPercent(estimateStay.getRcjgScore().doubleValue(), new Double("200")));
                    riskLevel.setPunishPercent(getPercent(estimateStay.getCfScore().doubleValue(), new Double("450")));
                    riskLevel.setActionTrendsPercent(getPercent(estimateStay.getXwdtScore().doubleValue(), new Double("150")));
                    riskLevel.setPsychicActionPercent(getPercent(estimateStay.getXlztScore().doubleValue(), new Double("100")));
                    listContent = this.baseMapper.findLevel(estimateStay.getSqjzryId());
                    riskLevel.setDailySupervisionDetail(linkContent(listContent));
                    // 【信息化监管违规*训诫*警告*治安处罚*提请逮捕*提请撤缓*提请撤销假释*提请收监执行*工作变动*夜不归宿*受到表扬*获得减刑】
                    List<Integer> numList = correctionEstimateStayMapper.getNumsAll(estimateStay.getSqjzryId(), correctionObjectId);
                    riskLevel.setYqViolate(numList.get(0));
                    riskLevel.setAdvise(numList.get(1));
                    riskLevel.setWarn(numList.get(2));
                    riskLevel.setPublicSecurity(numList.get(3));
                    riskLevel.setAskArrest(numList.get(4));
                    riskLevel.setCancelProbation(numList.get(5));
                    riskLevel.setCancelParole(numList.get(6));
                    riskLevel.setCommittedToPrison(numList.get(7));
                    riskLevel.setXzPunish(0);
                    riskLevel.setWorkChange(numList.get(8));
                    riskLevel.setNightOut(numList.get(9));
                    listContent = this.baseMapper.findMoonCode(estimateStay.getSqjzryId());
                    for (CorrectionRiskLevel cl : listContent) {
                        moonCodeStr += cl.getTime() + "心理评测为：" + getMoonCode(cl.getContent()) + ";";
                    }
                    riskLevel.setPsychicStatus(moonCodeStr + "其他为绿码。");
                    riskLevel.setSummary("截止到目前，该名社区矫正对象的监管风险等级评估为：" + getRiskLevelName(riskLevel.getRiskLevelAverage()));
                }

                if (null != levelObj) {
                    riskLevel.setId(levelObj.getId());
                    this.updateById(riskLevel);
                } else {
                    this.save(riskLevel);
                }
            }
        } catch (Exception e) {
            log.error("riskLevel sycnData Error:" + e.getMessage());
        }

    }

    /**
     * 按指标拼接实时监控情况
     *
     * @param listContent
     * @return
     */
    public String linkContent(List<CorrectionRiskLevel> listContent) {
        String content = "";
        for (CorrectionRiskLevel rl : listContent) {
            content += rl.getTime() + " " + rl.getContent() + ";\n";
        }
        return content;
    }

    /**
     * 风险等级转换
     *
     * @param score
     * @return
     */
    public String getRiskLevel(BigDecimal score) {
        String riskLevel = "";
        if (score.intValue() >= 700) {
            //低风险
            riskLevel = "FXDJ01";
        } else if (score.intValue() >= 600 && score.intValue() < 700) {
            //较低风险
            riskLevel = "FXDJ02";
        } else if (score.intValue() >= 500 && score.intValue() < 600) {
            //中风险
            riskLevel = "FXDJ03";
        } else if (score.intValue() >= 400 && score.intValue() < 500) {
            //较高风险
            riskLevel = "FXDJ04";
        } else {
            //高风险
            riskLevel = "FXDJ05";
        }
        return riskLevel;
    }

    /**
     * 风险等级转换中文
     *
     * @param riskLevelAverage
     * @return
     */
    public String getRiskLevelName(String riskLevelAverage) {
        String riskLevelName = "";
        int tag = Integer.parseInt(riskLevelAverage.substring(riskLevelAverage.length() - 1));
        switch (tag) {
            case 1:
                riskLevelName = "低风险";
                break;
            case 2:
                riskLevelName = "较低风险";
                break;
            case 3:
                riskLevelName = "中风险";
                break;
            case 4:
                riskLevelName = "较高风险";
                break;
            case 5:
                riskLevelName = "高风险";
                break;
        }
        return riskLevelName;
    }

    /**
     * 心情码转换  //心情码  5：绿码、4：蓝码、3：黄码、2：橙码、1：红码'
     *
     * @param moonCode
     * @return
     */
    public String getMoonCode(String moonCode) {
        String rs = "暂未测评";
        if (ObjectUtil.isEmpty(moonCode) || moonCode.trim().length() != 1) {
            return rs;
        }
        int tag = Integer.parseInt(moonCode);
        switch (tag) {
            case 1:
                rs = "红码";
                break;
            case 2:
                rs = "橙码";
                break;
            case 3:
                rs = "黄码";
                break;
            case 4:
                rs = "蓝码";
                break;
            case 5:
                rs = "绿码";
                break;
        }
        return rs;
    }

    /**
     * 获取上个月的风险信息
     * @param sqjzryId
     * @return
     */
    /*public CorrectionRiskLevel getByMonth(String sqjzryId) {
        CorrectionRiskLevel correctionRiskLevel = null;
        QueryWrapper<CorrectionRiskLevel> queryWrapper = new QueryWrapper<CorrectionRiskLevel>();
        queryWrapper.lambda().eq(CorrectionRiskLevel::getSqjzryId, sqjzryId);
        queryWrapper.lambda().eq(CorrectionRiskLevel::getScoringTime, DateTimeUtil.getLastMonth());
        queryWrapper.lambda().eq(CorrectionRiskLevel::getDelFlag, 0);
        queryWrapper.select("id, rcjg_score, cf_score, xwdt_score, xlzt_score");
        List<CorrectionRiskLevel> list = this.list(queryWrapper);
        if (list.size() > 0) {
            correctionRiskLevel = list.get(0);
        }
        return correctionRiskLevel;
    }*/

    /**
     * 计算百分比
     *
     * @param cs  除数
     * @param bcs 被除数
     * @return
     */
    public String getPercent(double cs, double bcs) {
        try {
            double val = (cs / bcs) * 100;
            DecimalFormat df = new DecimalFormat("0.00");
            return df.format(val);
        } catch (Exception e) {
            return "0";
        }

    }

    @Override
    public List<ScreenModel> riskLevel(Set<String> userIds) {
        return this.baseMapper.riskLevel(userIds);
    }

    @Override
    public List<CorrectionRiskLevel> riskLevelDetail(String title, Set<String> userIds, String name) {
        return this.baseMapper.riskLevelDetail(title, userIds, name);
    }

    @Override
    public Page<CorrectionRiskLevelDto> riskAssessmentPage(CorrectionRiskLevelParam correctionRiskLevelParam, Set<String> deptIds) {
        QueryWrapper<CorrectionRiskLevel> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionRiskLevelParam)) {
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionRiskLevelParam.getSqjzryName())) {
                queryWrapper.lambda().like(CorrectionRiskLevel::getSqjzryName, correctionRiskLevelParam.getSqjzryName());
            }
            //根据矫正对象id查询
            if (ObjectUtil.isNotEmpty(correctionRiskLevelParam.getSqjzryId())) {
                queryWrapper.lambda().like(CorrectionRiskLevel::getSqjzryId, correctionRiskLevelParam.getSqjzryId());
            }
            // 根据风险等级(当月) 查询
            if (ObjectUtil.isNotEmpty(correctionRiskLevelParam.getRiskLevel())) {
                queryWrapper.lambda().eq(CorrectionRiskLevel::getRiskLevel, correctionRiskLevelParam.getRiskLevel());
            }
            // 根据风险等级(矫正期) 查询
            if (ObjectUtil.isNotEmpty(correctionRiskLevelParam.getRiskLevelAverage())) {
                queryWrapper.lambda().eq(CorrectionRiskLevel::getRiskLevelAverage, correctionRiskLevelParam.getRiskLevelAverage());
            }
        }
        queryWrapper.lambda().in(CorrectionRiskLevel::getJzjg, deptIds);
        queryWrapper.lambda().eq(CorrectionRiskLevel::getDelFlag, 0);
        queryWrapper.lambda().orderByDesc(CorrectionRiskLevel::getCreateTime);
        return this.baseMapper.riskAssessmentPage(PageFactory.defaultPage(), queryWrapper);
    }
}
