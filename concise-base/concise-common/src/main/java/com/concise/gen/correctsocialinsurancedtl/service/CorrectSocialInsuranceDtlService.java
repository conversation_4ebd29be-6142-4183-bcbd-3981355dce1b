package com.concise.gen.correctsocialinsurancedtl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctsocialinsurancedtl.entity.CorrectSocialInsuranceDtl;
import com.concise.gen.correctsocialinsurancedtl.param.CorrectSocialInsuranceDtlParam;
import java.util.List;

/**
 * 历史社保明细表service接口
 *
 * <AUTHOR>
 * @date 2022-06-07 15:08:51
 */
public interface CorrectSocialInsuranceDtlService extends IService<CorrectSocialInsuranceDtl> {

    /**
     * 查询历史社保明细表
     *
     * <AUTHOR>
     * @date 2022-06-07 15:08:51
     */
    PageResult<CorrectSocialInsuranceDtl> page(CorrectSocialInsuranceDtlParam correctSocialInsuranceDtlParam);

    /**
     * 历史社保明细表列表
     *
     * <AUTHOR>
     * @date 2022-06-07 15:08:51
     */
    List<CorrectSocialInsuranceDtl> list(CorrectSocialInsuranceDtlParam correctSocialInsuranceDtlParam);

    /**
     * 添加历史社保明细表
     *
     * <AUTHOR>
     * @date 2022-06-07 15:08:51
     */
    void add(CorrectSocialInsuranceDtlParam correctSocialInsuranceDtlParam);

    /**
     * 删除历史社保明细表
     *
     * <AUTHOR>
     * @date 2022-06-07 15:08:51
     */
    void delete(CorrectSocialInsuranceDtlParam correctSocialInsuranceDtlParam);

    /**
     * 编辑历史社保明细表
     *
     * <AUTHOR>
     * @date 2022-06-07 15:08:51
     */
    void edit(CorrectSocialInsuranceDtlParam correctSocialInsuranceDtlParam);

    /**
     * 查看历史社保明细表
     *
     * <AUTHOR>
     * @date 2022-06-07 15:08:51
     */
     CorrectSocialInsuranceDtl detail(CorrectSocialInsuranceDtlParam correctSocialInsuranceDtlParam);
}
