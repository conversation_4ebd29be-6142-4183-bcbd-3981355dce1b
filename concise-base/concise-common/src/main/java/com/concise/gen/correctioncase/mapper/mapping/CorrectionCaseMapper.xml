<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.correctioncase.mapper.CorrectionCaseMapper">
    <resultMap id="caseResult" type="com.concise.gen.correctioncase.entity.CorrectionCase">
        <result column="id" property="id" />
        <result column="title" property="title" />
        <result column="case_type" property="caseType" />
        <result column="case_content" property="caseContent" />
        <result column="create_time" property="createTime" />
        <result column="label_ids" property="labelIds" />
    </resultMap>

    <select id="findByLabel" resultMap="caseResult">
        select ca.id, ca.title, ca.case_type, ca.create_time,
        (SELECT GROUP_CONCAT(cal.lable_id SEPARATOR ',') FROM  correction_case_lable cal WHERE cal.case_id = ca.id) as label_ids
        from correction_case ca where ca.id in (
                SELECT distinct (case_id) from correction_case_lable where lable_id in
                    <foreach collection="lableIds" index="index" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
        )
    </select>
</mapper>
