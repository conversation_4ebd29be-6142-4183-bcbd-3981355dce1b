package com.concise.gen.correctionlabelmanage.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.List;

/**
 * 标签管理
 *
 * <AUTHOR>
 * @date 2022-03-03 09:48:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_label_manage")
public class CorrectionLabelManage extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 标签
     */
    private String label;

    /**
     * 标签属性
     */
    private String labelAttribute;

    /**
     * 标签属性字典码
     */
    private String labelAttributeCode;

    /**
     * 属性分类
     */
    private String attributeType;

    /**
     * 属性分类字典码
     */
    private String attributeTypeCode;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 所属系统_多个系统间逗号隔开
     */
    private String belongSys;

    /**
     * 电话核查优先级
     */
    private int orderIndex;

    /**
     * 关联矫正对象数量
     */
    @TableField(exist = false)
    private String sqjzryNum;

    /**
     * 关联字典值
     */
    @TableField(exist = false)
    private String dictNum;

    /**
     * 标签ids
     */
    @TableField(exist = false)
    private String[] idArr;

    /**
     * 标签集合
     */
    @TableField(exist = false)
    private List<CorrectionLabelManage> labelList;

    /**
     * 子集合
     */
    @TableField(exist = false)
    private List<CorrectionLabelManage> children;

    /**
     * 树的层级
     */
    private Integer level;

    /**
     * 上级id
     */
    private String parentId;


    public CorrectionLabelManage(String attributeType,
                                 String attributeTypeCode,
                                 String belongSys,
                                 String dictNum,
                                 String id,
                                 String label,
                                 String labelAttribute,
                                 String labelAttributeCode,
                                 String remark,
                                 List<CorrectionLabelManage> children,
                                 Integer level,
                                 String parentId) {
        this.attributeType = attributeType;
        this.attributeTypeCode = attributeTypeCode;
        this.belongSys = belongSys;
        this.dictNum = dictNum;
        this.id = id;
        this.label = label;
        this.labelAttribute = labelAttribute;
        this.labelAttributeCode = labelAttributeCode;
        this.remark = remark;
        this.children = children;
        this.level = level;
        this.parentId = parentId;
    }

    public CorrectionLabelManage(){}

}
