package com.concise.gen.portraitImage.correctionquestionitem.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.concise.gen.portraitImage.correctionquestion.entity.CorrectionQuestion;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.List;

/**
 * 量表配置--试题选项表
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:38
 */
@Data
@TableName("correction_question_item")
public class CorrectionQuestionItem {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 问题id
     */
    private String questionId;

    /**
     * 选项序号
     */
    private Integer orderIndex;

    /**
     * 选项内容
     */
    private String content;

    /**
     * 是否有关联问题（0：没有 1：有）
     */
    private Integer haveHigherQuestion;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 问题列表
     */
    @TableField(exist = false)
    private List<CorrectionQuestion> questionList;

    /**
     * 是否选中， true 选中   false 未选中
     */
    @TableField(exist = false)
    private boolean isChecked;
}
