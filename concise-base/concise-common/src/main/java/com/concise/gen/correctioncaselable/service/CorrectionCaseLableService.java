package com.concise.gen.correctioncaselable.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctioncaselable.entity.CorrectionCaseLable;
import com.concise.gen.correctioncaselable.param.CorrectionCaseLableParam;
import java.util.List;

/**
 * 案例与标签关联表service接口
 *
 * <AUTHOR>
 * @date 2022-03-14 17:18:07
 */
public interface CorrectionCaseLableService extends IService<CorrectionCaseLable> {

    /**
     * 查询案例与标签关联表
     *
     * <AUTHOR>
     * @date 2022-03-14 17:18:07
     */
    PageResult<CorrectionCaseLable> page(CorrectionCaseLableParam correctionCaseLableParam);

    /**
     * 案例与标签关联表列表
     *
     * <AUTHOR>
     * @date 2022-03-14 17:18:07
     */
    List<CorrectionCaseLable> list(CorrectionCaseLableParam correctionCaseLableParam);

    /**
     * 添加案例与标签关联表
     *
     * <AUTHOR>
     * @date 2022-03-14 17:18:07
     */
    void add(CorrectionCaseLableParam correctionCaseLableParam);

    /**
     * 删除案例与标签关联表
     *
     * <AUTHOR>
     * @date 2022-03-14 17:18:07
     */
    void delete(CorrectionCaseLableParam correctionCaseLableParam);

    /**
     * 编辑案例与标签关联表
     *
     * <AUTHOR>
     * @date 2022-03-14 17:18:07
     */
    void edit(CorrectionCaseLableParam correctionCaseLableParam);

    /**
     * 查看案例与标签关联表
     *
     * <AUTHOR>
     * @date 2022-03-14 17:18:07
     */
     CorrectionCaseLable detail(CorrectionCaseLableParam correctionCaseLableParam);
}
