package com.concise.gen.messagemanage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.messagemanage.entity.MessageManage;
import com.concise.gen.messagemanage.param.MessageManageParam;
import java.util.List;

/**
 * MessageManageservice接口
 *
 * <AUTHOR>
 * @date 2023-01-04 14:10:20
 */
public interface MessageManageService extends IService<MessageManage> {

    /**
     * 查询MessageManage
     *
     * <AUTHOR>
     * @date 2023-01-04 14:10:20
     */
    PageResult<MessageManage> page(MessageManageParam messageManageParam);

    /**
     * MessageManage列表
     *
     * <AUTHOR>
     * @date 2023-01-04 14:10:20
     */
    List<MessageManage> list(MessageManageParam messageManageParam);

    /**
     * 添加MessageManage
     *
     * <AUTHOR>
     * @date 2023-01-04 14:10:20
     */
    void add(MessageManageParam messageManageParam);

    /**
     * 删除MessageManage
     *
     * <AUTHOR>
     * @date 2023-01-04 14:10:20
     */
    void delete(MessageManageParam messageManageParam);

    /**
     * 编辑MessageManage
     *
     * <AUTHOR>
     * @date 2023-01-04 14:10:20
     */
    void edit(MessageManageParam messageManageParam);

    /**
     * 查看MessageManage
     *
     * <AUTHOR>
     * @date 2023-01-04 14:10:20
     */
     MessageManage detail(MessageManageParam messageManageParam);
}
