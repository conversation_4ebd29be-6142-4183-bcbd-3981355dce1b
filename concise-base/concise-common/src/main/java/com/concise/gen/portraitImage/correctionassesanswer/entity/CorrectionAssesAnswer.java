package com.concise.gen.portraitImage.correctionassesanswer.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 评估管理--评估人员评估明细答案
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:45
 */
@Data
@TableName("correction_asses_answer")
public class CorrectionAssesAnswer {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 评估人员评估明细id(correction_asses_person_dtl.ID)
     */
    private String dtlId;

    /**
     * 问题id
     */
    private String questionId;

    /**
     * 答案(选项)ids
     */
    private String answerIds;

}
