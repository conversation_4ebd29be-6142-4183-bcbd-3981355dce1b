package com.concise.gen.correctioneducationalrectification.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 教育整顿参数类
 *
 * <AUTHOR>
 * @date 2022-03-10 17:37:39
*/
@Data
public class CorrectionEducationalRectificationParam extends BaseParam {

    /**
     * 
     */
    @NotNull(message = "不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空，请检查title参数", groups = {add.class, edit.class})
    private String title;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空，请检查content参数", groups = {add.class, edit.class})
    private String content;

    /**
     * dept_id 发布部门
     */
    private String deptId;

    /**
     * dept_name 发布部门名称
     */
    private String deptName;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

}
