package com.concise.gen.correctionlabeldictionary.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionlabeldictionary.entity.CorrectionLabelDictionary;
import com.concise.gen.correctionlabeldictionary.param.CorrectionLabelDictionaryParam;
import java.util.List;

/**
 * 标签关联字典值表service接口
 *
 * <AUTHOR>
 * @date 2022-03-08 22:24:16
 */
public interface CorrectionLabelDictionaryService extends IService<CorrectionLabelDictionary> {

    /**
     * 查询标签关联字典值表
     *
     * <AUTHOR>
     * @date 2022-03-08 22:24:16
     */
    PageResult<CorrectionLabelDictionary> page(CorrectionLabelDictionaryParam correctionLabelDictionaryParam);

    /**
     * 标签关联字典值表列表
     *
     * <AUTHOR>
     * @date 2022-03-08 22:24:16
     */
    List<CorrectionLabelDictionary> list(CorrectionLabelDictionaryParam correctionLabelDictionaryParam);

    /**
     * 添加标签关联字典值表
     *
     * <AUTHOR>
     * @date 2022-03-08 22:24:16
     */
    void add(CorrectionLabelDictionaryParam correctionLabelDictionaryParam);

    /**
     * 删除标签关联字典值表
     *
     * <AUTHOR>
     * @date 2022-03-08 22:24:16
     */
    void delete(CorrectionLabelDictionaryParam correctionLabelDictionaryParam);

    /**
     * 编辑标签关联字典值表
     *
     * <AUTHOR>
     * @date 2022-03-08 22:24:16
     */
    void edit(CorrectionLabelDictionaryParam correctionLabelDictionaryParam);

    /**
     * 查看标签关联字典值表
     *
     * <AUTHOR>
     * @date 2022-03-08 22:24:16
     */
     CorrectionLabelDictionary detail(CorrectionLabelDictionaryParam correctionLabelDictionaryParam);
}
