package com.concise.gen.correctionestimateportrayaldetail.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimateportrayaldetail.entity.CorrectionEstimatePortrayalDetail;
import com.concise.gen.correctionestimateportrayaldetail.param.CorrectionEstimatePortrayalDetailParam;

import java.util.List;

/**
 * 画像明细service接口
 *
 * <AUTHOR>
 * @date 2022-11-30 19:58:01
 */
public interface CorrectionEstimatePortrayalDetailService extends IService<CorrectionEstimatePortrayalDetail> {

    /**
     * 查询画像明细
     *
     * <AUTHOR>
     * @date 2022-11-30 19:58:01
     */
    PageResult<CorrectionEstimatePortrayalDetail> page(CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam);

    /**
     * 画像明细列表
     *
     * <AUTHOR>
     * @date 2022-11-30 19:58:01
     */
    List<CorrectionEstimatePortrayalDetail> list(CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam);

    /**
     * 添加画像明细
     *
     * <AUTHOR>
     * @date 2022-11-30 19:58:01
     */
    void add(CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam);

    /**
     * 删除画像明细
     *
     * <AUTHOR>
     * @date 2022-11-30 19:58:01
     */
    void delete(CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam);

    /**
     * 编辑画像明细
     *
     * <AUTHOR>
     * @date 2022-11-30 19:58:01
     */
    void edit(CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam);

    /**
     * 查看画像明细
     *
     * <AUTHOR>
     * @date 2022-11-30 19:58:01
     */
    CorrectionEstimatePortrayalDetail detail(CorrectionEstimatePortrayalDetailParam correctionEstimatePortrayalDetailParam);


    List<CorrectionEstimatePortrayalDetail> listByPortrayalId(String portrayalId, Integer type);
}
