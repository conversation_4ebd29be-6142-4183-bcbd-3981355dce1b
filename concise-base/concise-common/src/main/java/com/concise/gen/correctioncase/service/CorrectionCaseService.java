package com.concise.gen.correctioncase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctioncase.entity.CorrectionCase;
import com.concise.gen.correctioncase.param.CorrectionCaseParam;
import java.util.List;

/**
 * 案例库service接口
 *
 * <AUTHOR>
 * @date 2022-03-14 17:18:03
 */
public interface CorrectionCaseService extends IService<CorrectionCase> {

    /**
     * 查询案例库
     *
     * <AUTHOR>
     * @date 2022-03-14 17:18:03
     */
    PageResult<CorrectionCase> page(CorrectionCaseParam correctionCaseParam);

    /**
     * 案例库列表
     *
     * <AUTHOR>
     * @date 2022-03-14 17:18:03
     */
    List<CorrectionCase> list(CorrectionCaseParam correctionCaseParam);

    /**
     * 添加案例库
     *
     * <AUTHOR>
     * @date 2022-03-14 17:18:03
     */
    void add(CorrectionCaseParam correctionCaseParam);

    /**
     * 删除案例库
     *
     * <AUTHOR>
     * @date 2022-03-14 17:18:03
     */
    void delete(CorrectionCaseParam correctionCaseParam);

    /**
     * 编辑案例库
     *
     * <AUTHOR>
     * @date 2022-03-14 17:18:03
     */
    void edit(CorrectionCaseParam correctionCaseParam);

    /**
     * 查看案例库
     *
     * <AUTHOR>
     * @date 2022-03-14 17:18:03
     */
     CorrectionCase detail(CorrectionCaseParam correctionCaseParam);

    /**
     * 根据社区矫正人员标签查找匹配的案例
     *
     * <AUTHOR>
     * @date 2022-03-14 17:18:03
     */
    List<CorrectionCase> findBySqjzryId(String sqjzryId);
}
