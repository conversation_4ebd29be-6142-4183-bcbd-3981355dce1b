package com.concise.gen.correctionlabelmanage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionlabelmanage.entity.CorrectionLabelManage;
import com.concise.gen.correctionlabelmanage.param.CorrectionLabelManageParam;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 标签管理service接口
 *
 * <AUTHOR>
 * @date 2022-03-03 09:48:05
 */
public interface CorrectionLabelManageService extends IService<CorrectionLabelManage> {

    /**
     * 查询标签管理
     *
     * <AUTHOR>
     * @date 2022-03-03 09:48:05
     */
    Map<String,Object> page(CorrectionLabelManageParam correctionLabelManageParam, HttpServletRequest req);

    /**
     * 标签管理列表
     *
     * <AUTHOR>
     * @date 2022-03-03 09:48:05
     */
    List<CorrectionLabelManage> list(CorrectionLabelManageParam correctionLabelManageParam);

    /**
     * 添加标签管理
     *
     * <AUTHOR>
     * @date 2022-03-03 09:48:05
     */
    void add(CorrectionLabelManageParam correctionLabelManageParam);

    /**
     * 删除标签管理
     *
     * <AUTHOR>
     * @date 2022-03-03 09:48:05
     */
    void delete(CorrectionLabelManageParam correctionLabelManageParam);

    /**
     * 编辑标签管理
     *
     * <AUTHOR>
     * @date 2022-03-03 09:48:05
     */
    boolean edit(CorrectionLabelManageParam correctionLabelManageParam);

    /**
     * 查看标签管理
     *
     * <AUTHOR>
     * @date 2022-03-03 09:48:05
     */
     CorrectionLabelManage detail(CorrectionLabelManageParam correctionLabelManageParam);
}
