package com.concise.gen.portraitImage.correctionassesanswer.param;

import com.concise.common.pojo.base.param.BaseParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 评估管理--评估人员评估明细答案参数类
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:45
*/
@Data
public class CorrectionAssesAnswerParam extends BaseParam {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 评估人员评估明细id(correction_asses_person_dtl.ID)
     */
    @ApiModelProperty(value = "评估人员评估明细id(correction_asses_person_dtl.ID)")
    private String dtlId;

    /**
     * 问题id
     */
    @ApiModelProperty(value = "问题id")
    private String questionId;

    /**
     * 答案(选项)ids
     */
    @ApiModelProperty(value = "答案(选项)ids")
    private String answerIds;

}
