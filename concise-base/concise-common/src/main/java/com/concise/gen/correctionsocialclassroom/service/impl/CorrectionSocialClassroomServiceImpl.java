package com.concise.gen.correctionsocialclassroom.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.param.SysFileInfoParam;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionsocialclassroom.entity.CorrectionSocialClassroom;
import com.concise.gen.correctionsocialclassroom.enums.CorrectionSocialClassroomExceptionEnum;
import com.concise.gen.correctionsocialclassroom.mapper.CorrectionSocialClassroomMapper;
import com.concise.gen.correctionsocialclassroom.param.CorrectionSocialClassroomParam;
import com.concise.gen.correctionsocialclassroom.service.CorrectionSocialClassroomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 社矫大讲堂service接口实现类
 *
 * <AUTHOR>
 * @date 2022-03-10 17:45:12
 */
@Service
public class CorrectionSocialClassroomServiceImpl extends ServiceImpl<CorrectionSocialClassroomMapper, CorrectionSocialClassroom> implements CorrectionSocialClassroomService {

    @Autowired
    private SysFileInfoService sysFileInfoService;

    @Override
    public PageResult<CorrectionSocialClassroom> page(CorrectionSocialClassroomParam correctionSocialClassroomParam) {
        QueryWrapper<CorrectionSocialClassroom> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionSocialClassroomParam)) {

            // 根据名称 查询
            if (ObjectUtil.isNotEmpty(correctionSocialClassroomParam.getName())) {
                queryWrapper.lambda().like(CorrectionSocialClassroom::getName, correctionSocialClassroomParam.getName());
            }
            // 根据来源 查询
            if (ObjectUtil.isNotEmpty(correctionSocialClassroomParam.getFromSource())) {
                queryWrapper.lambda().like(CorrectionSocialClassroom::getFromSource, correctionSocialClassroomParam.getFromSource());
            }
            // 根据视频分类 查询
            if (ObjectUtil.isNotEmpty(correctionSocialClassroomParam.getType())) {
                queryWrapper.lambda().eq(CorrectionSocialClassroom::getType, correctionSocialClassroomParam.getType());
            }

            // 模块 查询
            if (ObjectUtil.isNotEmpty(correctionSocialClassroomParam.getModule())) {
                queryWrapper.lambda().eq(CorrectionSocialClassroom::getModule, correctionSocialClassroomParam.getModule());
            }
        }
        queryWrapper.lambda().eq(CorrectionSocialClassroom::getDelFlag, 0);
        queryWrapper.orderByDesc("create_time");
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionSocialClassroom> list(CorrectionSocialClassroomParam correctionSocialClassroomParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionSocialClassroomParam correctionSocialClassroomParam) {
        CorrectionSocialClassroom correctionSocialClassroom = new CorrectionSocialClassroom();
        BeanUtil.copyProperties(correctionSocialClassroomParam, correctionSocialClassroom);
        this.save(correctionSocialClassroom);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionSocialClassroomParam correctionSocialClassroomParam) {
        CorrectionSocialClassroom correctionSocialClassroom = this.queryCorrectionSocialClassroom(correctionSocialClassroomParam);
        BeanUtil.copyProperties(correctionSocialClassroomParam, correctionSocialClassroom);
        correctionSocialClassroom.setDelFlag(1);
        this.updateById(correctionSocialClassroom);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionSocialClassroomParam correctionSocialClassroomParam) {
        CorrectionSocialClassroom correctionSocialClassroom = this.queryCorrectionSocialClassroom(correctionSocialClassroomParam);
        BeanUtil.copyProperties(correctionSocialClassroomParam, correctionSocialClassroom);
        this.updateById(correctionSocialClassroom);
    }

    @Override
    public CorrectionSocialClassroom detail(CorrectionSocialClassroomParam correctionSocialClassroomParam) {
        return this.queryCorrectionSocialClassroom(correctionSocialClassroomParam);
    }

    /**
     * 获取社矫大讲堂
     *
     * <AUTHOR>
     * @date 2022-03-10 17:45:12
     */
    private CorrectionSocialClassroom queryCorrectionSocialClassroom(CorrectionSocialClassroomParam correctionSocialClassroomParam) {
        CorrectionSocialClassroom correctionSocialClassroom = this.getById(correctionSocialClassroomParam.getId());
        if (ObjectUtil.isNull(correctionSocialClassroom)) {
            throw new ServiceException(CorrectionSocialClassroomExceptionEnum.NOT_EXIST);
        }
        //查找视频，返回
        SysFileInfoParam p = null;
        if (ObjectUtil.isNotEmpty(correctionSocialClassroom.getFileId())) {
            List<SysFileInfoParam> fileInfoList= new ArrayList<SysFileInfoParam>();
            for (String fileId : correctionSocialClassroom.getFileId().split(",")) {
                SysFileInfo file = sysFileInfoService.getById(fileId);
                if (null != file) {
                    p = new SysFileInfoParam();
                    p.setUid(String.valueOf(file.getId()));
                    p.setName(file.getFileOriginName());
                    p.setUrl(file.getFilePath());
                    fileInfoList.add(p);
                }
            }
            correctionSocialClassroom.setFileList(fileInfoList);
        }
        //查找封面，返回
        if (ObjectUtil.isNotEmpty(correctionSocialClassroom.getImgId())) {
            List<SysFileInfoParam> imgList= new ArrayList<SysFileInfoParam>();
            for (String fileId : correctionSocialClassroom.getImgId().split(",")) {
                SysFileInfo file = sysFileInfoService.getById(fileId);
                if (null != file) {
                    p = new SysFileInfoParam();
                    p.setUid(String.valueOf(file.getId()));
                    p.setName(file.getFileOriginName());
                    p.setUrl(file.getFilePath());
                    imgList.add(p);
                }
            }
            correctionSocialClassroom.setImgList(imgList);
        }
        return correctionSocialClassroom;
    }
}
