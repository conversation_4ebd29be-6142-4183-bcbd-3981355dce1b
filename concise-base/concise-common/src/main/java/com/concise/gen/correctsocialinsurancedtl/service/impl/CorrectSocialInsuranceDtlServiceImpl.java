package com.concise.gen.correctsocialinsurancedtl.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctsocialinsurancedtl.entity.CorrectSocialInsuranceDtl;
import com.concise.gen.correctsocialinsurancedtl.enums.CorrectSocialInsuranceDtlExceptionEnum;
import com.concise.gen.correctsocialinsurancedtl.mapper.CorrectSocialInsuranceDtlMapper;
import com.concise.gen.correctsocialinsurancedtl.param.CorrectSocialInsuranceDtlParam;
import com.concise.gen.correctsocialinsurancedtl.service.CorrectSocialInsuranceDtlService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 历史社保明细表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-06-07 15:08:51
 */
@Service
public class CorrectSocialInsuranceDtlServiceImpl extends ServiceImpl<CorrectSocialInsuranceDtlMapper, CorrectSocialInsuranceDtl> implements CorrectSocialInsuranceDtlService {

    @Override
    public PageResult<CorrectSocialInsuranceDtl> page(CorrectSocialInsuranceDtlParam correctSocialInsuranceDtlParam) {
        QueryWrapper<CorrectSocialInsuranceDtl> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctSocialInsuranceDtlParam)) {

            // 根据历史社保主表id 查询
            if (ObjectUtil.isNotEmpty(correctSocialInsuranceDtlParam.getSocialInsuranceId())) {
                queryWrapper.lambda().eq(CorrectSocialInsuranceDtl::getSocialInsuranceId, correctSocialInsuranceDtlParam.getSocialInsuranceId());
            }
            // 根据矫正对象id 查询
            if (ObjectUtil.isNotEmpty(correctSocialInsuranceDtlParam.getSqjzryId())) {
                queryWrapper.lambda().eq(CorrectSocialInsuranceDtl::getSqjzryId, correctSocialInsuranceDtlParam.getSqjzryId());
            }
        }
        queryWrapper.lambda().orderByDesc(CorrectSocialInsuranceDtl::getMonth);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectSocialInsuranceDtl> list(CorrectSocialInsuranceDtlParam correctSocialInsuranceDtlParam) {
        return this.list();
    }

    @Override
    public void add(CorrectSocialInsuranceDtlParam correctSocialInsuranceDtlParam) {
        CorrectSocialInsuranceDtl correctSocialInsuranceDtl = new CorrectSocialInsuranceDtl();
        BeanUtil.copyProperties(correctSocialInsuranceDtlParam, correctSocialInsuranceDtl);
        this.save(correctSocialInsuranceDtl);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectSocialInsuranceDtlParam correctSocialInsuranceDtlParam) {
        this.removeById(correctSocialInsuranceDtlParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectSocialInsuranceDtlParam correctSocialInsuranceDtlParam) {
        CorrectSocialInsuranceDtl correctSocialInsuranceDtl = this.queryCorrectSocialInsuranceDtl(correctSocialInsuranceDtlParam);
        BeanUtil.copyProperties(correctSocialInsuranceDtlParam, correctSocialInsuranceDtl);
        this.updateById(correctSocialInsuranceDtl);
    }

    @Override
    public CorrectSocialInsuranceDtl detail(CorrectSocialInsuranceDtlParam correctSocialInsuranceDtlParam) {
        return this.queryCorrectSocialInsuranceDtl(correctSocialInsuranceDtlParam);
    }

    /**
     * 获取历史社保明细表
     *
     * <AUTHOR>
     * @date 2022-06-07 15:08:51
     */
    private CorrectSocialInsuranceDtl queryCorrectSocialInsuranceDtl(CorrectSocialInsuranceDtlParam correctSocialInsuranceDtlParam) {
        CorrectSocialInsuranceDtl correctSocialInsuranceDtl = this.getById(correctSocialInsuranceDtlParam.getId());
        if (ObjectUtil.isNull(correctSocialInsuranceDtl)) {
            throw new ServiceException(CorrectSocialInsuranceDtlExceptionEnum.NOT_EXIST);
        }
        return correctSocialInsuranceDtl;
    }
}
