package com.concise.gen.correctionestimatereport.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionestimatereport.dto.ReportDetailDTO;
import com.concise.gen.correctionestimatereport.entity.CorrectionEstimateReport;
import com.concise.gen.correctionestimatereport.param.CorrectionEstimateReportParam;
import java.util.List;
import java.util.Set;

/**
 * 评估报告service接口
 *
 * <AUTHOR>
 * @date 2023-01-09 10:57:38
 */
public interface CorrectionEstimateReportService extends IService<CorrectionEstimateReport> {

    /**
     * 查询评估报告
     *
     * <AUTHOR>
     * @date 2023-01-09 10:57:38
     */
    PageResult<CorrectionEstimateReport> page(CorrectionEstimateReportParam correctionEstimateReportParam, Set<String> org);

    /**
     * 评估报告列表
     *
     * <AUTHOR>
     * @date 2023-01-09 10:57:38
     */
    List<CorrectionEstimateReport> list(CorrectionEstimateReportParam correctionEstimateReportParam);

    /**
     * 添加评估报告
     *
     * <AUTHOR>
     * @date 2023-01-09 10:57:38
     */
    void add(CorrectionEstimateReportParam correctionEstimateReportParam);

    /**
     * 删除评估报告
     *
     * <AUTHOR>
     * @date 2023-01-09 10:57:38
     */
    void delete(CorrectionEstimateReportParam correctionEstimateReportParam);

    /**
     * 编辑评估报告
     *
     * <AUTHOR>
     * @date 2023-01-09 10:57:38
     */
    void edit(CorrectionEstimateReportParam correctionEstimateReportParam);

    /**
     * 查看评估报告
     *
     * <AUTHOR>
     * @date 2023-01-09 10:57:38
     */
    ReportDetailDTO detail(CorrectionEstimateReportParam correctionEstimateReportParam);

    /**
     * 初始化评估报告数据，每月月初一次
     */
    void initCorrectionEstimateReport();

    /**
     * 每天更新评估报告
     */
    void updateCorrectionEstimateReport(CorrectionEstimateReport correctionEstimateReport);
}
