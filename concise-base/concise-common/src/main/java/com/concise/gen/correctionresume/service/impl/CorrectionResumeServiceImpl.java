package com.concise.gen.correctionresume.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionresume.entity.CorrectionResume;
import com.concise.gen.correctionresume.mapper.CorrectionResumeMapper;
import com.concise.gen.correctionresume.param.CorrectionResumeParam;
import com.concise.gen.correctionresume.service.CorrectionResumeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 个人简历service接口实现类
 *
 * <AUTHOR>
 * @date 2022-02-22 16:07:54
 */
@DS("slave")
@Service
public class CorrectionResumeServiceImpl extends ServiceImpl<CorrectionResumeMapper, CorrectionResume> implements CorrectionResumeService {

    @Override
    public PageResult<CorrectionResume> page(CorrectionResumeParam correctionResumeParam) {
        QueryWrapper<CorrectionResume> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionResumeParam)) {

            // 根据社区矫正人员标识 查询
            if (ObjectUtil.isNotEmpty(correctionResumeParam.getPid())) {
                queryWrapper.lambda().eq
                        (CorrectionResume::getPid, correctionResumeParam.getPid());
            }
            // 根据起时 查询
            if (ObjectUtil.isNotEmpty(correctionResumeParam.getQs())) {
                queryWrapper.lambda().eq
                        (CorrectionResume::getQs, correctionResumeParam.getQs());
            }
            // 根据止日 查询
            if (ObjectUtil.isNotEmpty(correctionResumeParam.getZr())) {
                queryWrapper.lambda().eq
                        (CorrectionResume::getZr, correctionResumeParam.getZr());
            }
            // 根据所在单位 查询
            if (ObjectUtil.isNotEmpty(correctionResumeParam.getSzdw())) {
                queryWrapper.lambda().eq
                        (CorrectionResume::getSzdw, correctionResumeParam.getSzdw());
            }
            // 根据职务 查询
            if (ObjectUtil.isNotEmpty(correctionResumeParam.getZw())) {
                queryWrapper.lambda().eq
                        (CorrectionResume::getZw, correctionResumeParam.getZw());
            }
            // 根据职务Name 查询
            if (ObjectUtil.isNotEmpty(correctionResumeParam.getZwName())) {
                queryWrapper.lambda().eq
                        (CorrectionResume::getZwName, correctionResumeParam.getZwName());
            }
            // 根据是否删除（0：未删除，1删除） 查询
            if (ObjectUtil.isNotEmpty(correctionResumeParam.getDelFlag())) {
                queryWrapper.lambda().eq
                        (CorrectionResume::getDelFlag, correctionResumeParam.getDelFlag());
            }
        }
        queryWrapper.orderByDesc("zr");
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionResume> list(CorrectionResumeParam correctionResumeParam) {
        return this.list();
    }

}
