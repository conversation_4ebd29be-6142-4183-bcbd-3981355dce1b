package com.concise.gen.correctionlabelthirdpart.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 标签管理-第三方对接
 *
 * <AUTHOR>
 * @date 2023-04-23 14:35:27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_label_third_part")
public class CorrectionLabelThirdPart extends BaseEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 标签id
     */
    private String labelId;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 性质
     */
    private String naturl;

    /**
     * 影响时间
     */
    private String affectDate;

    /**
     * 分值
     */
    private String score;

    /**
     * 是否系统内置 0是，1否
     */
    private String builtFlag;

    /**
     * 状态 0启用，1禁用
     */
    private String status;

    /**
     * 是否分类 0是，1否
     */
    private String classify;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除状态 0-正常 1-删除
     */
    private Integer delFlag;

}
