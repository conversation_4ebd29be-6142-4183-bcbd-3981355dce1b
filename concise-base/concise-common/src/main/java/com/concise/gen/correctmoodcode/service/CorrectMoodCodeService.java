package com.concise.gen.correctmoodcode.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctmoodcode.entity.CorrectMoodCode;
import com.concise.gen.correctmoodcode.param.CorrectMoodCodeParam;
import java.util.List;

/**
 * 心情码service接口
 *
 * <AUTHOR>
 * @date 2022-05-26 18:01:13
 */
public interface CorrectMoodCodeService extends IService<CorrectMoodCode> {

    /**
     * 查询心情码
     *
     * <AUTHOR>
     * @date 2022-05-26 18:01:13
     */
    PageResult<CorrectMoodCode> page(CorrectMoodCodeParam correctMoodCodeParam);

    /**
     * 心情码列表
     *
     * <AUTHOR>
     * @date 2022-05-26 18:01:13
     */
    List<CorrectMoodCode> list(CorrectMoodCodeParam correctMoodCodeParam);

    /**
     * 添加心情码
     *
     * <AUTHOR>
     * @date 2022-05-26 18:01:13
     */
    void add(CorrectMoodCodeParam correctMoodCodeParam);

    /**
     * 删除心情码
     *
     * <AUTHOR>
     * @date 2022-05-26 18:01:13
     */
    void delete(CorrectMoodCodeParam correctMoodCodeParam);

    /**
     * 编辑心情码
     *
     * <AUTHOR>
     * @date 2022-05-26 18:01:13
     */
    void edit(CorrectMoodCodeParam correctMoodCodeParam);

    /**
     * 查看心情码
     *
     * <AUTHOR>
     * @date 2022-05-26 18:01:13
     */
     CorrectMoodCode detail(CorrectMoodCodeParam correctMoodCodeParam);

    /**
     * 根据矫正对象id查询最新一次的心情码
     * @param thirdId
     * @return
     */
     String getMoodCode(String thirdId);
}
