<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.correctionabilitymanage.mapper.CorrectionAbilityManageMapper">

    <resultMap id="abilityResult" type="com.concise.gen.correctionabilitymanage.entity.CorrectionAbilityManage">
        <result column="id" property="id" />
        <result column="daily_supervision_step" property="dailySupervisionStep" />
        <result column="educational_assistance_step" property="educationalAssistanceStep" />
        <result column="psychological_correction_step" property="psychologicalCorrectionStep" />
        <result column="correct_phase" property="correctPhase" />
        <result column="label_ids" property="labelIds" />
        <result column="label_names" property="labelNames" />
        <result column="create_time" property="createTime" />
        <result column="source" property="source" />
    </resultMap>

    <select id="page" resultMap="abilityResult">
        select am.id, am.daily_supervision_step, am.educational_assistance_step, am.psychological_correction_step,
            am.correct_phase, am.create_time, am.source,
         (
            SELECT GROUP_CONCAT(clm.label SEPARATOR ',') FROM correction_label_manage clm WHERE clm.id in
                (SELECT al.lable_id FROM  correction_ability_lable al WHERE al.ability_id = am.id)
        ) AS label_names
        FROM correction_ability_manage am ${ew.customSqlSegment}
            <if test="labelIds != null and labelIds != ''">
                and am.id in(select ability_id from correction_ability_lable where lable_id in
                <foreach collection="labelIds" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                )
            </if>
         ORDER BY am.create_time desc
    </select>

    <select id="findByCorrectPhase" resultMap="abilityResult">
        select am.id, am.daily_supervision_step, am.educational_assistance_step, am.psychological_correction_step,
        am.correct_phase, am.source,
        (SELECT GROUP_CONCAT(cal.lable_id SEPARATOR ',') FROM  correction_ability_lable cal WHERE cal.ability_id = am.id) as label_ids,
        (
            SELECT GROUP_CONCAT(clm.label SEPARATOR ',') FROM correction_label_manage clm WHERE clm.id in
                (SELECT al.lable_id FROM  correction_ability_lable al WHERE al.ability_id = am.id)
        ) AS label_names
        FROM correction_ability_manage am where del_flag = 0 ORDER BY LENGTH(label_ids) desc
    </select>
</mapper>
