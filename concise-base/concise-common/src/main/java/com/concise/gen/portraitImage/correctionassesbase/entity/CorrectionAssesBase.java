package com.concise.gen.portraitImage.correctionassesbase.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.portraitImage.correctionassesmembers.entity.CorrectionAssesMembers;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.List;

/**
 * 评估管理--基本信息
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_asses_base")
public class CorrectionAssesBase extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 任务名称
     */
    private String title;

    /**
     * 量表id
     */
    private String scaleBaseId;

    /**
     * 量表名称
     */
    private String scaleBaseName;

    /**
     * 测评阶段：0：入矫 1: 在矫
     */
    private Integer phase;

    /**
     * 测评周期：0：单词 1：一月一次
     */
    private Integer cycle;

    /**
     * 测评开始时间，格式: yyyyMMdd
     */
    private String startTime;

    /**
     * 测评结束时间，格式: yyyyMMdd
     */
    private String endTime;

    /**
     * 测评范围：0：部门  1：人员 (需求变更，同时可选部门和人员，该字段失效)
     */
    private Integer assesScope;

    /**
     * 是否临期提醒：0：否  1：是
     */
    private Integer needWarn;

    /**
     * 临期天数
     */
    private Integer dayNum;

    /**
     * 进度--总次数
     */
    private Integer totalNum;

    /**
     * 进度--完成次数
     */
    private Integer completeNum;

    /**
     * 状态：0：未开始 1：进行中 2：已完成 3：停止
     */
    private Integer status;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 测评开始时间_begin
     */
    @TableField(exist = false)
    private String startTime_begin;

    /**
     * 测评开始时间_end
     */
    @TableField(exist = false)
    private String startTime_end;

    /**
     * 机构ids
     */
    @TableField(exist = false)
    private List<String> deptIds;

    /**
     * 矫正对象集合
     */
    @TableField(exist = false)
    private List<CorrectionObjectInformation> sqjzryList;

    /**
     * 矫正对象集合（查看时使用）
     */
    @TableField(exist = false)
    private List<CorrectionAssesMembers> personList;

}
