package com.concise.gen.correctionlabelthirdpart.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionlabelthirdpart.entity.CorrectionLabelThirdPart;
import com.concise.gen.correctionlabelthirdpart.enums.CorrectionLabelThirdPartExceptionEnum;
import com.concise.gen.correctionlabelthirdpart.mapper.CorrectionLabelThirdPartMapper;
import com.concise.gen.correctionlabelthirdpart.param.CorrectionLabelThirdPartParam;
import com.concise.gen.correctionlabelthirdpart.service.CorrectionLabelThirdPartService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 标签管理-第三方对接service接口实现类
 *
 * <AUTHOR>
 * @date 2023-04-23 14:35:27
 */
@Service
public class CorrectionLabelThirdPartServiceImpl extends ServiceImpl<CorrectionLabelThirdPartMapper, CorrectionLabelThirdPart> implements CorrectionLabelThirdPartService {

    @Override
    public PageResult<CorrectionLabelThirdPart> page(CorrectionLabelThirdPartParam correctionLabelThirdPartParam) {
        QueryWrapper<CorrectionLabelThirdPart> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionLabelThirdPartParam)) {

            // 根据标签id 查询
            if (ObjectUtil.isNotEmpty(correctionLabelThirdPartParam.getLabelId())) {
                queryWrapper.lambda().eq(CorrectionLabelThirdPart::getLabelId, correctionLabelThirdPartParam.getLabelId());
            }
            // 根据标签名称 查询
            if (ObjectUtil.isNotEmpty(correctionLabelThirdPartParam.getName())) {
                queryWrapper.lambda().eq(CorrectionLabelThirdPart::getName, correctionLabelThirdPartParam.getName());
            }
            // 根据性质 查询
            if (ObjectUtil.isNotEmpty(correctionLabelThirdPartParam.getNaturl())) {
                queryWrapper.lambda().eq(CorrectionLabelThirdPart::getNaturl, correctionLabelThirdPartParam.getNaturl());
            }
            // 根据影响时间 查询
            if (ObjectUtil.isNotEmpty(correctionLabelThirdPartParam.getAffectDate())) {
                queryWrapper.lambda().eq(CorrectionLabelThirdPart::getAffectDate, correctionLabelThirdPartParam.getAffectDate());
            }
            // 根据分值 查询
            if (ObjectUtil.isNotEmpty(correctionLabelThirdPartParam.getScore())) {
                queryWrapper.lambda().eq(CorrectionLabelThirdPart::getScore, correctionLabelThirdPartParam.getScore());
            }
            // 根据是否系统内置 0是，1否 查询
            if (ObjectUtil.isNotEmpty(correctionLabelThirdPartParam.getBuiltFlag())) {
                queryWrapper.lambda().eq(CorrectionLabelThirdPart::getBuiltFlag, correctionLabelThirdPartParam.getBuiltFlag());
            }
            // 根据状态 0启用，1禁用 查询
            if (ObjectUtil.isNotEmpty(correctionLabelThirdPartParam.getStatus())) {
                queryWrapper.lambda().eq(CorrectionLabelThirdPart::getStatus, correctionLabelThirdPartParam.getStatus());
            }
            // 根据是否分类 0是，1否 查询
            if (ObjectUtil.isNotEmpty(correctionLabelThirdPartParam.getClassify())) {
                queryWrapper.lambda().eq(CorrectionLabelThirdPart::getClassify, correctionLabelThirdPartParam.getClassify());
            }
            // 根据备注 查询
            if (ObjectUtil.isNotEmpty(correctionLabelThirdPartParam.getRemark())) {
                queryWrapper.lambda().eq(CorrectionLabelThirdPart::getRemark, correctionLabelThirdPartParam.getRemark());
            }
            // 根据删除状态 0-正常 1-删除 查询
            if (ObjectUtil.isNotEmpty(correctionLabelThirdPartParam.getDelFlag())) {
                queryWrapper.lambda().eq(CorrectionLabelThirdPart::getDelFlag, correctionLabelThirdPartParam.getDelFlag());
            }
        }
        return new PageResult<>(this.page(new Page<>(correctionLabelThirdPartParam.getPageNo(),correctionLabelThirdPartParam.getPageSize()), queryWrapper));
    }

    @Override
    public List<CorrectionLabelThirdPart> list(CorrectionLabelThirdPartParam correctionLabelThirdPartParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionLabelThirdPartParam correctionLabelThirdPartParam) {
        CorrectionLabelThirdPart correctionLabelThirdPart = new CorrectionLabelThirdPart();
        BeanUtil.copyProperties(correctionLabelThirdPartParam, correctionLabelThirdPart);
        this.save(correctionLabelThirdPart);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionLabelThirdPartParam correctionLabelThirdPartParam) {
        this.removeById(correctionLabelThirdPartParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionLabelThirdPartParam correctionLabelThirdPartParam) {
        CorrectionLabelThirdPart correctionLabelThirdPart = new CorrectionLabelThirdPart();
        BeanUtil.copyProperties(correctionLabelThirdPartParam, correctionLabelThirdPart);
        LambdaUpdateWrapper<CorrectionLabelThirdPart> lambdaQueryWrapper = new LambdaUpdateWrapper();
        lambdaQueryWrapper.eq(CorrectionLabelThirdPart::getLabelId,correctionLabelThirdPartParam.getLabelId());
        this.update(correctionLabelThirdPart,lambdaQueryWrapper);
    }

    @Override
    public CorrectionLabelThirdPart detail(CorrectionLabelThirdPartParam correctionLabelThirdPartParam) {
        return this.queryCorrectionLabelThirdPart(correctionLabelThirdPartParam);
    }

    /**
     * 获取标签管理-第三方对接
     *
     * <AUTHOR>
     * @date 2023-04-23 14:35:27
     */
    private CorrectionLabelThirdPart queryCorrectionLabelThirdPart(CorrectionLabelThirdPartParam correctionLabelThirdPartParam) {
        CorrectionLabelThirdPart correctionLabelThirdPart = this.getById(correctionLabelThirdPartParam.getId());
        if (ObjectUtil.isNull(correctionLabelThirdPart)) {
            throw new ServiceException(CorrectionLabelThirdPartExceptionEnum.NOT_EXIST);
        }
        return correctionLabelThirdPart;
    }
}
