package com.concise.gen.correctionrelationships.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionrelationships.entity.CorrectionRelationships;
import com.concise.gen.correctionrelationships.mapper.CorrectionRelationshipsMapper;
import com.concise.gen.correctionrelationships.param.CorrectionRelationshipsParam;
import com.concise.gen.correctionrelationships.service.CorrectionRelationshipsService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 家庭成员service接口实现类
 *
 * <AUTHOR>
 * @date 2022-02-22 16:07:50
 */
@DS("slave")
@Service
public class CorrectionRelationshipsServiceImpl extends ServiceImpl<CorrectionRelationshipsMapper, CorrectionRelationships> implements CorrectionRelationshipsService {

    @Override
    public PageResult<CorrectionRelationships> page(CorrectionRelationshipsParam correctionRelationshipsParam) {
        QueryWrapper<CorrectionRelationships> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionRelationshipsParam)) {

            // 根据社区矫正人员标识 查询
            if (ObjectUtil.isNotEmpty(correctionRelationshipsParam.getPid())) {
                queryWrapper.lambda().eq
                        (CorrectionRelationships::getPid, correctionRelationshipsParam.getPid());
            }
            // 根据称谓 查询
            if (ObjectUtil.isNotEmpty(correctionRelationshipsParam.getGx())) {
                queryWrapper.lambda().eq
                        (CorrectionRelationships::getGx, correctionRelationshipsParam.getGx());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionRelationshipsParam.getXm())) {
                queryWrapper.lambda().eq
                        (CorrectionRelationships::getXm, correctionRelationshipsParam.getXm());
            }
            // 根据所在单位 查询
            if (ObjectUtil.isNotEmpty(correctionRelationshipsParam.getSzdw())) {
                queryWrapper.lambda().eq
                        (CorrectionRelationships::getSzdw, correctionRelationshipsParam.getSzdw());
            }
            // 根据居住地址 查询
            if (ObjectUtil.isNotEmpty(correctionRelationshipsParam.getJtzz())) {
                queryWrapper.lambda().eq
                        (CorrectionRelationships::getJtzz, correctionRelationshipsParam.getJtzz());
            }
            // 根据联系电话 查询
            if (ObjectUtil.isNotEmpty(correctionRelationshipsParam.getLxdh())) {
                queryWrapper.lambda().eq
                        (CorrectionRelationships::getLxdh, correctionRelationshipsParam.getLxdh());
            }
            // 根据称谓name 查询
            if (ObjectUtil.isNotEmpty(correctionRelationshipsParam.getGxName())) {
                queryWrapper.lambda().eq
                        (CorrectionRelationships::getGxName, correctionRelationshipsParam.getGxName());
            }
            // 根据是否删除（0：未删除，1删除） 查询
            if (ObjectUtil.isNotEmpty(correctionRelationshipsParam.getDelFlag())) {
                queryWrapper.lambda().eq
                        (CorrectionRelationships::getDelFlag, correctionRelationshipsParam.getDelFlag());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionRelationships> list(CorrectionRelationshipsParam correctionRelationshipsParam) {
        return this.list(new QueryWrapper<CorrectionRelationships>().lambda()
                .eq(CorrectionRelationships::getPid, correctionRelationshipsParam.getPid()));
    }





}
