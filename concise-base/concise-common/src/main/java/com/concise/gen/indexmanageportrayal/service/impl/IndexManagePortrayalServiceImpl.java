package com.concise.gen.indexmanageportrayal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.indexmanage.enums.IndexManageExceptionEnum;
import com.concise.gen.indexmanageportrayal.entity.IndexManagePortrayal;
import com.concise.gen.indexmanageportrayal.mapper.IndexManagePortrayalMapper;
import com.concise.gen.indexmanageportrayal.param.IndexManagePortrayalParam;
import com.concise.gen.indexmanageportrayal.service.IndexManagePortrayalService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 指标管理信息表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-05-12 10:02:19
 */
@Service
public class IndexManagePortrayalServiceImpl extends ServiceImpl<IndexManagePortrayalMapper, IndexManagePortrayal> implements IndexManagePortrayalService {

    @Override
    public PageResult<IndexManagePortrayal> page(IndexManagePortrayalParam indexManagePortrayalParam) {
        QueryWrapper<IndexManagePortrayal> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(indexManagePortrayalParam)) {

            // 根据指标分类 查询
            if (ObjectUtil.isNotEmpty(indexManagePortrayalParam.getIndexType())) {
                queryWrapper.lambda().like(IndexManagePortrayal::getIndexType, indexManagePortrayalParam.getIndexType());
            }
            // 根据删除状态 查询
            if (ObjectUtil.isNotEmpty(indexManagePortrayalParam.getDelFlag())) {
                queryWrapper.lambda().eq(IndexManagePortrayal::getDelFlag, indexManagePortrayalParam.getDelFlag());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<IndexManagePortrayal> list(IndexManagePortrayalParam indexManagePortrayalParam) {
        QueryWrapper<IndexManagePortrayal> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(indexManagePortrayalParam)) {

            // 根据指标分类 查询
            if (ObjectUtil.isNotEmpty(indexManagePortrayalParam.getIndexType())) {
                queryWrapper.lambda().like(IndexManagePortrayal::getIndexType, indexManagePortrayalParam.getIndexType());
            }
        }

        queryWrapper.lambda().eq(IndexManagePortrayal::getDelFlag, "0");
        return this.list(queryWrapper);
    }

    @Override
    public void add(IndexManagePortrayalParam indexManagePortrayalParam) {
        IndexManagePortrayal indexManagePortrayal = new IndexManagePortrayal();
        BeanUtil.copyProperties(indexManagePortrayalParam, indexManagePortrayal);
        this.save(indexManagePortrayal);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(IndexManagePortrayalParam indexManagePortrayalParam) {
        IndexManagePortrayal indexManagePortrayal = new IndexManagePortrayal();
        indexManagePortrayal.setId(indexManagePortrayalParam.getId());
        indexManagePortrayal.setDelFlag("1");
        this.updateById(indexManagePortrayal);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(IndexManagePortrayalParam indexManagePortrayalParam) {
        IndexManagePortrayal indexManagePortrayal = this.queryIndexManage(indexManagePortrayalParam);
        BeanUtil.copyProperties(indexManagePortrayalParam, indexManagePortrayal);
        this.updateById(indexManagePortrayal);
    }

    @Override
    public IndexManagePortrayal detail(IndexManagePortrayalParam indexManagePortrayalParam) {
        return this.queryIndexManage(indexManagePortrayalParam);
    }

    /**
     * 获取指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:19
     */
    private IndexManagePortrayal queryIndexManage(IndexManagePortrayalParam indexManagePortrayalParam) {
        IndexManagePortrayal indexManagePortrayal = this.getById(indexManagePortrayalParam.getId());
        if (ObjectUtil.isNull(indexManagePortrayal)) {
            throw new ServiceException(IndexManageExceptionEnum.NOT_EXIST);
        }
        return indexManagePortrayal;
    }
}
