package com.concise.gen.correctionplaninfomeasureeducation.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
 *
 * <AUTHOR>
 * @date 2024-11-21 16:55:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_plan_info_measure_education")
public class CorrectionPlanInfoMeasureEducation extends BaseEntity {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 矫正对象id
     */
    private String jzdxId;

    /**
     * 月份
     */
    private String month;

    /**
     * 课件分类id
     */
    private String categoryId;

    /**
     * 课件分类
     */
    private String categoryName;

}
