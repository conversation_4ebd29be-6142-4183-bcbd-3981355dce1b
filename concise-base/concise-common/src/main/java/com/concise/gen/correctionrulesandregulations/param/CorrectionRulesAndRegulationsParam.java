package com.concise.gen.correctionrulesandregulations.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.param.SysFileInfoParam;
import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
* 规章制度参数类
 *
 * <AUTHOR>
 * @date 2022-03-10 17:45:09
*/
@Data
public class CorrectionRulesAndRegulationsParam extends BaseParam {

    /**
     *
     */
    private String id;

    /**
     * 标题
     */
    private String title;

    /**
     * 发布单位（该字段废弃，前台展示发布部门）
     */
    private String releaseDeptName;

    /**
     * 类型（0_法律法规,1_技术规范,2_工作通知,3_标准规范)
     */
    private Integer releaseType;

    /**
     * 文件编号
     */
    private String fileId;

    /**
     * dept_id 发布部门
     */
    private String deptId;

    /**
     * dept_name 发布部门名称
     */
    private String deptName;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 附件集合
     */
    private List<SysFileInfoParam> fileList;
}
