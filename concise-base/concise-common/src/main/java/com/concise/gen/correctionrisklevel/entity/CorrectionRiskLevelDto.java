package com.concise.gen.correctionrisklevel.entity;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 风险等级
 *
 * <AUTHOR>
 * @date 2022-05-18 18:29:16
 */
@Data
public class CorrectionRiskLevelDto {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 矫正对象id
     */
    private String sqjzryId;

    /**
     * 姓名
     */
    private String sqjzryName;


    /**
     * 矫正机构ID
     */
    private String jzjg;

    /**
     * 矫正机构名称
     */
    private String jzjgName;

    /**
     * 风险等级(当月)
     */
    private String riskLevel;

    /**
     * 风险等级中文值
     */
    private String riskLevelText;




    /**
     * 时间
     */
    private String time;

    public String getRiskLevelText() {
        if (ObjectUtil.isNotNull(riskLevel)) {
            switch (riskLevel) {
                case "FXDJ01":
                    return "低风险";
                case "FXDJ02":
                    return "较低风险";
                case "FXDJ03":
                    return "中风险";
                case "FXDJ04":
                    return "较高风险";
                case "FXDJ05":
                    return "高风险";
                default:
                    return null;
            }
        }
        return null;
    }
}
