package com.concise.gen.correctionrisklevel.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionobjectinformation.entity.ScreenModel;
import com.concise.gen.correctionrisklevel.entity.CorrectionRiskLevel;
import com.concise.gen.correctionrisklevel.entity.CorrectionRiskLevelDto;
import com.concise.gen.correctionrisklevel.param.CorrectionRiskLevelParam;

import java.util.List;
import java.util.Set;

/**
 * 风险等级service接口
 *
 * <AUTHOR>
 * @date 2022-05-18 18:29:16
 */
public interface CorrectionRiskLevelService extends IService<CorrectionRiskLevel> {

    /**
     * 查询风险等级
     *
     * <AUTHOR>
     * @date 2022-05-18 18:29:16
     */
    PageResult<CorrectionRiskLevel> page(CorrectionRiskLevelParam correctionRiskLevelParam, Set<String> org);

    /**
     * 风险等级列表
     *
     * <AUTHOR>
     * @date 2022-05-18 18:29:16
     */
    List<CorrectionRiskLevel> list(CorrectionRiskLevelParam correctionRiskLevelParam);

    /**
     * 添加风险等级
     *
     * <AUTHOR>
     * @date 2022-05-18 18:29:16
     */
    void add(CorrectionRiskLevelParam correctionRiskLevelParam);

    /**
     * 删除风险等级
     *
     * <AUTHOR>
     * @date 2022-05-18 18:29:16
     */
    void delete(CorrectionRiskLevelParam correctionRiskLevelParam);

    /**
     * 编辑风险等级
     *
     * <AUTHOR>
     * @date 2022-05-18 18:29:16
     */
    void edit(CorrectionRiskLevelParam correctionRiskLevelParam);

    /**
     * 查看风险等级
     *
     * <AUTHOR>
     * @date 2022-05-18 18:29:16
     */
    CorrectionRiskLevel detail(CorrectionRiskLevelParam correctionRiskLevelParam);

    /**
     * 每月同步等级管理数据
     */
    void sycnData();

    /**
     * 余杭大屏风险等级统计
     *
     * @param userIds
     * @return
     */
    List<ScreenModel> riskLevel(Set<String> userIds);

    /**
     * 余杭大屏风险等级统计详情
     *
     * @param title
     * @return
     */
    List<CorrectionRiskLevel> riskLevelDetail(String title, Set<String> userIds,String name);

    Page<CorrectionRiskLevelDto> riskAssessmentPage(CorrectionRiskLevelParam correctionRiskLevelParam,Set<String> deptIds);
}
