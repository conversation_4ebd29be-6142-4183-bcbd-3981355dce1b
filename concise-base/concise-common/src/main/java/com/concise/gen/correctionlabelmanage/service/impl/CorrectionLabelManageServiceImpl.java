package com.concise.gen.correctionlabelmanage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.gen.correctionlabelmanage.entity.CorrectionLabelManage;
import com.concise.gen.correctionlabelmanage.enums.CorrectionLabelManageExceptionEnum;
import com.concise.gen.correctionlabelmanage.mapper.CorrectionLabelManageMapper;
import com.concise.gen.correctionlabelmanage.param.CorrectionLabelManageParam;
import com.concise.gen.correctionlabelmanage.service.CorrectionLabelManageService;
import com.concise.gen.correctionlabelsystem.entity.CorrectionLabelSystem;
import com.concise.gen.correctionlabelsystem.service.CorrectionLabelSystemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 标签管理service接口实现类
 *
 * <AUTHOR>
 * @date 2022-03-03 09:48:05
 */
@Service
public class CorrectionLabelManageServiceImpl extends ServiceImpl<CorrectionLabelManageMapper, CorrectionLabelManage> implements CorrectionLabelManageService {

    @Resource
    private CorrectionLabelSystemService correctionLabelSystemService;

    /**
     * 每页大小（默认10）
     */
    private static final String PAGE_SIZE_PARAM_NAME = "pageSize";

    /**
     * 第几页（从1开始）
     */
    private static final String PAGE_NO_PARAM_NAME = "pageNo";


    @Override
    public Map<String, Object> page(CorrectionLabelManageParam correctionLabelManageParam, HttpServletRequest req) {
        QueryWrapper<CorrectionLabelManage> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionLabelManageParam)) {

            // 根据标签 查询
            if (ObjectUtil.isNotEmpty(correctionLabelManageParam.getLabel())) {
                queryWrapper.lambda().like(CorrectionLabelManage::getLabelAttribute, correctionLabelManageParam.getLabel());
            }
            // 根据属性分类字典码 查询
            if (ObjectUtil.isNotEmpty(correctionLabelManageParam.getAttributeTypeCode())) {
                queryWrapper.lambda().eq(CorrectionLabelManage::getAttributeTypeCode, correctionLabelManageParam.getAttributeTypeCode());
            }
            // 根据标签属性 查询
            if (ObjectUtil.isNotEmpty(correctionLabelManageParam.getLabelAttributeCode())) {
                queryWrapper.lambda().eq(CorrectionLabelManage::getLabelAttributeCode, correctionLabelManageParam.getLabelAttributeCode());
            }
        }
        queryWrapper.lambda().eq(CorrectionLabelManage::getDelFlag, 0);
        Page<CorrectionLabelManage> page = this.baseMapper.page(new Page(1, 100000), queryWrapper);
        List<CorrectionLabelManage> records = page.getRecords();
        Map<String, Object> map = new HashMap<>();
        if (records.size() > 0) {
            //分组根据（LabelAttributeCode）
            List<CorrectionLabelManage> collect = records.stream()
                    .collect(Collectors.groupingBy(CorrectionLabelManage::getLabelAttributeCode))
                    .entrySet().stream()
                    .map(entry -> {
                        CorrectionLabelManage first = entry.getValue().get(0);
                        return new CorrectionLabelManage(first.getAttributeType(),
                                first.getAttributeTypeCode(),
                                "",
                                first.getDictNum(),
                                first.getId(),
                                first.getLabel(),
                                first.getLabelAttribute(),
                                first.getLabelAttributeCode(),
                                first.getRemark(),
                                entry.getValue(),
                                first.getLevel(),
                                first.getParentId());
                    })
                    .collect(Collectors.toList());

            collect.stream().forEach(item -> {
                List<CorrectionLabelManage> children = item.getChildren();
                if (children.size() == 0) {
                    return;
                }
                //根据level 将数组分组为2和3
                Map<Integer, List<CorrectionLabelManage>> groupedByLevel = children.stream()
                        .filter(child -> null != child.getLevel() && !child.equals(""))
                        .collect(Collectors.groupingBy(CorrectionLabelManage::getLevel));

                //取得二级树集合
                List<CorrectionLabelManage> level2Objects = groupedByLevel.get(2);

                //遍历校验parentId，过滤出三级树
                for (CorrectionLabelManage level2Object : level2Objects) {
                    String id = level2Object.getId();
                    List<CorrectionLabelManage> children2 = children.stream()
                            .filter(obj -> null != obj.getParentId() && obj.getParentId().equals(id))
                            .collect(Collectors.toList());
                    if (children2.size() > 0) {
                        level2Object.setChildren(children2);
                    }
                }
                if (level2Objects.size() > 0) {
                    item.setChildren(level2Objects);
                }
            });

            //修改标签名为分类名称
            collect.forEach(item -> item.setLabel(item.getLabelAttribute()));
            //统计数量
            collect.forEach(item -> {
                        //二级树sum
                        long secondSum = item.getChildren().stream().mapToLong(child -> Long.parseLong(null == child.getDictNum() ? "0" : child.getDictNum())).sum();
                        //三级树sum
                        List<Long> thirdValueList = new ArrayList<>();
                        item.getChildren().stream().forEach(third -> {
                            if (null == third.getChildren() || third.getChildren().size() == 0) {
                                return;
                            }
                            long thirdSum = third.getChildren().stream().mapToLong(child -> Long.parseLong(null == child.getDictNum() ? "0" : child.getDictNum())).sum();
//                    System.out.println(thirdSum);
                            thirdValueList.add(thirdSum);
                        });
                        long sum = 0L;
                        for (Long num : thirdValueList) {
                            sum += num;
                        }
                        item.setDictNum(
                                String.valueOf(
                                        secondSum + sum
                                )
                        );
                    }
            );
            //统计数量
            collect.forEach(item -> {
                        //二级树sum
                        long secondSum = item.getChildren().stream().mapToLong(child -> Long.parseLong(null == child.getSqjzryNum() ? "0" : child.getSqjzryNum())).sum();
                        //三级树sum
                        List<Long> thirdValueList = new ArrayList<>();
                        item.getChildren().stream().forEach(third -> {
                            if (null == third.getChildren() || third.getChildren().size() == 0) {
                                return;
                            }
                            long thirdSum = third.getChildren().stream().mapToLong(child -> Long.parseLong(null == child.getSqjzryNum() ? "0" : child.getSqjzryNum())).sum();
                            thirdValueList.add(thirdSum);
                        });
                        long sum = 0L;
                        for (Long num : thirdValueList) {
                            sum += num;
                        }
                        item.setSqjzryNum(
                                String.valueOf(
                                        secondSum + sum
                                )
                        );
                    }
            );
            collect.forEach(item -> item.setId(item.getChildren().stream().map(CorrectionLabelManage::getId).collect(Collectors.joining(","))));

            //分页
            Integer pageNo = 1;
            Integer pageSize = 10;
            //每页条数
            String pageSizeString = req.getParameter(PAGE_SIZE_PARAM_NAME);
            if (ObjectUtil.isNotEmpty(pageSizeString)) {
                pageSize = Integer.parseInt(pageSizeString);
            }
            //第几页
            String pageNoString = req.getParameter(PAGE_NO_PARAM_NAME);
            if (ObjectUtil.isNotEmpty(pageNoString)) {
                pageNo = Integer.parseInt(pageNoString);
            }
            records = collect;
            List<CorrectionLabelManage> result = records.stream().skip((pageNo - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
            map.put("rows", result);
            map.put("pageNo", pageNo);
            map.put("pageSize", pageSize);
            map.put("totalPage", collect.size() % pageSize == 0 ? collect.size() / pageSize : collect.size() / pageSize + 1);
            map.put("totalRows", collect.size());
        }
        return map;
    }

    @Override
    public List<CorrectionLabelManage> list(CorrectionLabelManageParam correctionLabelManageParam) {
        QueryWrapper<CorrectionLabelManage> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionLabelManageParam)) {
            // 根据标签 查询
            if (ObjectUtil.isNotEmpty(correctionLabelManageParam.getLabel())) {
                queryWrapper.lambda().like(CorrectionLabelManage::getLabel, correctionLabelManageParam.getLabel());
            }
            // 所属应用模糊 查询
            if (ObjectUtil.isNotEmpty(correctionLabelManageParam.getBelongSys())) {
                queryWrapper.lambda().like(CorrectionLabelManage::getBelongSys, correctionLabelManageParam.getBelongSys());
            }
            if (ObjectUtil.isNotEmpty(correctionLabelManageParam.getLabelAttributeCode())) {
                queryWrapper.lambda().like(CorrectionLabelManage::getLabelAttributeCode, correctionLabelManageParam.getLabelAttributeCode());
            }
            if (ObjectUtil.isNotEmpty(correctionLabelManageParam.getLevel())) {
                queryWrapper.lambda().like(CorrectionLabelManage::getLevel, correctionLabelManageParam.getLevel());
            }
        }
        queryWrapper.lambda().eq(CorrectionLabelManage::getDelFlag, 0);
        return this.list(queryWrapper);
    }

    @Override
    public void add(CorrectionLabelManageParam correctionLabelManageParam) {
        CorrectionLabelManage correctionLabelManage = new CorrectionLabelManage();
        BeanUtil.copyProperties(correctionLabelManageParam, correctionLabelManage);
        CorrectionLabelManage cm = null;
        for (String lable : correctionLabelManage.getLabel().split(",")) {
            // 先判断标签是否已存在，已存在则跳过
            int num = this.count(new QueryWrapper<CorrectionLabelManage>().lambda().eq(CorrectionLabelManage::getLabelAttributeCode, correctionLabelManageParam.getLabelAttributeCode())
                    .eq(CorrectionLabelManage::getAttributeTypeCode, correctionLabelManageParam.getAttributeTypeCode())
                    .eq(CorrectionLabelManage::getLabel, correctionLabelManageParam.getLabel()));
            if (num == 0) {
                cm = new CorrectionLabelManage();
                BeanUtil.copyProperties(correctionLabelManage, cm);
                cm.setLabel(lable);
                cm.setCreateTime(DateUtil.date());
                save(cm);
                for (String belongSys : correctionLabelManageParam.getBelongSys().split(",")) {
                    CorrectionLabelSystem correctionLabelSystem = new CorrectionLabelSystem();
                    correctionLabelSystem.setLabelId(cm.getId());
                    correctionLabelSystem.setSysCode(belongSys);
                    correctionLabelSystemService.save(correctionLabelSystem);
                }
            }
        }
        //更新序号
        if (null != correctionLabelManageParam.getLabelList() && correctionLabelManageParam.getLabelList().size() > 0) {
            updateBatchById(correctionLabelManageParam.getLabelList());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionLabelManageParam correctionLabelManageParam) {
        CorrectionLabelManage correctionLabelManage = null;
        for (String id : correctionLabelManageParam.getIdArr()) {
            correctionLabelManage = new CorrectionLabelManage();
            correctionLabelManage.setId(id);
            correctionLabelManage.setDelFlag(1);
            this.updateById(correctionLabelManage);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean edit(CorrectionLabelManageParam correctionLabelManageParam) {
        CorrectionLabelManage correctionLabelManage = this.queryCorrectionLabelManage(correctionLabelManageParam);
        BeanUtil.copyProperties(correctionLabelManageParam, correctionLabelManage);
        // 先判断标签是否已存在，已存在则提示修改失败
        int num = this.count(new QueryWrapper<CorrectionLabelManage>().lambda().eq(CorrectionLabelManage::getLabelAttributeCode, correctionLabelManageParam.getLabelAttributeCode())
                .eq(CorrectionLabelManage::getAttributeTypeCode, correctionLabelManageParam.getAttributeTypeCode())
                .eq(CorrectionLabelManage::getLabel, correctionLabelManageParam.getLabel())
                .ne(CorrectionLabelManage::getId, correctionLabelManageParam.getId()));
        if (num > 0) {
            return false;
        }
        //更新序号
        if (null != correctionLabelManageParam.getLabelList() && correctionLabelManageParam.getLabelList().size() > 0) {
            updateBatchById(correctionLabelManageParam.getLabelList());
        }
        return this.updateById(correctionLabelManage);
    }

    @Override
    public CorrectionLabelManage detail(CorrectionLabelManageParam correctionLabelManageParam) {
        return this.queryCorrectionLabelManage(correctionLabelManageParam);
    }

    /**
     * 获取标签管理
     *
     * <AUTHOR>
     * @date 2022-03-03 09:48:05
     */
    private CorrectionLabelManage queryCorrectionLabelManage(CorrectionLabelManageParam correctionLabelManageParam) {
        CorrectionLabelManage correctionLabelManage = this.getById(correctionLabelManageParam.getId());
        if (ObjectUtil.isNull(correctionLabelManage)) {
            throw new ServiceException(CorrectionLabelManageExceptionEnum.NOT_EXIST);
        }
        return correctionLabelManage;
    }
}
