package com.concise.gen.correctioncorrectplan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 矫正方案
 *
 * <AUTHOR>
 * @date 2022-03-07 14:27:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_correct_plan")
public class CorrectionCorrectPlan extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 矫正对象id
     */
    private String sqjzryId;

    /**
     * 姓名
     */
    private String sqjzryName;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 矫正机构ID
     */
    private String jzjg;

    /**
     * 矫正机构名称
     */
    private String jzjgName;

    /**
     * 主要犯罪事实
     */
    private String criminalBehavior;

    /**
     * 现实表现
     */
    private String actuallyExpression;

    /**
     * 社会反应及心理测试情况
     */
    private String societyPshchologicalTest;

    /**
     * 矫正意见
     */
    private String correctIdea;

    /**
     * 矫正方案措施
     */
    private String correctPlan;

    /**
     * 监管措施
     */
    private String correctMeasure;

    /**
     * 方案调整时间
     */
    private Date adjustTime;

    /**
     * 方案调整原因，字典值：TZYY
     */
    private String adjustReason;

    /**
     * 方案发挥成效; 1: 良好 2：一般 3：不佳
     */
    private Integer exertEffect;

    /**
     * 社矫对象表现; 1: 良好 2：一般 3：不佳
     */
    private Integer expression;

    /**
     * 上次实施效果评估
     */
    private String exertEstimate;

    /**
     * 方案状态，0：入矫制定 1：在矫调整 2：解矫总结
     */
    private Integer planStatus;

    /**
     * 控制是否页面显示 0 显示 1 不显示
     */
    private Integer delFlag;

    /**
     * 生成日期_begin
     */
    @TableField(exist = false)
    private String createTime_begin;

    /**
     * 生成日期_end
     */
    @TableField(exist = false)
    private String createTime_end;

    /**
     * 方案调整日期_begin
     */
    @TableField(exist = false)
    private String adjustTime_begin;

    /**
     * 方案调整日期_end
     */
    @TableField(exist = false)
    private String adjustTime_end;

    /**
     * 方案调整次数
     */
    @TableField(exist = false)
    private int planNum;

    /**
     * 矫正阶段 1：入矫初期 2：矫正中期 3：矫正末期
     */
    @TableField(exist = false)
    private int phase;

    /**
     * 查询标志位： 0 查在矫  1： 查在矫以外的
     */
    @TableField(exist = false)
    private int tag;
}
