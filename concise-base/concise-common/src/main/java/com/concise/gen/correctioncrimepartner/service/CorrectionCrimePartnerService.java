package com.concise.gen.correctioncrimepartner.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctioncrimepartner.entity.CorrectionCrimePartner;
import com.concise.gen.correctioncrimepartner.param.CorrectionCrimePartnerParam;

import java.util.List;

/**
 * 同案犯service接口
 *
 * <AUTHOR>
 * @date 2022-02-22 16:07:46
 */
public interface CorrectionCrimePartnerService extends IService<CorrectionCrimePartner> {

    /**
     * 查询同案犯
     *
     * <AUTHOR>
     * @date 2022-02-22 16:07:46
     */
    PageResult<CorrectionCrimePartner> page(CorrectionCrimePartnerParam correctionCrimePartnerParam);

    /**
     * 同案犯列表
     *
     * <AUTHOR>
     * @date 2022-02-22 16:07:46
     */
    List<CorrectionCrimePartner> list(CorrectionCrimePartnerParam correctionCrimePartnerParam);
}
