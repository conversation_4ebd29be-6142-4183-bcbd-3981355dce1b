package com.concise.gen.messagemanage.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * MessageManage
 *
 * <AUTHOR>
 * @date 2023-01-04 14:10:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("message_manage")
public class MessageManage extends BaseEntity {

    /**
     * 
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 
     */
    private String templateName;

    /**
     * 
     */
    private String messsageContent;

    /**
     * 
     */
    private String sendobj;

    /**
     * 
     */
    private String enabledSwitch;

}
