package com.concise.gen.correctionestimateportrayaldetail.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;

import java.util.Date;

/**
 * 画像明细
 *
 * <AUTHOR>
 * @date 2022-11-30 19:58:01
 */
@Data
@TableName("correction_estimate_portrayal_detail")
public class CorrectionEstimatePortrayalDetail {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 画像类型
     */
    private Integer type;

    /**
     * 画像id
     */
    private String portrayalId;

    /**
     * 画像描述
     */
    private String hxms;

    /**
     * 轨迹趋势
     */
    private String gjqs;

    /**
     * 轨迹风险
     */
    private String xlhxGjfx;

    /**
     * 改善建议
     */
    private String jy;

    /**
     * 扣分明细
     */
    private String kfmx;

    /**
     * 权重总分
     */
    private String zf;

    /**
     * 扣分
     */
    private String kf;

    /**
     * 得分
     */
    private String df;

    /**
     * 风险等级
     */
    private String level;

}
