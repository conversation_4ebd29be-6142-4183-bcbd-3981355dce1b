package com.concise.gen.messagemanage.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* MessageManage参数类
 *
 * <AUTHOR>
 * @date 2023-01-04 14:10:20
*/
@Data
public class MessageManageParam extends BaseParam {

    /**
     * 
     */
    @NotNull(message = "不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 
     */
    @NotBlank(message = "不能为空，请检查templateName参数", groups = {add.class, edit.class})
    private String templateName;

    /**
     * 
     */
    @NotBlank(message = "不能为空，请检查messsageCount参数", groups = {add.class, edit.class})
    private String messsageContent;

    /**
     * 
     */
    @NotBlank(message = "不能为空，请检查sendobj参数", groups = {add.class, edit.class})
    private String sendobj;

    /**
     * 
     */
    @NotBlank(message = "不能为空，请检查enabledSwitch参数", groups = {add.class, edit.class})
    private String enabledSwitch;

}
