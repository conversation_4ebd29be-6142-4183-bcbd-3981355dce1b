package com.concise.gen.correctionestimatereport.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ReportDetailRiskDTO {

    /**
     * 指标名称
     */
    private String indexName;

    /**
     * 分值
     */
    private BigDecimal score;

    /**
     * 情况
     */
    private List<String> conditions;

    public ReportDetailRiskDTO() {
    }

    public ReportDetailRiskDTO(String indexName, BigDecimal score, List<String> conditions) {
        this.indexName = indexName;
        this.score = score;
        this.conditions = conditions;
    }
}
