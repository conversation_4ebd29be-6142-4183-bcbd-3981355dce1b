package com.concise.gen.correctionlabeldictionary.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 标签关联字典值表参数类
 *
 * <AUTHOR>
 * @date 2022-03-08 22:24:16
*/
@Data
public class CorrectionLabelDictionaryParam extends BaseParam {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 标签ID
     */
    private String labelId;

    /**
     * 字典code
     */
    private String dictionaryCode;

    /**
     * 字典name
     */
    private String dictionaryName;

    /**
     * 字典类别name
     */
    private String dictionaryTypeName;

}
