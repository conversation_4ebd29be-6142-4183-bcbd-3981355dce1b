<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.examquestion.mapper.ExamQuestionMapper">

    <select id="householdDebtList"
            resultType="com.concise.gen.portraitImage.correctionassesanswer.entity.CorrectionAssesAnswer">
        SELECT
            correction_asses_answer.*
        FROM
            correction_asses_answer
                LEFT JOIN correction_asses_person_dtl ON correction_asses_person_dtl.id = correction_asses_answer.dtl_id
                LEFT JOIN (
                SELECT
                    MAX( correction_asses_person_dtl.end_time ) AS end_time,
                    correction_asses_person_dtl.sqjzry_id
                FROM
                    correction_asses_answer
                        LEFT JOIN correction_asses_person_dtl ON correction_asses_person_dtl.id = correction_asses_answer.dtl_id
                WHERE
                    correction_asses_answer.question_id = '1622418543030132743'
                GROUP BY
                    correction_asses_person_dtl.sqjzry_id
            ) new_info ON correction_asses_person_dtl.sqjzry_id = new_info.sqjzry_id
                AND correction_asses_person_dtl.end_time = new_info.end_time
        WHERE
            correction_asses_answer.question_id = '1622418543030132743'
    </select>
</mapper>
