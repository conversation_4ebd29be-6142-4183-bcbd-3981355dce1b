package com.concise.gen.correctiontablefielddict.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 表字段与字典关联表
 *
 * <AUTHOR>
 * @date 2024-11-27 16:36:14
 */

@Data
@TableName("correction_table_field_dict")
public class CorrectionTableFieldDict {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 数据库名称
     */
    private String databaseName;

    /**
     * 表名称
     */
    private String tableName;

    /**
     * 表名称中文
     */
    private String tableNameCn;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段名称中文
     */
    private String fieldNameCn;

    /**
     * 字典类型id
     */
    private String dictTypeId;

    /**
     * 字典类型
     */
    private String dictType;

    /**
     * 字典类型名称
     */
    private String dictTypeName;

}
