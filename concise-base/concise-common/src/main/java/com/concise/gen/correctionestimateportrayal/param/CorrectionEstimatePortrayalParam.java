package com.concise.gen.correctionestimateportrayal.param;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
* 在矫评估参数类
 *
 * <AUTHOR>
 * @date 2022-05-17 11:48:38
*/
@Data
public class CorrectionEstimatePortrayalParam extends BaseParam {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 矫正对象id
     */
    private String sqjzryId;

    /**
     * 姓名
     */
    private String sqjzryName;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 矫正机构ID
     */
    private String jzjg;

    /**
     * 矫正机构名称
     */
    private String jzjgName;

    /**
     * 总分
     */
    private BigDecimal scoreTotal;

    /**
     * 评估分
     */
    private BigDecimal scoreEstimate;

    /**
     * 评估月份
     */
    private Integer estimateMonth;

    /**
     * 是否等级降到严管（0：否，1: 是）
     */
    private int levelDown;

    /**
     * 信息化监管违规
     */
    private int yqViolate;

    /**
     * 训诫
     */
    private int advise;

    /**
     * 警告
     */
    private int warn;

    /**
     * 治安处罚
     */
    private int publicSecurity;

    /**
     * 提请逮捕
     */
    private int askArrest;

    /**
     * 提请撤缓
     */
    private int cancelProbation;

    /**
     * 提请撤销假释
     */
    private int cancelParole;

    /**
     * 提请收监执行
     */
    private int committedToPrison;

    /**
     * 工作变动
     */
    private int workChange;

    /**
     * 夜不归宿
     */
    private int nightOut;

    /**
     * 心情码状态（1：红码，2: 橙码，3：黄码）
     */
    private int heartCode;

    /**
     * 受到表扬
     */
    private int praise;

    /**
     * 获得减刑
     */
    private int penaltyDown;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private int delFlag;

    /**
     * 日常监管得分
     */
    private BigDecimal rcjgScore;

    /**
     * 处罚得分
     */
    private BigDecimal cfScore;

    /**
     * 行为动态得分
     */
    private BigDecimal xwdtScore;

    /**
     * 心理状态得分
     */
    private BigDecimal xlztScore;

    /**
     * 行政处罚
     */
    private int administrativePenalty;

    /**
     * 是否流动人口（0：否，1: 是）
     */
    private int isLdrk;

    /**
     * 是否有精神病史或精神病遗传史（0：否，1: 是）
     */
    private int isPsychosis;

    /**
     * 负面心情
     */
    private int negativeMood;

    /**
     * 对本次法院最终判决结果的看法
     */
    private int writteWritten;

    /**
     * 负面关键词预警
     */
    private int negativeWords;


    private String ageCondition;

    /**
     * 学历
     */
    private String education;

    /**
     * 是否有前科
     */
    private int criminality;

    /**
     * 对社区矫正监管措施的熟悉程度
     */
    private int jgcsFamiliarity;

    /**
     * 是否累犯惯犯
     */
    private int recidivist;

    /**
     * 社区矫正监管措施问题是否通过
     */
    private int jgcsPass;

    /**
     * 就业状态
     */
    private String jobCondition;

    /**
     * 工作变动
     */
    private int jobMove;

    /**
     * 就业类型
     */
    private int jobType;

    /**
     * 职务类型
     */
    private int positionType;


    /**
     * 家庭关系
     */
    private int familyTies;

    /**
     * 家庭矛盾
     */
    private int familyConflict;

    /**
     * 家人是否有重大疾病需要照顾
     */
    private int familyIllness;

    /**
     * 家庭变故
     */
    private int familyCalamity;

    /**
     * 是否低保
     */
    private int insured;

    /**
     * 是否特困
     */
    private int poverty;

    /**
     * 家庭收入
     */
    private int familyIncome;

    /**
     * 家庭负债
     */
    private int familyLiabilities;

    /**
     * 信用分
     */
    private int creditScore;

    /**
     * 个人收入
     */
    private int personIncome;

    /**
     * 个人负债
     */
    private int personLiabilities;

    /**
     * 是否残疾
     */
    private int disability;

    /**
     * 个人健康
     */
    private int health;

    /**
     * 门诊情况
     */
    private int outpatientCondition;

    /**
     * 是否完成新冠疫苗接种
     */
    private int xgybjz;

    /**
     * 住院情况
     */
    private int hospitalization;

    /**
     * 居住情况
     */
    private int residentialCondition;

    /**
     * 近期是否与人有矛盾
     */
    private int contradiction;

}
