package com.concise.gen.correctionabilitylable.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 能力管理、标签关联表参数类
 *
 * <AUTHOR>
 * @date 2022-03-03 11:00:37
*/
@Data
public class CorrectionAbilityLableParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * 能力id
     */
    @NotNull(message = "能力id不能为空，请检查abilityId参数", groups = {add.class, edit.class})
    private Long abilityId;

    /**
     * 标签id
     */
    @NotNull(message = "标签id不能为空，请检查lableId参数", groups = {add.class, edit.class})
    private String lableId;

}
