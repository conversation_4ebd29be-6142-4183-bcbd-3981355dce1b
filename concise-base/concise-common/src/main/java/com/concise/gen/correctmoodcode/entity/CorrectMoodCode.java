package com.concise.gen.correctmoodcode.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 心情码
 *
 * <AUTHOR>
 * @date 2022-05-26 18:01:13
 */
@Data
@TableName("sqjzzxsjk0.correct_mood_code")
public class CorrectMoodCode {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 矫正对象id(万达平台)
     */
    private String thirdId;

    /**
     * 心情码 5：绿码、4：蓝码、3：黄码、2：橙码、1：红码
     */
    private String moodCode;

    /**
     * 矫正对象姓名
     */
    private String userName;

    /**
     * 更新时间
     */
    private String updateTime;
}
