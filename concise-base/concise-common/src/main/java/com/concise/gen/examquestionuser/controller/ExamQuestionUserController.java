package com.concise.gen.examquestionuser. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.examquestionuser. param.ExamQuestionUserParam;
import com.concise.gen.examquestionuser. service.ExamQuestionUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 用户答题表控制器
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:27
 */
@Api(tags = "用户答题表")
@RestController
public class ExamQuestionUserController {

    @Resource
    private ExamQuestionUserService examQuestionUserService;

    /**
     * 查询用户答题表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:27
     */
    @Permission
    @GetMapping("/examQuestionUser/page")
    @ApiOperation("用户答题表_分页查询")
    @BusinessLog(title = "用户答题表_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(ExamQuestionUserParam examQuestionUserParam) {
        return new SuccessResponseData(examQuestionUserService.page(examQuestionUserParam));
    }

    /**
     * 添加用户答题表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:27
     */
    @Permission
    @PostMapping("/examQuestionUser/add")
    @ApiOperation("用户答题表_增加")
    @BusinessLog(title = "用户答题表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(ExamQuestionUserParam.add.class) ExamQuestionUserParam examQuestionUserParam) {
        examQuestionUserService.add(examQuestionUserParam);
        return new SuccessResponseData();
    }

    /**
     * 删除用户答题表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:27
     */
    @Permission
    @PostMapping("/examQuestionUser/delete")
    @ApiOperation("用户答题表_删除")
    @BusinessLog(title = "用户答题表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(ExamQuestionUserParam.delete.class) ExamQuestionUserParam examQuestionUserParam) {
        examQuestionUserService.delete(examQuestionUserParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑用户答题表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:27
     */
    @Permission
    @PostMapping("/examQuestionUser/edit")
    @ApiOperation("用户答题表_编辑")
    @BusinessLog(title = "用户答题表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(ExamQuestionUserParam.edit.class) ExamQuestionUserParam examQuestionUserParam) {
        examQuestionUserService.edit(examQuestionUserParam);
        return new SuccessResponseData();
    }

    /**
     * 查看用户答题表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:27
     */
    @Permission
    @GetMapping("/examQuestionUser/detail")
    @ApiOperation("用户答题表_查看")
    @BusinessLog(title = "用户答题表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(ExamQuestionUserParam.detail.class) ExamQuestionUserParam examQuestionUserParam) {
        return new SuccessResponseData(examQuestionUserService.detail(examQuestionUserParam));
    }

    /**
     * 用户答题表列表
     *
     * <AUTHOR>
     * @date 2023-01-03 15:53:27
     */
    @Permission
    @GetMapping("/examQuestionUser/list")
    @ApiOperation("用户答题表_列表")
    @BusinessLog(title = "用户答题表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(ExamQuestionUserParam examQuestionUserParam) {
        return new SuccessResponseData(examQuestionUserService.list(examQuestionUserParam));
    }

}
