package com.concise.gen.messagemanage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.messagemanage.entity.MessageManage;
import com.concise.gen.messagemanage.enums.MessageManageExceptionEnum;
import com.concise.gen.messagemanage.mapper.MessageManageMapper;
import com.concise.gen.messagemanage.param.MessageManageParam;
import com.concise.gen.messagemanage.service.MessageManageService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * MessageManageservice接口实现类
 *
 * <AUTHOR>
 * @date 2023-01-04 14:10:20
 */
@Service
public class MessageManageServiceImpl extends ServiceImpl<MessageManageMapper, MessageManage> implements MessageManageService {

    @Override
    public PageResult<MessageManage> page(MessageManageParam messageManageParam) {
        QueryWrapper<MessageManage> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(messageManageParam)) {

            // 根据 查询
            if (ObjectUtil.isNotEmpty(messageManageParam.getTemplateName())) {
                queryWrapper.lambda().like(MessageManage::getTemplateName, messageManageParam.getTemplateName());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(messageManageParam.getMesssageContent())) {
                queryWrapper.lambda().eq(MessageManage::getMesssageContent, messageManageParam.getMesssageContent());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(messageManageParam.getSendobj())) {
                if(messageManageParam.getSendobj().equals("SDME_01")){

                }else{
                    queryWrapper.lambda().eq(MessageManage::getSendobj, messageManageParam.getSendobj());
                }

            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(messageManageParam.getEnabledSwitch())) {
                queryWrapper.lambda().eq(MessageManage::getEnabledSwitch, messageManageParam.getEnabledSwitch());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<MessageManage> list(MessageManageParam messageManageParam) {
        return this.list();
    }

    @Override
    public void add(MessageManageParam messageManageParam) {
        MessageManage messageManage = new MessageManage();
        BeanUtil.copyProperties(messageManageParam, messageManage);
        this.save(messageManage);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(MessageManageParam messageManageParam) {
        this.removeById(messageManageParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(MessageManageParam messageManageParam) {
        MessageManage messageManage = this.queryMessageManage(messageManageParam);
        BeanUtil.copyProperties(messageManageParam, messageManage);
        this.updateById(messageManage);
    }

    @Override
    public MessageManage detail(MessageManageParam messageManageParam) {
        return this.queryMessageManage(messageManageParam);
    }

    /**
     * 获取MessageManage
     *
     * <AUTHOR>
     * @date 2023-01-04 14:10:20
     */
    private MessageManage queryMessageManage(MessageManageParam messageManageParam) {
        MessageManage messageManage = this.getById(messageManageParam.getId());
        if (ObjectUtil.isNull(messageManage)) {
            throw new ServiceException(MessageManageExceptionEnum.NOT_EXIST);
        }
        return messageManage;
    }
}
