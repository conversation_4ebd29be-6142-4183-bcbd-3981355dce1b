package com.concise.gen.portraitImage.correctionquestion.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.concise.gen.portraitImage.correctionquestionitem.entity.CorrectionQuestionItem;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.List;

/**
 * 量表配置--试题信息表
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:35
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_question")
public class CorrectionQuestion extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 量表id
     */
    private String scaleBaseId;

    /**
     * 问题名称
     */
    private String question;

    /**
     * 问题类型（0：单选 1：多选）
     */
    private Integer questionType;

    /**
     * 问题序号
     */
    private Integer orderIndex;

    /**
     * 关联的选项id
     */
    private String relevanceItemId;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 问题选项
     */
    @TableField(exist = false)
    private List<CorrectionQuestionItem> questionItemList;

    /**
     * 选中的选项ids
     */
    @TableField(exist = false)
    private String chooseItemIds;
}
