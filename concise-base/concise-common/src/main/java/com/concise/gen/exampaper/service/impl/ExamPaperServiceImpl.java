package com.concise.gen.exampaper.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.exampaper.entity.ExamPaper;
import com.concise.gen.exampaper.enums.ExamPaperExceptionEnum;
import com.concise.gen.exampaper.mapper.ExamPaperMapper;
import com.concise.gen.exampaper.param.ExamPaperParam;
import com.concise.gen.exampaper.service.ExamPaperService;
import com.concise.gen.exampaperquestion.entity.ExamPaperQuestion;
import com.concise.gen.exampaperquestion.service.ExamPaperQuestionService;
import com.concise.gen.examquestion.entity.ExamQuestion;
import com.concise.gen.examquestion.service.ExamQuestionService;
import com.concise.gen.examquestionitem.entity.ExamQuestionItem;
import com.concise.gen.examquestionitem.service.ExamQuestionItemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 新量表管理service接口实现类
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:03
 */
@Service
public class ExamPaperServiceImpl extends ServiceImpl<ExamPaperMapper, ExamPaper> implements ExamPaperService {

    @Resource
    private ExamPaperQuestionService examPaperQuestionService;

    @Resource
    private ExamQuestionService examQuestionService;

    @Resource
    private ExamQuestionItemService examQuestionItemService;

    @Override
    public PageResult<ExamPaper> page(ExamPaperParam examPaperParam) {
        QueryWrapper<ExamPaper> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(examPaperParam)) {

            // 根据量表名称 查询
            if (ObjectUtil.isNotEmpty(examPaperParam.getTitle())) {
                queryWrapper.lambda().like(ExamPaper::getTitle, examPaperParam.getTitle());
            }
            // 根据启用情况（0：启用 1：停用） 查询
            if (ObjectUtil.isNotEmpty(examPaperParam.getStatus())) {
                queryWrapper.lambda().eq(ExamPaper::getStatus, examPaperParam.getStatus());
            }
            // 根据是否删除（0：未删除，1删除） 查询
            if (ObjectUtil.isNotEmpty(examPaperParam.getDelFlag())) {
                queryWrapper.lambda().eq(ExamPaper::getDelFlag, examPaperParam.getDelFlag());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<ExamPaper> list(ExamPaperParam examPaperParam) {
        return this.list();
    }

    @Override
    public void add(ExamPaperParam examPaperParam) {
        ExamPaper examPaper = new ExamPaper();
        BeanUtils.copyProperties(examPaperParam, examPaper);
        examPaper.setCreateTime(DateUtil.date());
        examPaper.setUpdateTime(DateUtil.date());
        examPaper.setDelFlag("0");
        this.save(examPaper);
        for (String id : examPaperParam.getQuestionList()) {
            ExamQuestion examQuestion = examQuestionService.getById(id);
            ExamPaperQuestion examPaperQuestion = new ExamPaperQuestion();
            examPaperQuestion.setPaperId(examPaper.getId());
            examPaperQuestion.setQuestionId(examQuestion.getId());
            examPaperQuestionService.save(examPaperQuestion);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(ExamPaperParam examPaperParam) {
        this.removeById(examPaperParam.getId());
        examPaperQuestionService.remove(new QueryWrapper<ExamPaperQuestion>().lambda().eq(ExamPaperQuestion::getPaperId, examPaperParam.getId()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ExamPaperParam examPaperParam) {
        ExamPaper examPaper = this.queryExamPaper(examPaperParam);
        BeanUtils.copyProperties(examPaperParam, examPaper);
        this.updateById(examPaper);
        examPaperQuestionService.remove(new QueryWrapper<ExamPaperQuestion>().lambda().eq(ExamPaperQuestion::getPaperId,examPaper.getId()));
        for (String id : examPaperParam.getQuestionList()) {
            ExamQuestion examQuestion = examQuestionService.getById(id);
            ExamPaperQuestion examPaperQuestion = new ExamPaperQuestion();
            examPaperQuestion.setPaperId(examPaper.getId());
            examPaperQuestion.setQuestionId(examQuestion.getId());
            examPaperQuestionService.save(examPaperQuestion);
        }
    }

    @Override
    public ExamPaper detail(ExamPaperParam examPaperParam) {
        return this.queryExamPaper(examPaperParam);
    }

    /**
     * 获取新量表管理
     *
     * <AUTHOR>
     * @date 2023-01-06 14:58:03
     */
    private ExamPaper queryExamPaper(ExamPaperParam examPaperParam) {
        ExamPaper examPaper = this.getById(examPaperParam.getId());
        if (ObjectUtil.isNull(examPaper)) {
            throw new ServiceException(ExamPaperExceptionEnum.NOT_EXIST);
        }
        List<ExamPaperQuestion> paperQuestionList = examPaperQuestionService.list(new QueryWrapper<ExamPaperQuestion>().lambda().eq(ExamPaperQuestion::getPaperId, examPaper.getId()));
        if (CollectionUtil.isNotEmpty(paperQuestionList)) {
            List<ExamQuestion> examQuestionList = new ArrayList<>();
            List<ExamQuestionItem> list = examQuestionItemService.list(new QueryWrapper<ExamQuestionItem>().lambda().isNotNull(ExamQuestionItem::getPid));
            for (ExamPaperQuestion examPaperQuestion : paperQuestionList) {
                ExamQuestion question = examQuestionService.getById(examPaperQuestion.getQuestionId());
                if (ObjectUtil.isNotEmpty(question)) {
                    examQuestionService.buildChildren(question, list);
                    examQuestionList.add(question);
                }
            }
            examPaper.setQuestionList(examQuestionList);
            Set<String> typeIds = examQuestionList.stream().map(ExamQuestion::getTypeId).collect(Collectors.toSet());
            examPaper.setTypeId(String.join(",",typeIds));
        }
        return examPaper;
    }

    @Override
    public List<ExamQuestion> examPaperTreeSelected(String id) {
        ExamPaper paper = this.getById(id);
        List<ExamPaperQuestion> paperQuestionList = examPaperQuestionService.list(new QueryWrapper<ExamPaperQuestion>().lambda().eq(ExamPaperQuestion::getPaperId, paper.getId()));
        Set<String> questionIds = paperQuestionList.stream().map(ExamPaperQuestion::getQuestionId).collect(Collectors.toSet());
        List<ExamQuestion> list = examQuestionService.list(new QueryWrapper<ExamQuestion>().lambda().in(ExamQuestion::getId,questionIds));
        if (CollectionUtil.isNotEmpty(list)){
            for (ExamQuestion examQuestion : list) {
                List<ExamQuestion> questionList = examQuestionService.list(new QueryWrapper<ExamQuestion>().lambda().eq(ExamQuestion::getTypeId, examQuestion.getTypeId()).likeRight(ExamQuestion::getSort, examQuestion.getSort() + "-").orderByAsc(ExamQuestion::getSort));
                if (CollectionUtil.isNotEmpty(questionList)){
                    for (ExamQuestion question : questionList) {
                        question.setPid(examQuestion.getId());
                    }
                    examQuestion.setChildQuestion(questionList);
                }
            }
        }
        return list;
    }
}
