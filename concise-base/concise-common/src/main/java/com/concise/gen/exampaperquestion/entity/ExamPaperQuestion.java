package com.concise.gen.exampaperquestion.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 量表题目内容
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:05
 */
@Data
@TableName("exam_paper_question")
public class ExamPaperQuestion  {

    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 量表id
     */
    private String paperId;

    /**
     * 题目id
     */
    private String questionId;

}
