package com.concise.modular.risk.cmp;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.modular.risk.riskevaluatemanage.entity.RiskEvaluateContext;
import com.yomahub.liteflow.core.NodeComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/31
 * 系统得分计算
 */
public class RiskSystemScoreCmp extends NodeComponent {

    private final Logger log = LoggerFactory.getLogger(RiskSystemScoreCmp.class);

    @Override
    public void process() throws Exception {
        RiskEvaluateContext contextBean = getContextBean(RiskEvaluateContext.class);

        if (ObjectUtil.isEmpty(contextBean)) {
            log.error("获取RiskEvaluateContext对象失败");
        }

        CorrectionObjectInformation correctionObjectInformation = contextBean.getCorrectionObjectInformation();
        if (ObjectUtil.isEmpty(correctionObjectInformation)) {
            log.error("矫正对象信息为空");
            return;
        }
        //文化程度
        String whcd = correctionObjectInformation.getWhcd();
        //初中及以下为5分
        if ("01".equals(whcd) || "02".equals(whcd) || "03".equals(whcd)) {
            contextBean.setCultureDegreeScore(new BigDecimal(5));
            contextBean.setCultureDegreeResult("初中及以下");

        }
        //本科及以上，-5分
        if ("06".equals(whcd) || "07".equals(whcd) || "08".equals(whcd) || "09".equals(whcd)) {
            contextBean.setCultureDegreeScore(new BigDecimal(-5));
            contextBean.setCultureDegreeResult("本科及以上");
        }
        //调查评估情况，待处理

        //矫正类别
        String jzlb = correctionObjectInformation.getJzlb();
        //管制8分
        if ("01".equals(jzlb)) {
            contextBean.setCorrectionTypeScore(new BigDecimal(8));
            contextBean.setCorrectionTypeResult("管制");
        }
        //假释3分
        if ("03".equals(jzlb)) {
            contextBean.setCorrectionTypeScore(new BigDecimal(3));
            contextBean.setCorrectionTypeResult("假释");
        }
        //暂予监外5分
        if ("04".equals(jzlb)) {
            contextBean.setCorrectionTypeScore(new BigDecimal(5));
            contextBean.setCorrectionTypeResult("暂予监外执行");
        }

        //矫正期限
        int month = 0;
        try {
            if (ObjectUtil.isAllNotEmpty(correctionObjectInformation.getSqjzksrq(), correctionObjectInformation.getSqjzjsrq())) {
                long l = DateUtil.betweenMonth(correctionObjectInformation.getSqjzksrq(), correctionObjectInformation.getSqjzjsrq(), true);
                month = Integer.parseInt(String.valueOf(l));
            }
//6个月及以下-2
            if (month < 7) {
                contextBean.setCorrectionPeriodScore(new BigDecimal(-2));
                contextBean.setCorrectionPeriodResult("6个月及以下");
            }
            //7个月-3年，3分
            if (month > 6 && month <= 36) {
                contextBean.setCorrectionPeriodScore(new BigDecimal(3));
                contextBean.setCorrectionPeriodResult("6个月-3年");
            }
            //3年及以上，5分
            if (month > 36) {
                contextBean.setCorrectionPeriodScore(new BigDecimal(5));
                contextBean.setCorrectionPeriodResult("3年及以上");
            }
        } catch (NumberFormatException e) {
            log.error("矫正期限转换失败");
        }

    }

}
