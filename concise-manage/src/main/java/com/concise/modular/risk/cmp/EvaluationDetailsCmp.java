package com.concise.modular.risk.cmp;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.concise.modular.risk.riskevaluatemanage.dto.EvaluationDetailDto;
import com.concise.modular.risk.riskevaluatemanage.entity.RiskEvaluateManage;
import com.concise.paper.entity.PaperMaintenance;
import com.concise.paper.entity.PaperTopic;
import com.concise.paper.entity.PaperTopicItem;
import com.yomahub.liteflow.core.NodeComponent;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;

/**
 * 评估详情查询组件
 * 根据评估管理ID查询关联的量卷和表单详情
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Component("evaluationDetailsCmp")
public class EvaluationDetailsCmp extends NodeComponent {

    private final Logger log = LoggerFactory.getLogger(EvaluationDetailsCmp.class);

    @Override
    public void process() throws Exception {
        log.debug("开始查询评估详情");

        String evaluationManageId = this.getRequestData();
        if (StrUtil.isBlank(evaluationManageId)) {
            log.error("评估管理ID不能为空");
            return;
        }

        List<EvaluationDetailDto> resultList = new ArrayList<>();

        try {
            // 1. 根据评估管理ID查询评估管理信息
            com.concise.modular.risk.riskevaluatemanage.service.RiskEvaluateManageService riskEvaluateManageService =
                SpringUtil.getBean(com.concise.modular.risk.riskevaluatemanage.service.RiskEvaluateManageService.class);
            RiskEvaluateManage evaluateManage = riskEvaluateManageService.getById(evaluationManageId);

            if (ObjectUtil.isNull(evaluateManage)) {
                log.warn("未找到评估管理记录，ID: {}", evaluationManageId);
                return;
            }

            // 2. 处理量卷数据（从formCollectionJsonWrite中获取已填写的表单数据）
            if (StrUtil.isNotBlank(evaluateManage.getFormCollectionJsonWrite())) {
                processFormData(evaluateManage.getFormCollectionJsonWrite(), resultList);
            } else if (StrUtil.isNotBlank(evaluateManage.getFormCollectionJson())) {
                // 如果没有填写数据，则使用原始表单数据
                processFormData(evaluateManage.getFormCollectionJson(), resultList);
            }

            log.info("查询评估详情完成，共找到 {} 条记录", resultList.size());

        } catch (Exception e) {
            log.error("查询评估详情时发生异常，评估管理ID: {}", evaluationManageId, e);
        }
    }

    /**
     * 处理表单数据
     */
    private void processFormData(String formJsonStr, List<EvaluationDetailDto> resultList) {
        try {
            PaperMaintenance paperMaintenance = JSONObject.parseObject(formJsonStr, PaperMaintenance.class);
            if (ObjectUtil.isNull(paperMaintenance)) {
                log.warn("解析表单JSON失败");
                return;
            }

            log.info("处理表单数据，表单名称: {}", paperMaintenance.getTitle());

            // 获取题目列表
            List<PaperTopic> topicList = paperMaintenance.getTopicList();
            if (CollUtil.isEmpty(topicList)) {
                log.warn("表单中没有题目数据");
                return;
            }

            // 转换题目数据
            for (PaperTopic topic : topicList) {
                EvaluationDetailDto detailDto = convertTopicToDto(topic, paperMaintenance);
                if (ObjectUtil.isNotNull(detailDto)) {
                    resultList.add(detailDto);
                }
            }

        } catch (Exception e) {
            log.error("处理表单数据时发生异常", e);
        }
    }

    /**
     * 将题目转换为DTO
     */
    private EvaluationDetailDto convertTopicToDto(PaperTopic topic, PaperMaintenance paperMaintenance) {
        try {
            EvaluationDetailDto dto = new EvaluationDetailDto();
            
            // 基本信息
            dto.setTopicId(topic.getId());
            dto.setTopicName(topic.getTopicName());
            dto.setTopicType(topic.getTopicType());
            // 根据topicType获取类型名称
            dto.setTopicTypeName(getTopicTypeName(topic.getTopicType()));
            dto.setIndexName(topic.getIndexName());
            dto.setTopicScore(topic.getTopicScore());
            dto.setSerialNumber(topic.getSerialNumber());
            dto.setUserSelectId(topic.getUserSelectId());
            dto.setRemark(topic.getRemark());
            
            // 数据来源信息
            dto.setSourceType("表单");
            dto.setSourceName(paperMaintenance.getTitle());

            // 处理选项列表
            List<EvaluationDetailDto.TopicItemDto> itemDtoList = new ArrayList<>();
            List<PaperTopicItem> itemList = topic.getItemList();
            
            if (CollUtil.isNotEmpty(itemList)) {
                for (PaperTopicItem item : itemList) {
                    EvaluationDetailDto.TopicItemDto itemDto = new EvaluationDetailDto.TopicItemDto();
                    itemDto.setId(item.getId());
                    itemDto.setContent(item.getContent());
                    itemDto.setItemScore(item.getItemScore());
                    itemDto.setSerialNumber(item.getSerialNumber());
                    itemDto.setReserve1(item.getReserve1());
                    itemDto.setReserve2(item.getReserve2());
                    
                    // 判断是否被选中
                    boolean selected = StrUtil.isNotBlank(topic.getUserSelectId()) && 
                                     topic.getUserSelectId().equals(item.getId());
                    itemDto.setSelected(selected);
                    
                    // 如果是用户选择的选项，设置相关信息
                    if (selected) {
                        dto.setUserSelectContent(item.getContent());
                        dto.setUserSelectScore(item.getItemScore());
                    }
                    
                    itemDtoList.add(itemDto);
                }
            }
            
            dto.setItemList(itemDtoList);
            return dto;

        } catch (Exception e) {
            log.error("转换题目数据时发生异常，题目: {}", topic.getTopicName(), e);
            return null;
        }
    }

    /**
     * 根据题目类型代码获取类型名称
     */
    private String getTopicTypeName(String topicType) {
        if (StrUtil.isBlank(topicType)) {
            return "";
        }

        // 这里可以根据实际的字典配置来获取类型名称
        // 暂时使用简单的映射，实际项目中应该从字典表获取
        switch (topicType) {
            case "1":
                return "单选题";
            case "2":
                return "多选题";
            case "3":
                return "填空题";
            case "4":
                return "问答题";
            default:
                return topicType;
        }
    }
}
