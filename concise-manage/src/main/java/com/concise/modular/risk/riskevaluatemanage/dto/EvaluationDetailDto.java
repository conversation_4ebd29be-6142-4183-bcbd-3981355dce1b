package com.concise.modular.risk.riskevaluatemanage.dto;

import java.util.List;

import lombok.Data;

/**
 * 评估详情DTO
 * 包含题目、用户选择的选项、得分等信息
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
public class EvaluationDetailDto {

    /**
     * 题目ID
     */
    private String topicId;

    /**
     * 题目名称
     */
    private String topicName;

    /**
     * 题目类型
     */
    private String topicType;

    /**
     * 题目类型名称
     */
    private String topicTypeName;

    /**
     * 指标名称
     */
    private String indexName;

    /**
     * 题目总分
     */
    private Integer topicScore;

    /**
     * 序号
     */
    private Integer serialNumber;

    /**
     * 用户选择的选项ID
     */
    private String userSelectId;

    /**
     * 用户选择的选项内容
     */
    private String userSelectContent;

    /**
     * 用户选择选项的得分
     */
    private Integer userSelectScore;

    /**
     * 备注
     */
    private String remark;

    /**
     * 所有选项列表
     */
    private List<TopicItemDto> itemList;

    /**
     * 数据来源类型（量卷/表单）
     */
    private String sourceType;

    /**
     * 数据来源名称
     */
    private String sourceName;

    /**
     * 题目选项DTO
     */
    @Data
    public static class TopicItemDto {
        /**
         * 选项ID
         */
        private String id;

        /**
         * 选项内容
         */
        private String content;

        /**
         * 选项得分
         */
        private Integer itemScore;

        /**
         * 序号
         */
        private Integer serialNumber;

        /**
         * 是否被选中
         */
        private Boolean selected;

        /**
         * 预留字段1
         */
        private String reserve1;

        /**
         * 预留字段2
         */
        private String reserve2;
    }
}
