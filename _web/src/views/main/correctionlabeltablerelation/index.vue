<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('correctionLabelTableRelation:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="标签">
                <a-input v-model="queryParam.label" allow-clear placeholder="请输入标签"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="标签属性">
                <a-input v-model="queryParam.labelAttribute" allow-clear placeholder="请输入标签属性"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="标签属性字典码">
                  <a-input v-model="queryParam.labelAttributeCode" allow-clear placeholder="请输入标签属性字典码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="属性分类">
                  <a-input v-model="queryParam.attributeType" allow-clear placeholder="请输入属性分类"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="属性分类字典码">
                  <a-input v-model="queryParam.attributeTypeCode" allow-clear placeholder="请输入属性分类字典码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否删除（0：未删除，1删除）">
                  <a-input v-model="queryParam.delFlag" allow-clear placeholder="请输入是否删除（0：未删除，1删除）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="备注">
                  <a-input v-model="queryParam.remark" allow-clear placeholder="请输入备注"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="所属系统_多个系统间逗号隔开">
                  <a-input v-model="queryParam.belongSys" allow-clear placeholder="请输入所属系统_多个系统间逗号隔开"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电话核查优先级">
                  <a-input v-model="queryParam.orderIndex" allow-clear placeholder="请输入电话核查优先级"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="关联表">
                  <a-input v-model="queryParam.relationTable" allow-clear placeholder="请输入关联表"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="关联表名称">
                  <a-input v-model="queryParam.relationTableName" allow-clear placeholder="请输入关联表名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="关联内容">
                  <a-input v-model="queryParam.relationContent" allow-clear placeholder="请输入关联内容"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="关联字段">
                  <a-input v-model="queryParam.relationField" allow-clear placeholder="请输入关联字段"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="关联字段名称">
                  <a-input v-model="queryParam.relationFieldName" allow-clear placeholder="请输入关联字段名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="关联字典">
                  <a-input v-model="queryParam.relationDict" allow-clear placeholder="请输入关联字典"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="关联字典名称">
                  <a-input v-model="queryParam.relationDictName" allow-clear placeholder="请输入关联字典名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="标签层级">
                  <a-input v-model="queryParam.labelLevel" allow-clear placeholder="请输入标签层级"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('correctionLabelTableRelation:add')" >
          <a-button type="primary" v-if="hasPerm('correctionLabelTableRelation:add')" icon="plus" @click="$refs.addForm.add()">新增标签关联</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('correctionLabelTableRelation:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('correctionLabelTableRelation:edit') & hasPerm('correctionLabelTableRelation:delete')"/>
          <a-popconfirm v-if="hasPerm('correctionLabelTableRelation:delete')" placement="topRight" title="确认删除？" @confirm="() => correctionLabelTableRelationDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { correctionLabelTableRelationPage, correctionLabelTableRelationDelete } from '@/api/modular/main/correctionlabeltablerelation/correctionLabelTableRelationManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '标签',
            align: 'center',
            dataIndex: 'label'
          },
          {
            title: '标签属性',
            align: 'center',
            dataIndex: 'labelAttribute'
          },
          {
            title: '标签属性字典码',
            align: 'center',
            dataIndex: 'labelAttributeCode'
          },
          {
            title: '属性分类',
            align: 'center',
            dataIndex: 'attributeType'
          },
          {
            title: '属性分类字典码',
            align: 'center',
            dataIndex: 'attributeTypeCode'
          },
          {
            title: '是否删除（0：未删除，1删除）',
            align: 'center',
            dataIndex: 'delFlag'
          },
          {
            title: '备注',
            align: 'center',
            dataIndex: 'remark'
          },
          {
            title: '所属系统_多个系统间逗号隔开',
            align: 'center',
            dataIndex: 'belongSys'
          },
          {
            title: '电话核查优先级',
            align: 'center',
            dataIndex: 'orderIndex'
          },
          {
            title: '关联表',
            align: 'center',
            dataIndex: 'relationTable'
          },
          {
            title: '关联表名称',
            align: 'center',
            dataIndex: 'relationTableName'
          },
          {
            title: '关联内容',
            align: 'center',
            dataIndex: 'relationContent'
          },
          {
            title: '关联字段',
            align: 'center',
            dataIndex: 'relationField'
          },
          {
            title: '关联字段名称',
            align: 'center',
            dataIndex: 'relationFieldName'
          },
          {
            title: '关联字典',
            align: 'center',
            dataIndex: 'relationDict'
          },
          {
            title: '关联字典名称',
            align: 'center',
            dataIndex: 'relationDictName'
          },
          {
            title: '标签层级',
            align: 'center',
            dataIndex: 'labelLevel'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return correctionLabelTableRelationPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('correctionLabelTableRelation:edit') || this.hasPerm('correctionLabelTableRelation:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      correctionLabelTableRelationDelete (record) {
        correctionLabelTableRelationDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
