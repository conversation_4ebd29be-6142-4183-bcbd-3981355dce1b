<template>
  <a-modal
    title="新增标签关联"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="标签"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入标签" v-decorator="['label', {rules: [{required: true, message: '请输入标签！'}]}]" />
        </a-form-item>
        <a-form-item
          label="标签属性"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入标签属性" v-decorator="['labelAttribute', {rules: [{required: true, message: '请输入标签属性！'}]}]" />
        </a-form-item>
        <a-form-item
          label="标签属性字典码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入标签属性字典码" v-decorator="['labelAttributeCode', {rules: [{required: true, message: '请输入标签属性字典码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="属性分类"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入属性分类" v-decorator="['attributeType', {rules: [{required: true, message: '请输入属性分类！'}]}]" />
        </a-form-item>
        <a-form-item
          label="属性分类字典码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入属性分类字典码" v-decorator="['attributeTypeCode', {rules: [{required: true, message: '请输入属性分类字典码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否删除（0：未删除，1删除）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否删除（0：未删除，1删除）" v-decorator="['delFlag', {rules: [{required: true, message: '请输入是否删除（0：未删除，1删除）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="备注"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入备注" v-decorator="['remark', {rules: [{required: true, message: '请输入备注！'}]}]" />
        </a-form-item>
        <a-form-item
          label="所属系统_多个系统间逗号隔开"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入所属系统_多个系统间逗号隔开" v-decorator="['belongSys', {rules: [{required: true, message: '请输入所属系统_多个系统间逗号隔开！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电话核查优先级"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电话核查优先级" v-decorator="['orderIndex', {rules: [{required: true, message: '请输入电话核查优先级！'}]}]" />
        </a-form-item>
        <a-form-item
          label="关联表"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入关联表" v-decorator="['relationTable', {rules: [{required: true, message: '请输入关联表！'}]}]" />
        </a-form-item>
        <a-form-item
          label="关联表名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入关联表名称" v-decorator="['relationTableName', {rules: [{required: true, message: '请输入关联表名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="关联内容"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入关联内容" v-decorator="['relationContent', {rules: [{required: true, message: '请输入关联内容！'}]}]" />
        </a-form-item>
        <a-form-item
          label="关联字段"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入关联字段" v-decorator="['relationField', {rules: [{required: true, message: '请输入关联字段！'}]}]" />
        </a-form-item>
        <a-form-item
          label="关联字段名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入关联字段名称" v-decorator="['relationFieldName', {rules: [{required: true, message: '请输入关联字段名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="关联字典"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入关联字典" v-decorator="['relationDict', {rules: [{required: true, message: '请输入关联字典！'}]}]" />
        </a-form-item>
        <a-form-item
          label="关联字典名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入关联字典名称" v-decorator="['relationDictName', {rules: [{required: true, message: '请输入关联字典名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="标签层级"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入标签层级" v-decorator="['labelLevel', {rules: [{required: true, message: '请输入标签层级！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { correctionLabelTableRelationAdd } from '@/api/modular/main/correctionlabeltablerelation/correctionLabelTableRelationManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            correctionLabelTableRelationAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
