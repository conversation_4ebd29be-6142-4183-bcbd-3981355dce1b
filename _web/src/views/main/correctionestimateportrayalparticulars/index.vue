<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('correctionEstimatePortrayalParticulars:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="心理画像描述">
                <a-input v-model="queryParam.xlhxms" allow-clear placeholder="请输入心理画像描述"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="知法画像描述">
                <a-input v-model="queryParam.zfhxms" allow-clear placeholder="请输入知法画像描述"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="就业画像描述">
                  <a-input v-model="queryParam.jyhxms" allow-clear placeholder="请输入就业画像描述"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="家庭画像描述">
                  <a-input v-model="queryParam.jthxms" allow-clear placeholder="请输入家庭画像描述"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="信用画像描述">
                  <a-input v-model="queryParam.xyhxms" allow-clear placeholder="请输入信用画像描述"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="个人基本画像描述">
                  <a-input v-model="queryParam.jbhxms" allow-clear placeholder="请输入个人基本画像描述"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="心理画像建议">
                  <a-input v-model="queryParam.xlhxjy" allow-clear placeholder="请输入心理画像建议"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="知法画像建议">
                  <a-input v-model="queryParam.zfhxjy" allow-clear placeholder="请输入知法画像建议"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="就业画像建议">
                  <a-input v-model="queryParam.jyhxjy" allow-clear placeholder="请输入就业画像建议"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="家庭画像建议">
                  <a-input v-model="queryParam.jthxjy" allow-clear placeholder="请输入家庭画像建议"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="信用画像建议">
                  <a-input v-model="queryParam.xyhxjy" allow-clear placeholder="请输入信用画像建议"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="个人基本画像建议">
                  <a-input v-model="queryParam.jbhxjy" allow-clear placeholder="请输入个人基本画像建议"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="心理画像扣分明细">
                  <a-input v-model="queryParam.xlhxkfmx" allow-clear placeholder="请输入心理画像扣分明细"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="知法画像扣分明细">
                  <a-input v-model="queryParam.zfhxkfmx" allow-clear placeholder="请输入知法画像扣分明细"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="就业画像扣分明细">
                  <a-input v-model="queryParam.jyhxkfmx" allow-clear placeholder="请输入就业画像扣分明细"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="家庭画像扣分明细">
                  <a-input v-model="queryParam.jthxkfmx" allow-clear placeholder="请输入家庭画像扣分明细"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="信用画像扣分明细">
                  <a-input v-model="queryParam.xyhxkfmx" allow-clear placeholder="请输入信用画像扣分明细"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="个人基本画像扣分明细">
                  <a-input v-model="queryParam.jbhxkfmx" allow-clear placeholder="请输入个人基本画像扣分明细"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="心理画像轨迹风险分析">
                  <a-input v-model="queryParam.xlhxgjfx" allow-clear placeholder="请输入心理画像轨迹风险分析"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="知法画像轨迹风险分析">
                  <a-input v-model="queryParam.zfhxgjfx" allow-clear placeholder="请输入知法画像轨迹风险分析"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="就业画像轨迹风险分析">
                  <a-input v-model="queryParam.jyhxgjfx" allow-clear placeholder="请输入就业画像轨迹风险分析"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="家庭画像轨迹风险分析">
                  <a-input v-model="queryParam.jthxgjfx" allow-clear placeholder="请输入家庭画像轨迹风险分析"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="信用画像轨迹风险分析">
                  <a-input v-model="queryParam.xyhxgjfx" allow-clear placeholder="请输入信用画像轨迹风险分析"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="个人基本画像轨迹风险分析">
                  <a-input v-model="queryParam.jbhxgjfx" allow-clear placeholder="请输入个人基本画像轨迹风险分析"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="心理画像风险等级">
                  <a-input v-model="queryParam.xlhxLevel" allow-clear placeholder="请输入心理画像风险等级"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="知法画像风险等级">
                  <a-input v-model="queryParam.zfhxLevel" allow-clear placeholder="请输入知法画像风险等级"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="就业画像风险等级">
                  <a-input v-model="queryParam.jyhxLevel" allow-clear placeholder="请输入就业画像风险等级"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="家庭画像风险等级">
                  <a-input v-model="queryParam.jthxLevel" allow-clear placeholder="请输入家庭画像风险等级"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="信用画像风险等级">
                  <a-input v-model="queryParam.xyhxLevel" allow-clear placeholder="请输入信用画像风险等级"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="个人基本画像风险等级">
                  <a-input v-model="queryParam.jbhxLevel" allow-clear placeholder="请输入个人基本画像风险等级"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="心理画像轨迹风险分析">
                  <a-input v-model="queryParam.xlhxGjqs" allow-clear placeholder="请输入心理画像轨迹风险分析"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="知法画像轨迹风险分析">
                  <a-input v-model="queryParam.zfhxGjqs" allow-clear placeholder="请输入知法画像轨迹风险分析"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="就业画像轨迹风险分析">
                  <a-input v-model="queryParam.jyhxGjqs" allow-clear placeholder="请输入就业画像轨迹风险分析"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="家庭画像轨迹风险分析">
                  <a-input v-model="queryParam.jthxGjqs" allow-clear placeholder="请输入家庭画像轨迹风险分析"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="信用画像轨迹风险分析">
                  <a-input v-model="queryParam.xyhxGjqs" allow-clear placeholder="请输入信用画像轨迹风险分析"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="个人基本画像轨迹风险分析">
                  <a-input v-model="queryParam.jbhxGjqs" allow-clear placeholder="请输入个人基本画像轨迹风险分析"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否删除（0：未删除，1删除）">
                  <a-input v-model="queryParam.delFlag" allow-clear placeholder="请输入是否删除（0：未删除，1删除）"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('correctionEstimatePortrayalParticulars:add')" >
          <a-button type="primary" v-if="hasPerm('correctionEstimatePortrayalParticulars:add')" icon="plus" @click="$refs.addForm.add()">新增心理画像明细（新）</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('correctionEstimatePortrayalParticulars:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('correctionEstimatePortrayalParticulars:edit') & hasPerm('correctionEstimatePortrayalParticulars:delete')"/>
          <a-popconfirm v-if="hasPerm('correctionEstimatePortrayalParticulars:delete')" placement="topRight" title="确认删除？" @confirm="() => correctionEstimatePortrayalParticularsDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { correctionEstimatePortrayalParticularsPage, correctionEstimatePortrayalParticularsDelete } from '@/api/modular/main/correctionestimateportrayalparticulars/correctionEstimatePortrayalParticularsManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '心理画像描述',
            align: 'center',
            dataIndex: 'xlhxms'
          },
          {
            title: '知法画像描述',
            align: 'center',
            dataIndex: 'zfhxms'
          },
          {
            title: '就业画像描述',
            align: 'center',
            dataIndex: 'jyhxms'
          },
          {
            title: '家庭画像描述',
            align: 'center',
            dataIndex: 'jthxms'
          },
          {
            title: '信用画像描述',
            align: 'center',
            dataIndex: 'xyhxms'
          },
          {
            title: '个人基本画像描述',
            align: 'center',
            dataIndex: 'jbhxms'
          },
          {
            title: '心理画像建议',
            align: 'center',
            dataIndex: 'xlhxjy'
          },
          {
            title: '知法画像建议',
            align: 'center',
            dataIndex: 'zfhxjy'
          },
          {
            title: '就业画像建议',
            align: 'center',
            dataIndex: 'jyhxjy'
          },
          {
            title: '家庭画像建议',
            align: 'center',
            dataIndex: 'jthxjy'
          },
          {
            title: '信用画像建议',
            align: 'center',
            dataIndex: 'xyhxjy'
          },
          {
            title: '个人基本画像建议',
            align: 'center',
            dataIndex: 'jbhxjy'
          },
          {
            title: '心理画像扣分明细',
            align: 'center',
            dataIndex: 'xlhxkfmx'
          },
          {
            title: '知法画像扣分明细',
            align: 'center',
            dataIndex: 'zfhxkfmx'
          },
          {
            title: '就业画像扣分明细',
            align: 'center',
            dataIndex: 'jyhxkfmx'
          },
          {
            title: '家庭画像扣分明细',
            align: 'center',
            dataIndex: 'jthxkfmx'
          },
          {
            title: '信用画像扣分明细',
            align: 'center',
            dataIndex: 'xyhxkfmx'
          },
          {
            title: '个人基本画像扣分明细',
            align: 'center',
            dataIndex: 'jbhxkfmx'
          },
          {
            title: '心理画像轨迹风险分析',
            align: 'center',
            dataIndex: 'xlhxgjfx'
          },
          {
            title: '知法画像轨迹风险分析',
            align: 'center',
            dataIndex: 'zfhxgjfx'
          },
          {
            title: '就业画像轨迹风险分析',
            align: 'center',
            dataIndex: 'jyhxgjfx'
          },
          {
            title: '家庭画像轨迹风险分析',
            align: 'center',
            dataIndex: 'jthxgjfx'
          },
          {
            title: '信用画像轨迹风险分析',
            align: 'center',
            dataIndex: 'xyhxgjfx'
          },
          {
            title: '个人基本画像轨迹风险分析',
            align: 'center',
            dataIndex: 'jbhxgjfx'
          },
          {
            title: '心理画像风险等级',
            align: 'center',
            dataIndex: 'xlhxLevel'
          },
          {
            title: '知法画像风险等级',
            align: 'center',
            dataIndex: 'zfhxLevel'
          },
          {
            title: '就业画像风险等级',
            align: 'center',
            dataIndex: 'jyhxLevel'
          },
          {
            title: '家庭画像风险等级',
            align: 'center',
            dataIndex: 'jthxLevel'
          },
          {
            title: '信用画像风险等级',
            align: 'center',
            dataIndex: 'xyhxLevel'
          },
          {
            title: '个人基本画像风险等级',
            align: 'center',
            dataIndex: 'jbhxLevel'
          },
          {
            title: '心理画像权重总分',
            align: 'center',
            dataIndex: 'xlhxZf'
          },
          {
            title: '知法画像权重总分',
            align: 'center',
            dataIndex: 'zfhxZf'
          },
          {
            title: '就业画像权重总分',
            align: 'center',
            dataIndex: 'jyhxZf'
          },
          {
            title: '家庭画像权重总分',
            align: 'center',
            dataIndex: 'jthxZf'
          },
          {
            title: '信用画像权重总分',
            align: 'center',
            dataIndex: 'xyhxZf'
          },
          {
            title: '个人基本画像权重总分',
            align: 'center',
            dataIndex: 'jbhxZf'
          },
          {
            title: '心理画像扣分',
            align: 'center',
            dataIndex: 'xlhxKf'
          },
          {
            title: '知法画像扣分',
            align: 'center',
            dataIndex: 'zfhxKf'
          },
          {
            title: '就业画像扣分',
            align: 'center',
            dataIndex: 'jyhxKf'
          },
          {
            title: '家庭画像扣分',
            align: 'center',
            dataIndex: 'jthxKf'
          },
          {
            title: '信用画像扣分',
            align: 'center',
            dataIndex: 'xyhxKf'
          },
          {
            title: '个人基本画像扣分',
            align: 'center',
            dataIndex: 'jbhxKf'
          },
          {
            title: '心理画像轨迹风险分析',
            align: 'center',
            dataIndex: 'xlhxGjqs'
          },
          {
            title: '知法画像轨迹风险分析',
            align: 'center',
            dataIndex: 'zfhxGjqs'
          },
          {
            title: '就业画像轨迹风险分析',
            align: 'center',
            dataIndex: 'jyhxGjqs'
          },
          {
            title: '家庭画像轨迹风险分析',
            align: 'center',
            dataIndex: 'jthxGjqs'
          },
          {
            title: '信用画像轨迹风险分析',
            align: 'center',
            dataIndex: 'xyhxGjqs'
          },
          {
            title: '个人基本画像轨迹风险分析',
            align: 'center',
            dataIndex: 'jbhxGjqs'
          },
          {
            title: '是否删除（0：未删除，1删除）',
            align: 'center',
            dataIndex: 'delFlag'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return correctionEstimatePortrayalParticularsPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('correctionEstimatePortrayalParticulars:edit') || this.hasPerm('correctionEstimatePortrayalParticulars:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      correctionEstimatePortrayalParticularsDelete (record) {
        correctionEstimatePortrayalParticularsDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
