<template>
  <a-modal
    title="编辑心理画像明细（新）"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="心理画像描述"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入心理画像描述" v-decorator="['xlhxms', {rules: [{required: true, message: '请输入心理画像描述！'}]}]" />
        </a-form-item>
        <a-form-item
          label="知法画像描述"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入知法画像描述" v-decorator="['zfhxms', {rules: [{required: true, message: '请输入知法画像描述！'}]}]" />
        </a-form-item>
        <a-form-item
          label="就业画像描述"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入就业画像描述" v-decorator="['jyhxms', {rules: [{required: true, message: '请输入就业画像描述！'}]}]" />
        </a-form-item>
        <a-form-item
          label="家庭画像描述"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入家庭画像描述" v-decorator="['jthxms', {rules: [{required: true, message: '请输入家庭画像描述！'}]}]" />
        </a-form-item>
        <a-form-item
          label="信用画像描述"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入信用画像描述" v-decorator="['xyhxms', {rules: [{required: true, message: '请输入信用画像描述！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个人基本画像描述"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个人基本画像描述" v-decorator="['jbhxms', {rules: [{required: true, message: '请输入个人基本画像描述！'}]}]" />
        </a-form-item>
        <a-form-item
          label="心理画像建议"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入心理画像建议" v-decorator="['xlhxjy', {rules: [{required: true, message: '请输入心理画像建议！'}]}]" />
        </a-form-item>
        <a-form-item
          label="知法画像建议"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入知法画像建议" v-decorator="['zfhxjy', {rules: [{required: true, message: '请输入知法画像建议！'}]}]" />
        </a-form-item>
        <a-form-item
          label="就业画像建议"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入就业画像建议" v-decorator="['jyhxjy', {rules: [{required: true, message: '请输入就业画像建议！'}]}]" />
        </a-form-item>
        <a-form-item
          label="家庭画像建议"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入家庭画像建议" v-decorator="['jthxjy', {rules: [{required: true, message: '请输入家庭画像建议！'}]}]" />
        </a-form-item>
        <a-form-item
          label="信用画像建议"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入信用画像建议" v-decorator="['xyhxjy', {rules: [{required: true, message: '请输入信用画像建议！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个人基本画像建议"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个人基本画像建议" v-decorator="['jbhxjy', {rules: [{required: true, message: '请输入个人基本画像建议！'}]}]" />
        </a-form-item>
        <a-form-item
          label="心理画像扣分明细"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入心理画像扣分明细" v-decorator="['xlhxkfmx', {rules: [{required: true, message: '请输入心理画像扣分明细！'}]}]" />
        </a-form-item>
        <a-form-item
          label="知法画像扣分明细"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入知法画像扣分明细" v-decorator="['zfhxkfmx', {rules: [{required: true, message: '请输入知法画像扣分明细！'}]}]" />
        </a-form-item>
        <a-form-item
          label="就业画像扣分明细"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入就业画像扣分明细" v-decorator="['jyhxkfmx', {rules: [{required: true, message: '请输入就业画像扣分明细！'}]}]" />
        </a-form-item>
        <a-form-item
          label="家庭画像扣分明细"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入家庭画像扣分明细" v-decorator="['jthxkfmx', {rules: [{required: true, message: '请输入家庭画像扣分明细！'}]}]" />
        </a-form-item>
        <a-form-item
          label="信用画像扣分明细"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入信用画像扣分明细" v-decorator="['xyhxkfmx', {rules: [{required: true, message: '请输入信用画像扣分明细！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个人基本画像扣分明细"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个人基本画像扣分明细" v-decorator="['jbhxkfmx', {rules: [{required: true, message: '请输入个人基本画像扣分明细！'}]}]" />
        </a-form-item>
        <a-form-item
          label="心理画像轨迹风险分析"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入心理画像轨迹风险分析" v-decorator="['xlhxgjfx', {rules: [{required: true, message: '请输入心理画像轨迹风险分析！'}]}]" />
        </a-form-item>
        <a-form-item
          label="知法画像轨迹风险分析"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入知法画像轨迹风险分析" v-decorator="['zfhxgjfx', {rules: [{required: true, message: '请输入知法画像轨迹风险分析！'}]}]" />
        </a-form-item>
        <a-form-item
          label="就业画像轨迹风险分析"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入就业画像轨迹风险分析" v-decorator="['jyhxgjfx', {rules: [{required: true, message: '请输入就业画像轨迹风险分析！'}]}]" />
        </a-form-item>
        <a-form-item
          label="家庭画像轨迹风险分析"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入家庭画像轨迹风险分析" v-decorator="['jthxgjfx', {rules: [{required: true, message: '请输入家庭画像轨迹风险分析！'}]}]" />
        </a-form-item>
        <a-form-item
          label="信用画像轨迹风险分析"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入信用画像轨迹风险分析" v-decorator="['xyhxgjfx', {rules: [{required: true, message: '请输入信用画像轨迹风险分析！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个人基本画像轨迹风险分析"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个人基本画像轨迹风险分析" v-decorator="['jbhxgjfx', {rules: [{required: true, message: '请输入个人基本画像轨迹风险分析！'}]}]" />
        </a-form-item>
        <a-form-item
          label="心理画像风险等级"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入心理画像风险等级" v-decorator="['xlhxLevel', {rules: [{required: true, message: '请输入心理画像风险等级！'}]}]" />
        </a-form-item>
        <a-form-item
          label="知法画像风险等级"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入知法画像风险等级" v-decorator="['zfhxLevel', {rules: [{required: true, message: '请输入知法画像风险等级！'}]}]" />
        </a-form-item>
        <a-form-item
          label="就业画像风险等级"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入就业画像风险等级" v-decorator="['jyhxLevel', {rules: [{required: true, message: '请输入就业画像风险等级！'}]}]" />
        </a-form-item>
        <a-form-item
          label="家庭画像风险等级"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入家庭画像风险等级" v-decorator="['jthxLevel', {rules: [{required: true, message: '请输入家庭画像风险等级！'}]}]" />
        </a-form-item>
        <a-form-item
          label="信用画像风险等级"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入信用画像风险等级" v-decorator="['xyhxLevel', {rules: [{required: true, message: '请输入信用画像风险等级！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个人基本画像风险等级"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个人基本画像风险等级" v-decorator="['jbhxLevel', {rules: [{required: true, message: '请输入个人基本画像风险等级！'}]}]" />
        </a-form-item>
        <a-form-item
          label="心理画像权重总分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="知法画像权重总分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="就业画像权重总分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="家庭画像权重总分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="信用画像权重总分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="个人基本画像权重总分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="心理画像扣分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="知法画像扣分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="就业画像扣分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="家庭画像扣分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="信用画像扣分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="个人基本画像扣分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="心理画像轨迹风险分析"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入心理画像轨迹风险分析" v-decorator="['xlhxGjqs', {rules: [{required: true, message: '请输入心理画像轨迹风险分析！'}]}]" />
        </a-form-item>
        <a-form-item
          label="知法画像轨迹风险分析"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入知法画像轨迹风险分析" v-decorator="['zfhxGjqs', {rules: [{required: true, message: '请输入知法画像轨迹风险分析！'}]}]" />
        </a-form-item>
        <a-form-item
          label="就业画像轨迹风险分析"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入就业画像轨迹风险分析" v-decorator="['jyhxGjqs', {rules: [{required: true, message: '请输入就业画像轨迹风险分析！'}]}]" />
        </a-form-item>
        <a-form-item
          label="家庭画像轨迹风险分析"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入家庭画像轨迹风险分析" v-decorator="['jthxGjqs', {rules: [{required: true, message: '请输入家庭画像轨迹风险分析！'}]}]" />
        </a-form-item>
        <a-form-item
          label="信用画像轨迹风险分析"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入信用画像轨迹风险分析" v-decorator="['xyhxGjqs', {rules: [{required: true, message: '请输入信用画像轨迹风险分析！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个人基本画像轨迹风险分析"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个人基本画像轨迹风险分析" v-decorator="['jbhxGjqs', {rules: [{required: true, message: '请输入个人基本画像轨迹风险分析！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否删除（0：未删除，1删除）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否删除（0：未删除，1删除）" v-decorator="['delFlag', {rules: [{required: true, message: '请输入是否删除（0：未删除，1删除）！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { correctionEstimatePortrayalParticularsEdit } from '@/api/modular/main/correctionestimateportrayalparticulars/correctionEstimatePortrayalParticularsManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              xlhxms: record.xlhxms,
              zfhxms: record.zfhxms,
              jyhxms: record.jyhxms,
              jthxms: record.jthxms,
              xyhxms: record.xyhxms,
              jbhxms: record.jbhxms,
              xlhxjy: record.xlhxjy,
              zfhxjy: record.zfhxjy,
              jyhxjy: record.jyhxjy,
              jthxjy: record.jthxjy,
              xyhxjy: record.xyhxjy,
              jbhxjy: record.jbhxjy,
              xlhxkfmx: record.xlhxkfmx,
              zfhxkfmx: record.zfhxkfmx,
              jyhxkfmx: record.jyhxkfmx,
              jthxkfmx: record.jthxkfmx,
              xyhxkfmx: record.xyhxkfmx,
              jbhxkfmx: record.jbhxkfmx,
              xlhxgjfx: record.xlhxgjfx,
              zfhxgjfx: record.zfhxgjfx,
              jyhxgjfx: record.jyhxgjfx,
              jthxgjfx: record.jthxgjfx,
              xyhxgjfx: record.xyhxgjfx,
              jbhxgjfx: record.jbhxgjfx,
              xlhxLevel: record.xlhxLevel,
              zfhxLevel: record.zfhxLevel,
              jyhxLevel: record.jyhxLevel,
              jthxLevel: record.jthxLevel,
              xyhxLevel: record.xyhxLevel,
              jbhxLevel: record.jbhxLevel,
              xlhxZf: record.xlhxZf,
              zfhxZf: record.zfhxZf,
              jyhxZf: record.jyhxZf,
              jthxZf: record.jthxZf,
              xyhxZf: record.xyhxZf,
              jbhxZf: record.jbhxZf,
              xlhxKf: record.xlhxKf,
              zfhxKf: record.zfhxKf,
              jyhxKf: record.jyhxKf,
              jthxKf: record.jthxKf,
              xyhxKf: record.xyhxKf,
              jbhxKf: record.jbhxKf,
              xlhxGjqs: record.xlhxGjqs,
              zfhxGjqs: record.zfhxGjqs,
              jyhxGjqs: record.jyhxGjqs,
              jthxGjqs: record.jthxGjqs,
              xyhxGjqs: record.xyhxGjqs,
              jbhxGjqs: record.jbhxGjqs,
              delFlag: record.delFlag
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            correctionEstimatePortrayalParticularsEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
