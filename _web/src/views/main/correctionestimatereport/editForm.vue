<template>
  <a-modal
    title="编辑评估报告"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="矫正对象id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正对象id" v-decorator="['sqjzryId', {rules: [{required: true, message: '请输入矫正对象id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['sqjzryName', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 性别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 性别" v-decorator="['xb', {rules: [{required: true, message: '请输入 性别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="性别中文值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入性别中文值" v-decorator="['xbName', {rules: [{required: true, message: '请输入性别中文值！'}]}]" />
        </a-form-item>
        <a-form-item
          label="身份证号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入身份证号" v-decorator="['sfzh', {rules: [{required: true, message: '请输入身份证号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="年龄"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入年龄" v-decorator="['age', {rules: [{required: true, message: '请输入年龄！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否成年"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否成年" v-decorator="['sfcn', {rules: [{required: true, message: '请输入是否成年！'}]}]" />
        </a-form-item>
        <a-form-item
          label="婚姻状况"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入婚姻状况" v-decorator="['hyzk', {rules: [{required: true, message: '请输入婚姻状况！'}]}]" />
        </a-form-item>
        <a-form-item
          label="婚姻状况中文值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入婚姻状况中文值" v-decorator="['hyzkName', {rules: [{required: true, message: '请输入婚姻状况中文值！'}]}]" />
        </a-form-item>
        <a-form-item
          label="文化程度"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入文化程度" v-decorator="['whcd', {rules: [{required: true, message: '请输入文化程度！'}]}]" />
        </a-form-item>
        <a-form-item
          label="文化程度中文值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入文化程度中文值" v-decorator="['whcdName', {rules: [{required: true, message: '请输入文化程度中文值！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正机构ID"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正机构ID" v-decorator="['jzjg', {rules: [{required: true, message: '请输入矫正机构ID！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正机构名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正机构名称" v-decorator="['jzjgName', {rules: [{required: true, message: '请输入矫正机构名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="入娇评估分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="上月评估分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="本月评估分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="评估月份"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入评估月份" v-decorator="['estimateMonth', {rules: [{required: true, message: '请输入评估月份！'}]}]" />
        </a-form-item>
        <a-form-item
          label="累犯惯犯"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入累犯惯犯" v-decorator="['oldLag', {rules: [{required: true, message: '请输入累犯惯犯！'}]}]" />
        </a-form-item>
        <a-form-item
          label="违法犯罪案由"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入违法犯罪案由" v-decorator="['crimeReason', {rules: [{required: true, message: '请输入违法犯罪案由！'}]}]" />
        </a-form-item>
        <a-form-item
          label="社区矫正类别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入社区矫正类别" v-decorator="['correctType', {rules: [{required: true, message: '请输入社区矫正类别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否数罪并罚"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否数罪并罚" v-decorator="['moreCrime', {rules: [{required: true, message: '请输入是否数罪并罚！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否共同犯罪"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否共同犯罪" v-decorator="['togetherCrime', {rules: [{required: true, message: '请输入是否共同犯罪！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否五独"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否五独" v-decorator="['fivePoisons', {rules: [{required: true, message: '请输入是否五独！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否五涉"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否五涉" v-decorator="['fiveInvolvement', {rules: [{required: true, message: '请输入是否五涉！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否四史"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否四史" v-decorator="['fourFamous', {rules: [{required: true, message: '请输入是否四史！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正类别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正类别" v-decorator="['jzlb', {rules: [{required: true, message: '请输入矫正类别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正类别中文值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正类别中文值" v-decorator="['jzlbName', {rules: [{required: true, message: '请输入矫正类别中文值！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否成年中文值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否成年中文值" v-decorator="['sfcnName', {rules: [{required: true, message: '请输入是否成年中文值！'}]}]" />
        </a-form-item>
        <a-form-item
          label="就业就学情况"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入就业就学情况" v-decorator="['jyjxqk', {rules: [{required: true, message: '请输入就业就学情况！'}]}]" />
        </a-form-item>
        <a-form-item
          label="就业就学情况中文值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入就业就学情况中文值" v-decorator="['jyjxqkName', {rules: [{required: true, message: '请输入就业就学情况中文值！'}]}]" />
        </a-form-item>
        <a-form-item
          label="具体罪名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="具体罪名中文值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="是否删除（0：未删除，1删除）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否删除（0：未删除，1删除）" v-decorator="['delFlag', {rules: [{required: true, message: '请输入是否删除（0：未删除，1删除）！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { correctionEstimateReportEdit } from '@/api/modular/main/correctionestimatereport/correctionEstimateReportManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              sqjzryId: record.sqjzryId,
              sqjzryName: record.sqjzryName,
              xb: record.xb,
              xbName: record.xbName,
              sfzh: record.sfzh,
              age: record.age,
              sfcn: record.sfcn,
              hyzk: record.hyzk,
              hyzkName: record.hyzkName,
              whcd: record.whcd,
              whcdName: record.whcdName,
              jzjg: record.jzjg,
              jzjgName: record.jzjgName,
              enterScore: record.enterScore,
              lastMonthScore: record.lastMonthScore,
              scoreEstimate: record.scoreEstimate,
              estimateMonth: record.estimateMonth,
              oldLag: record.oldLag,
              crimeReason: record.crimeReason,
              correctType: record.correctType,
              moreCrime: record.moreCrime,
              togetherCrime: record.togetherCrime,
              fivePoisons: record.fivePoisons,
              fiveInvolvement: record.fiveInvolvement,
              fourFamous: record.fourFamous,
              jzlb: record.jzlb,
              jzlbName: record.jzlbName,
              sfcnName: record.sfcnName,
              jyjxqk: record.jyjxqk,
              jyjxqkName: record.jyjxqkName,
              jtzm: record.jtzm,
              jtzmName: record.jtzmName,
              delFlag: record.delFlag
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            correctionEstimateReportEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
