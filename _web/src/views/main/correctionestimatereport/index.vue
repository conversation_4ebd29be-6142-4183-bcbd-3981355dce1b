<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('correctionEstimateReport:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正对象id">
                <a-input v-model="queryParam.sqjzryId" allow-clear placeholder="请输入矫正对象id"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.sqjzryName" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label=" 性别">
                  <a-input v-model="queryParam.xb" allow-clear placeholder="请输入 性别"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="性别中文值">
                  <a-input v-model="queryParam.xbName" allow-clear placeholder="请输入性别中文值"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="身份证号">
                  <a-input v-model="queryParam.sfzh" allow-clear placeholder="请输入身份证号"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="年龄">
                  <a-input v-model="queryParam.age" allow-clear placeholder="请输入年龄"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否成年">
                  <a-input v-model="queryParam.sfcn" allow-clear placeholder="请输入是否成年"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="婚姻状况">
                  <a-input v-model="queryParam.hyzk" allow-clear placeholder="请输入婚姻状况"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="婚姻状况中文值">
                  <a-input v-model="queryParam.hyzkName" allow-clear placeholder="请输入婚姻状况中文值"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="文化程度">
                  <a-input v-model="queryParam.whcd" allow-clear placeholder="请输入文化程度"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="文化程度中文值">
                  <a-input v-model="queryParam.whcdName" allow-clear placeholder="请输入文化程度中文值"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正机构ID">
                  <a-input v-model="queryParam.jzjg" allow-clear placeholder="请输入矫正机构ID"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正机构名称">
                  <a-input v-model="queryParam.jzjgName" allow-clear placeholder="请输入矫正机构名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="评估月份">
                  <a-input v-model="queryParam.estimateMonth" allow-clear placeholder="请输入评估月份"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="累犯惯犯">
                  <a-input v-model="queryParam.oldLag" allow-clear placeholder="请输入累犯惯犯"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="违法犯罪案由">
                  <a-input v-model="queryParam.crimeReason" allow-clear placeholder="请输入违法犯罪案由"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="社区矫正类别">
                  <a-input v-model="queryParam.correctType" allow-clear placeholder="请输入社区矫正类别"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否数罪并罚">
                  <a-input v-model="queryParam.moreCrime" allow-clear placeholder="请输入是否数罪并罚"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否共同犯罪">
                  <a-input v-model="queryParam.togetherCrime" allow-clear placeholder="请输入是否共同犯罪"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否五独">
                  <a-input v-model="queryParam.fivePoisons" allow-clear placeholder="请输入是否五独"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否五涉">
                  <a-input v-model="queryParam.fiveInvolvement" allow-clear placeholder="请输入是否五涉"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否四史">
                  <a-input v-model="queryParam.fourFamous" allow-clear placeholder="请输入是否四史"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正类别">
                  <a-input v-model="queryParam.jzlb" allow-clear placeholder="请输入矫正类别"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正类别中文值">
                  <a-input v-model="queryParam.jzlbName" allow-clear placeholder="请输入矫正类别中文值"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否成年中文值">
                  <a-input v-model="queryParam.sfcnName" allow-clear placeholder="请输入是否成年中文值"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="就业就学情况">
                  <a-input v-model="queryParam.jyjxqk" allow-clear placeholder="请输入就业就学情况"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="就业就学情况中文值">
                  <a-input v-model="queryParam.jyjxqkName" allow-clear placeholder="请输入就业就学情况中文值"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否删除（0：未删除，1删除）">
                  <a-input v-model="queryParam.delFlag" allow-clear placeholder="请输入是否删除（0：未删除，1删除）"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('correctionEstimateReport:add')" >
          <a-button type="primary" v-if="hasPerm('correctionEstimateReport:add')" icon="plus" @click="$refs.addForm.add()">新增评估报告</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('correctionEstimateReport:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('correctionEstimateReport:edit') & hasPerm('correctionEstimateReport:delete')"/>
          <a-popconfirm v-if="hasPerm('correctionEstimateReport:delete')" placement="topRight" title="确认删除？" @confirm="() => correctionEstimateReportDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { correctionEstimateReportPage, correctionEstimateReportDelete } from '@/api/modular/main/correctionestimatereport/correctionEstimateReportManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '矫正对象id',
            align: 'center',
            dataIndex: 'sqjzryId'
          },
          {
            title: '姓名',
            align: 'center',
            dataIndex: 'sqjzryName'
          },
          {
            title: ' 性别',
            align: 'center',
            dataIndex: 'xb'
          },
          {
            title: '性别中文值',
            align: 'center',
            dataIndex: 'xbName'
          },
          {
            title: '身份证号',
            align: 'center',
            dataIndex: 'sfzh'
          },
          {
            title: '年龄',
            align: 'center',
            dataIndex: 'age'
          },
          {
            title: '是否成年',
            align: 'center',
            dataIndex: 'sfcn'
          },
          {
            title: '婚姻状况',
            align: 'center',
            dataIndex: 'hyzk'
          },
          {
            title: '婚姻状况中文值',
            align: 'center',
            dataIndex: 'hyzkName'
          },
          {
            title: '文化程度',
            align: 'center',
            dataIndex: 'whcd'
          },
          {
            title: '文化程度中文值',
            align: 'center',
            dataIndex: 'whcdName'
          },
          {
            title: '矫正机构ID',
            align: 'center',
            dataIndex: 'jzjg'
          },
          {
            title: '矫正机构名称',
            align: 'center',
            dataIndex: 'jzjgName'
          },
          {
            title: '入娇评估分',
            align: 'center',
            dataIndex: 'enterScore'
          },
          {
            title: '上月评估分',
            align: 'center',
            dataIndex: 'lastMonthScore'
          },
          {
            title: '本月评估分',
            align: 'center',
            dataIndex: 'scoreEstimate'
          },
          {
            title: '评估月份',
            align: 'center',
            dataIndex: 'estimateMonth'
          },
          {
            title: '累犯惯犯',
            align: 'center',
            dataIndex: 'oldLag'
          },
          {
            title: '违法犯罪案由',
            align: 'center',
            dataIndex: 'crimeReason'
          },
          {
            title: '社区矫正类别',
            align: 'center',
            dataIndex: 'correctType'
          },
          {
            title: '是否数罪并罚',
            align: 'center',
            dataIndex: 'moreCrime'
          },
          {
            title: '是否共同犯罪',
            align: 'center',
            dataIndex: 'togetherCrime'
          },
          {
            title: '是否五独',
            align: 'center',
            dataIndex: 'fivePoisons'
          },
          {
            title: '是否五涉',
            align: 'center',
            dataIndex: 'fiveInvolvement'
          },
          {
            title: '是否四史',
            align: 'center',
            dataIndex: 'fourFamous'
          },
          {
            title: '矫正类别',
            align: 'center',
            dataIndex: 'jzlb'
          },
          {
            title: '矫正类别中文值',
            align: 'center',
            dataIndex: 'jzlbName'
          },
          {
            title: '是否成年中文值',
            align: 'center',
            dataIndex: 'sfcnName'
          },
          {
            title: '就业就学情况',
            align: 'center',
            dataIndex: 'jyjxqk'
          },
          {
            title: '就业就学情况中文值',
            align: 'center',
            dataIndex: 'jyjxqkName'
          },
          {
            title: '具体罪名',
            align: 'center',
            dataIndex: 'jtzm'
          },
          {
            title: '具体罪名中文值',
            align: 'center',
            dataIndex: 'jtzmName'
          },
          {
            title: '是否删除（0：未删除，1删除）',
            align: 'center',
            dataIndex: 'delFlag'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return correctionEstimateReportPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('correctionEstimateReport:edit') || this.hasPerm('correctionEstimateReport:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      correctionEstimateReportDelete (record) {
        correctionEstimateReportDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
