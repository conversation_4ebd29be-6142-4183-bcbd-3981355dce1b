<template>
  <a-modal
    title="新增风险预警日志"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="风险评估id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入风险评估id" v-decorator="['pid', {rules: [{required: true, message: '请输入风险评估id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="风险指标id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入风险指标id" v-decorator="['riskIndexId', {rules: [{required: true, message: '请输入风险指标id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="风险因素"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入风险因素" v-decorator="['riskReason', {rules: [{required: true, message: '请输入风险因素！'}]}]" />
        </a-form-item>
        <a-form-item
          label="预警日志类型（1-日常监管，2-心理评估，3-综合评估"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入预警日志类型（1-日常监管，2-心理评估，3-综合评估" v-decorator="['logType', {rules: [{required: true, message: '请输入预警日志类型（1-日常监管，2-心理评估，3-综合评估！'}]}]" />
        </a-form-item>
        <a-form-item
          label="结果"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入结果" v-decorator="['result', {rules: [{required: true, message: '请输入结果！'}]}]" />
        </a-form-item>
        <a-form-item
          label="发生时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入发生时间" v-decorator="['occurTime', {rules: [{required: true, message: '请输入发生时间！'}]}]" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="较上月"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入较上月" v-decorator="['comparedLastMonth', {rules: [{required: true, message: '请输入较上月！'}]}]" />
        </a-form-item>
        <a-form-item
          label="比较类别（0-文字，1-数值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入比较类别（0-文字，1-数值" v-decorator="['comparedType', {rules: [{required: true, message: '请输入比较类别（0-文字，1-数值！'}]}]" />
        </a-form-item>
        <a-form-item
          label="来源"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入来源" v-decorator="['sourceType', {rules: [{required: true, message: '请输入来源！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { wxRiskWarningLogAdd } from '@/api/modular/main/wxriskwarninglog/wxRiskWarningLogManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            wxRiskWarningLogAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
