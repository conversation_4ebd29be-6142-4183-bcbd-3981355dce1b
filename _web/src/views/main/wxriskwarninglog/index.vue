<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('wxRiskWarningLog:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="风险评估id">
                <a-input v-model="queryParam.pid" allow-clear placeholder="请输入风险评估id"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="风险指标id">
                <a-input v-model="queryParam.riskIndexId" allow-clear placeholder="请输入风险指标id"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="风险因素">
                  <a-input v-model="queryParam.riskReason" allow-clear placeholder="请输入风险因素"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="预警日志类型（1-日常监管，2-心理评估，3-综合评估">
                  <a-input v-model="queryParam.logType" allow-clear placeholder="请输入预警日志类型（1-日常监管，2-心理评估，3-综合评估"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="结果">
                  <a-input v-model="queryParam.result" allow-clear placeholder="请输入结果"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="发生时间">
                  <a-input v-model="queryParam.occurTime" allow-clear placeholder="请输入发生时间"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="较上月">
                  <a-input v-model="queryParam.comparedLastMonth" allow-clear placeholder="请输入较上月"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="比较类别（0-文字，1-数值">
                  <a-input v-model="queryParam.comparedType" allow-clear placeholder="请输入比较类别（0-文字，1-数值"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="来源">
                  <a-input v-model="queryParam.sourceType" allow-clear placeholder="请输入来源"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('wxRiskWarningLog:add')" >
          <a-button type="primary" v-if="hasPerm('wxRiskWarningLog:add')" icon="plus" @click="$refs.addForm.add()">新增风险预警日志</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('wxRiskWarningLog:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('wxRiskWarningLog:edit') & hasPerm('wxRiskWarningLog:delete')"/>
          <a-popconfirm v-if="hasPerm('wxRiskWarningLog:delete')" placement="topRight" title="确认删除？" @confirm="() => wxRiskWarningLogDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { wxRiskWarningLogPage, wxRiskWarningLogDelete } from '@/api/modular/main/wxriskwarninglog/wxRiskWarningLogManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '风险评估id',
            align: 'center',
            dataIndex: 'pid'
          },
          {
            title: '风险指标id',
            align: 'center',
            dataIndex: 'riskIndexId'
          },
          {
            title: '风险因素',
            align: 'center',
            dataIndex: 'riskReason'
          },
          {
            title: '预警日志类型（1-日常监管，2-心理评估，3-综合评估',
            align: 'center',
            dataIndex: 'logType'
          },
          {
            title: '结果',
            align: 'center',
            dataIndex: 'result'
          },
          {
            title: '发生时间',
            align: 'center',
            dataIndex: 'occurTime'
          },
          {
            title: '较上月',
            align: 'center',
            dataIndex: 'comparedLastMonth'
          },
          {
            title: '比较类别（0-文字，1-数值',
            align: 'center',
            dataIndex: 'comparedType'
          },
          {
            title: '来源',
            align: 'center',
            dataIndex: 'sourceType'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return wxRiskWarningLogPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('wxRiskWarningLog:edit') || this.hasPerm('wxRiskWarningLog:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      wxRiskWarningLogDelete (record) {
        wxRiskWarningLogDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
