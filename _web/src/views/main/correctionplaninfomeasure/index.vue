<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('correctionPlanInfoMeasure:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正方案详情id">
                <a-input v-model="queryParam.pid" allow-clear placeholder="请输入矫正方案详情id"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="措施类型">
                <a-input v-model="queryParam.measureType" allow-clear placeholder="请输入措施类型"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="频次">
                  <a-input v-model="queryParam.frequency" allow-clear placeholder="请输入频次"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="单位">
                  <a-input v-model="queryParam.unit" allow-clear placeholder="请输入单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否完成（0-未完成；1-已完成）">
                  <a-input v-model="queryParam.finished" allow-clear placeholder="请输入是否完成（0-未完成；1-已完成）"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('correctionPlanInfoMeasure:add')" >
          <a-button type="primary" v-if="hasPerm('correctionPlanInfoMeasure:add')" icon="plus" @click="$refs.addForm.add()">新增矫正方案2.0措施详情</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('correctionPlanInfoMeasure:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('correctionPlanInfoMeasure:edit') & hasPerm('correctionPlanInfoMeasure:delete')"/>
          <a-popconfirm v-if="hasPerm('correctionPlanInfoMeasure:delete')" placement="topRight" title="确认删除？" @confirm="() => correctionPlanInfoMeasureDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { correctionPlanInfoMeasurePage, correctionPlanInfoMeasureDelete } from '@/api/modular/main/correctionplaninfomeasure/correctionPlanInfoMeasureManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '矫正方案详情id',
            align: 'center',
            dataIndex: 'pid'
          },
          {
            title: '措施类型',
            align: 'center',
            dataIndex: 'measureType'
          },
          {
            title: '频次',
            align: 'center',
            dataIndex: 'frequency'
          },
          {
            title: '单位',
            align: 'center',
            dataIndex: 'unit'
          },
          {
            title: '是否完成（0-未完成；1-已完成）',
            align: 'center',
            dataIndex: 'finished'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return correctionPlanInfoMeasurePage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('correctionPlanInfoMeasure:edit') || this.hasPerm('correctionPlanInfoMeasure:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      correctionPlanInfoMeasureDelete (record) {
        correctionPlanInfoMeasureDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
