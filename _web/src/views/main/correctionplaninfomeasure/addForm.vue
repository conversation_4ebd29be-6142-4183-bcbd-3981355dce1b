<template>
  <a-modal
    title="新增矫正方案2.0措施详情"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="矫正方案详情id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正方案详情id" v-decorator="['pid', {rules: [{required: true, message: '请输入矫正方案详情id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="措施类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入措施类型" v-decorator="['measureType', {rules: [{required: true, message: '请输入措施类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="频次"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入频次" v-decorator="['frequency', {rules: [{required: true, message: '请输入频次！'}]}]" />
        </a-form-item>
        <a-form-item
          label="单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入单位" v-decorator="['unit', {rules: [{required: true, message: '请输入单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否完成（0-未完成；1-已完成）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否完成（0-未完成；1-已完成）" v-decorator="['finished', {rules: [{required: true, message: '请输入是否完成（0-未完成；1-已完成）！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { correctionPlanInfoMeasureAdd } from '@/api/modular/main/correctionplaninfomeasure/correctionPlanInfoMeasureManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            correctionPlanInfoMeasureAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
