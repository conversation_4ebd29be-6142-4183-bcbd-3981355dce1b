<template>
  <a-modal
    title="编辑吴兴入矫评估"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="矫正对象id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正对象id" v-decorator="['sqjzryId', {rules: [{required: true, message: '请输入矫正对象id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['sqjzryName', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="身份证号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入身份证号" v-decorator="['sfzh', {rules: [{required: true, message: '请输入身份证号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正机构ID"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正机构ID" v-decorator="['jzjg', {rules: [{required: true, message: '请输入矫正机构ID！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正机构名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正机构名称" v-decorator="['jzjgName', {rules: [{required: true, message: '请输入矫正机构名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="总分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input-number placeholder="请输入总分" style="width: 100%" v-decorator="['scoreTotal', {rules: [{required: true, message: '请输入总分！'}]}]" />
        </a-form-item>
        <a-form-item
          label="评估分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input-number placeholder="请输入评估分" style="width: 100%" v-decorator="['scoreEstimate', {rules: [{required: true, message: '请输入评估分！'}]}]" />
        </a-form-item>
        <a-form-item
          label="年龄"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入年龄" v-decorator="['crimeAge', {rules: [{required: true, message: '请输入年龄！'}]}]" />
        </a-form-item>
        <a-form-item
          label="受教育程度"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入受教育程度" v-decorator="['educationLevel', {rules: [{required: true, message: '请输入受教育程度！'}]}]" />
        </a-form-item>
        <a-form-item
          label="婚姻家庭状况"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入婚姻家庭状况" v-decorator="['marriageFamily', {rules: [{required: true, message: '请输入婚姻家庭状况！'}]}]" />
        </a-form-item>
        <a-form-item
          label="就业态度和状况"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入就业态度和状况" v-decorator="['workState', {rules: [{required: true, message: '请输入就业态度和状况！'}]}]" />
        </a-form-item>
        <a-form-item
          label="生活来源"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入生活来源" v-decorator="['lifeSource', {rules: [{required: true, message: '请输入生活来源！'}]}]" />
        </a-form-item>
        <a-form-item
          label="房户一体"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入房户一体" v-decorator="['houseNumberSame', {rules: [{required: true, message: '请输入房户一体！'}]}]" />
        </a-form-item>
        <a-form-item
          label="认罪服法态度"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入认罪服法态度" v-decorator="['guiltyManner', {rules: [{required: true, message: '请输入认罪服法态度！'}]}]" />
        </a-form-item>
        <a-form-item
          label="对现实社会的心态"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入对现实社会的心态" v-decorator="['mentality', {rules: [{required: true, message: '请输入对现实社会的心态！'}]}]" />
        </a-form-item>
        <a-form-item
          label="心理健康状况"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入心理健康状况" v-decorator="['psychicHealth', {rules: [{required: true, message: '请输入心理健康状况！'}]}]" />
        </a-form-item>
        <a-form-item
          label="有精神病史或精神病遗传史"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入有精神病史或精神病遗传史" v-decorator="['mentalDisease', {rules: [{required: true, message: '请输入有精神病史或精神病遗传史！'}]}]" />
        </a-form-item>
        <a-form-item
          label="交友情况"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入交友情况" v-decorator="['makeFriends', {rules: [{required: true, message: '请输入交友情况！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个人成长经历"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个人成长经历" v-decorator="['personGrowUp', {rules: [{required: true, message: '请输入个人成长经历！'}]}]" />
        </a-form-item>
        <a-form-item
          label="家庭成员犯罪记录"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入家庭成员犯罪记录" v-decorator="['familyCrimeRecord', {rules: [{required: true, message: '请输入家庭成员犯罪记录！'}]}]" />
        </a-form-item>
        <a-form-item
          label="家属配合矫正工作"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入家属配合矫正工作" v-decorator="['familyConcertCorrect', {rules: [{required: true, message: '请输入家属配合矫正工作！'}]}]" />
        </a-form-item>
        <a-form-item
          label="过去受刑事处罚记录"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入过去受刑事处罚记录" v-decorator="['criminalPenaltyRecord', {rules: [{required: true, message: '请输入过去受刑事处罚记录！'}]}]" />
        </a-form-item>
        <a-form-item
          label="过去受行政处罚记录"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入过去受行政处罚记录" v-decorator="['administrativePenaltyRecord', {rules: [{required: true, message: '请输入过去受行政处罚记录！'}]}]" />
        </a-form-item>
        <a-form-item
          label="累犯惯犯"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入累犯惯犯" v-decorator="['oldLag', {rules: [{required: true, message: '请输入累犯惯犯！'}]}]" />
        </a-form-item>
        <a-form-item
          label="违法犯罪案由"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入违法犯罪案由" v-decorator="['crimeReason', {rules: [{required: true, message: '请输入违法犯罪案由！'}]}]" />
        </a-form-item>
        <a-form-item
          label="社区矫正类别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入社区矫正类别" v-decorator="['correctType', {rules: [{required: true, message: '请输入社区矫正类别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否共同犯罪"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否共同犯罪" v-decorator="['togetherCrime', {rules: [{required: true, message: '请输入是否共同犯罪！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否五涉"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否五涉" v-decorator="['fiveInvolvement', {rules: [{required: true, message: '请输入是否五涉！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否四史"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否四史" v-decorator="['fourFamous', {rules: [{required: true, message: '请输入是否四史！'}]}]" />
        </a-form-item>
        <a-form-item
          label="状态（0：暂存，1：提交）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入状态（0：暂存，1：提交）" v-decorator="['status', {rules: [{required: true, message: '请输入状态（0：暂存，1：提交）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="评估日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择评估日期" v-decorator="['estimateTime',{rules: [{ required: true, message: '请选择评估日期！' }]}]" @change="onChangeestimateTime"/>
        </a-form-item>
        <a-form-item
          label="是否删除（0：未删除，1删除）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否删除（0：未删除，1删除）" v-decorator="['delFlag', {rules: [{required: true, message: '请输入是否删除（0：未删除，1删除）！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { wxEstimateEnterEdit } from '@/api/modular/main/wxestimateenter/wxEstimateEnterManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        estimateTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              sqjzryId: record.sqjzryId,
              sqjzryName: record.sqjzryName,
              sfzh: record.sfzh,
              jzjg: record.jzjg,
              jzjgName: record.jzjgName,
              scoreTotal: record.scoreTotal,
              scoreEstimate: record.scoreEstimate,
              crimeAge: record.crimeAge,
              educationLevel: record.educationLevel,
              marriageFamily: record.marriageFamily,
              workState: record.workState,
              lifeSource: record.lifeSource,
              houseNumberSame: record.houseNumberSame,
              guiltyManner: record.guiltyManner,
              mentality: record.mentality,
              psychicHealth: record.psychicHealth,
              mentalDisease: record.mentalDisease,
              makeFriends: record.makeFriends,
              personGrowUp: record.personGrowUp,
              familyCrimeRecord: record.familyCrimeRecord,
              familyConcertCorrect: record.familyConcertCorrect,
              criminalPenaltyRecord: record.criminalPenaltyRecord,
              administrativePenaltyRecord: record.administrativePenaltyRecord,
              oldLag: record.oldLag,
              crimeReason: record.crimeReason,
              correctType: record.correctType,
              togetherCrime: record.togetherCrime,
              fiveInvolvement: record.fiveInvolvement,
              fourFamous: record.fourFamous,
              status: record.status,
              delFlag: record.delFlag
            }
          )
        }, 100)
        // 时间单独处理
        if (record.estimateTime != null) {
            this.form.getFieldDecorator('estimateTime', { initialValue: moment(record.estimateTime, 'YYYY-MM-DD') })
        }
        this.estimateTimeDateString = moment(record.estimateTime).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.estimateTime = this.estimateTimeDateString
            wxEstimateEnterEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangeestimateTime(date, dateString) {
        this.estimateTimeDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
