<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('wxEstimateEnter:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正对象id">
                <a-input v-model="queryParam.sqjzryId" allow-clear placeholder="请输入矫正对象id"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.sqjzryName" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="身份证号">
                  <a-input v-model="queryParam.sfzh" allow-clear placeholder="请输入身份证号"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正机构ID">
                  <a-input v-model="queryParam.jzjg" allow-clear placeholder="请输入矫正机构ID"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正机构名称">
                  <a-input v-model="queryParam.jzjgName" allow-clear placeholder="请输入矫正机构名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="总分">
                  <a-input-number v-model="queryParam.scoreTotal" style="width: 100%" allow-clear placeholder="请输入总分"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="评估分">
                  <a-input-number v-model="queryParam.scoreEstimate" style="width: 100%" allow-clear placeholder="请输入评估分"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="年龄">
                  <a-input v-model="queryParam.crimeAge" allow-clear placeholder="请输入年龄"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="受教育程度">
                  <a-input v-model="queryParam.educationLevel" allow-clear placeholder="请输入受教育程度"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="婚姻家庭状况">
                  <a-input v-model="queryParam.marriageFamily" allow-clear placeholder="请输入婚姻家庭状况"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="就业态度和状况">
                  <a-input v-model="queryParam.workState" allow-clear placeholder="请输入就业态度和状况"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="生活来源">
                  <a-input v-model="queryParam.lifeSource" allow-clear placeholder="请输入生活来源"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="房户一体">
                  <a-input v-model="queryParam.houseNumberSame" allow-clear placeholder="请输入房户一体"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="认罪服法态度">
                  <a-input v-model="queryParam.guiltyManner" allow-clear placeholder="请输入认罪服法态度"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="对现实社会的心态">
                  <a-input v-model="queryParam.mentality" allow-clear placeholder="请输入对现实社会的心态"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="心理健康状况">
                  <a-input v-model="queryParam.psychicHealth" allow-clear placeholder="请输入心理健康状况"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="有精神病史或精神病遗传史">
                  <a-input v-model="queryParam.mentalDisease" allow-clear placeholder="请输入有精神病史或精神病遗传史"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="交友情况">
                  <a-input v-model="queryParam.makeFriends" allow-clear placeholder="请输入交友情况"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="个人成长经历">
                  <a-input v-model="queryParam.personGrowUp" allow-clear placeholder="请输入个人成长经历"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="家庭成员犯罪记录">
                  <a-input v-model="queryParam.familyCrimeRecord" allow-clear placeholder="请输入家庭成员犯罪记录"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="家属配合矫正工作">
                  <a-input v-model="queryParam.familyConcertCorrect" allow-clear placeholder="请输入家属配合矫正工作"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="过去受刑事处罚记录">
                  <a-input v-model="queryParam.criminalPenaltyRecord" allow-clear placeholder="请输入过去受刑事处罚记录"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="过去受行政处罚记录">
                  <a-input v-model="queryParam.administrativePenaltyRecord" allow-clear placeholder="请输入过去受行政处罚记录"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="累犯惯犯">
                  <a-input v-model="queryParam.oldLag" allow-clear placeholder="请输入累犯惯犯"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="违法犯罪案由">
                  <a-input v-model="queryParam.crimeReason" allow-clear placeholder="请输入违法犯罪案由"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="社区矫正类别">
                  <a-input v-model="queryParam.correctType" allow-clear placeholder="请输入社区矫正类别"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否共同犯罪">
                  <a-input v-model="queryParam.togetherCrime" allow-clear placeholder="请输入是否共同犯罪"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否五涉">
                  <a-input v-model="queryParam.fiveInvolvement" allow-clear placeholder="请输入是否五涉"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否四史">
                  <a-input v-model="queryParam.fourFamous" allow-clear placeholder="请输入是否四史"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="状态（0：暂存，1：提交）">
                  <a-input v-model="queryParam.status" allow-clear placeholder="请输入状态（0：暂存，1：提交）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="评估日期">
                  <a-date-picker style="width: 100%" placeholder="请选择评估日期" v-model="queryParam.estimateTimeDate" @change="onChangeestimateTime"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否删除（0：未删除，1删除）">
                  <a-input v-model="queryParam.delFlag" allow-clear placeholder="请输入是否删除（0：未删除，1删除）"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('wxEstimateEnter:add')" >
          <a-button type="primary" v-if="hasPerm('wxEstimateEnter:add')" icon="plus" @click="$refs.addForm.add()">新增吴兴入矫评估</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('wxEstimateEnter:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('wxEstimateEnter:edit') & hasPerm('wxEstimateEnter:delete')"/>
          <a-popconfirm v-if="hasPerm('wxEstimateEnter:delete')" placement="topRight" title="确认删除？" @confirm="() => wxEstimateEnterDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { wxEstimateEnterPage, wxEstimateEnterDelete } from '@/api/modular/main/wxestimateenter/wxEstimateEnterManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '矫正对象id',
            align: 'center',
            dataIndex: 'sqjzryId'
          },
          {
            title: '姓名',
            align: 'center',
            dataIndex: 'sqjzryName'
          },
          {
            title: '身份证号',
            align: 'center',
            dataIndex: 'sfzh'
          },
          {
            title: '矫正机构ID',
            align: 'center',
            dataIndex: 'jzjg'
          },
          {
            title: '矫正机构名称',
            align: 'center',
            dataIndex: 'jzjgName'
          },
          {
            title: '总分',
            align: 'center',
            dataIndex: 'scoreTotal'
          },
          {
            title: '评估分',
            align: 'center',
            dataIndex: 'scoreEstimate'
          },
          {
            title: '年龄',
            align: 'center',
            dataIndex: 'crimeAge'
          },
          {
            title: '受教育程度',
            align: 'center',
            dataIndex: 'educationLevel'
          },
          {
            title: '婚姻家庭状况',
            align: 'center',
            dataIndex: 'marriageFamily'
          },
          {
            title: '就业态度和状况',
            align: 'center',
            dataIndex: 'workState'
          },
          {
            title: '生活来源',
            align: 'center',
            dataIndex: 'lifeSource'
          },
          {
            title: '房户一体',
            align: 'center',
            dataIndex: 'houseNumberSame'
          },
          {
            title: '认罪服法态度',
            align: 'center',
            dataIndex: 'guiltyManner'
          },
          {
            title: '对现实社会的心态',
            align: 'center',
            dataIndex: 'mentality'
          },
          {
            title: '心理健康状况',
            align: 'center',
            dataIndex: 'psychicHealth'
          },
          {
            title: '有精神病史或精神病遗传史',
            align: 'center',
            dataIndex: 'mentalDisease'
          },
          {
            title: '交友情况',
            align: 'center',
            dataIndex: 'makeFriends'
          },
          {
            title: '个人成长经历',
            align: 'center',
            dataIndex: 'personGrowUp'
          },
          {
            title: '家庭成员犯罪记录',
            align: 'center',
            dataIndex: 'familyCrimeRecord'
          },
          {
            title: '家属配合矫正工作',
            align: 'center',
            dataIndex: 'familyConcertCorrect'
          },
          {
            title: '过去受刑事处罚记录',
            align: 'center',
            dataIndex: 'criminalPenaltyRecord'
          },
          {
            title: '过去受行政处罚记录',
            align: 'center',
            dataIndex: 'administrativePenaltyRecord'
          },
          {
            title: '累犯惯犯',
            align: 'center',
            dataIndex: 'oldLag'
          },
          {
            title: '违法犯罪案由',
            align: 'center',
            dataIndex: 'crimeReason'
          },
          {
            title: '社区矫正类别',
            align: 'center',
            dataIndex: 'correctType'
          },
          {
            title: '是否共同犯罪',
            align: 'center',
            dataIndex: 'togetherCrime'
          },
          {
            title: '是否五涉',
            align: 'center',
            dataIndex: 'fiveInvolvement'
          },
          {
            title: '是否四史',
            align: 'center',
            dataIndex: 'fourFamous'
          },
          {
            title: '状态（0：暂存，1：提交）',
            align: 'center',
            dataIndex: 'status'
          },
          {
            title: '评估日期',
            align: 'center',
            dataIndex: 'estimateTime'
          },
          {
            title: '是否删除（0：未删除，1删除）',
            align: 'center',
            dataIndex: 'delFlag'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return wxEstimateEnterPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('wxEstimateEnter:edit') || this.hasPerm('wxEstimateEnter:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const queryParamestimateTime = this.queryParam.estimateTimeDate
        if (queryParamestimateTime != null) {
            this.queryParam.estimateTime = moment(queryParamestimateTime).format('YYYY-MM-DD')
            if (queryParamestimateTime.length < 1) {
                delete this.queryParam.estimateTime
            }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        return obj
      },
      wxEstimateEnterDelete (record) {
        wxEstimateEnterDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      onChangeestimateTime(date, dateString) {
        this.estimateTimeDateString = dateString
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
