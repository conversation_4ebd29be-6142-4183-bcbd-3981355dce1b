<template>
  <a-modal
    title="新增风险评估变动日志"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="矫正对象id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正对象id" v-decorator="['jzdxId', {rules: [{required: true, message: '请输入矫正对象id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['xm', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正单位id" v-decorator="['jzjg', {rules: [{required: true, message: '请输入矫正单位id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正单位" v-decorator="['jzjgName', {rules: [{required: true, message: '请输入矫正单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="评估月份"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入评估月份" v-decorator="['assessMonth', {rules: [{required: true, message: '请输入评估月份！'}]}]" />
        </a-form-item>
        <a-form-item
          label="风险码（1-绿码，2-蓝，3-黄，4-橙，5-红"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入风险码（1-绿码，2-蓝，3-黄，4-橙，5-红" v-decorator="['riskCode', {rules: [{required: true, message: '请输入风险码（1-绿码，2-蓝，3-黄，4-橙，5-红！'}]}]" />
        </a-form-item>
        <a-form-item
          label="风险等级（1-低风险，2-较低风险，3-中风险，4-较高风险，5-高风险"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入风险等级（1-低风险，2-较低风险，3-中风险，4-较高风险，5-高风险" v-decorator="['riskLevel', {rules: [{required: true, message: '请输入风险等级（1-低风险，2-较低风险，3-中风险，4-较高风险，5-高风险！'}]}]" />
        </a-form-item>
        <a-form-item
          label="风险得分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入风险得分" v-decorator="['riskScore', {rules: [{required: true, message: '请输入风险得分！'}]}]" />
        </a-form-item>
        <a-form-item
          label="变码原因"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入变码原因" v-decorator="['changeReason', {rules: [{required: true, message: '请输入变码原因！'}]}]" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="风险原因"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入风险原因" v-decorator="['riskReason', {rules: [{required: true, message: '请输入风险原因！'}]}]" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="监管措施"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入监管措施" v-decorator="['regulatoryMeasure', {rules: [{required: true, message: '请输入监管措施！'}]}]" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="处理状态(0-待处理，1-已处理）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入处理状态(0-待处理，1-已处理）" v-decorator="['dealStatus', {rules: [{required: true, message: '请输入处理状态(0-待处理，1-已处理）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="附件id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入附件id" v-decorator="['attachment', {rules: [{required: true, message: '请输入附件id！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { wxRiskAssessmentLogAdd } from '@/api/modular/main/wxriskassessmentlog/wxRiskAssessmentLogManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            wxRiskAssessmentLogAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
