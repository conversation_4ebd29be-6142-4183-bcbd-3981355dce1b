<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('correctionPlanInfo:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正对象id">
                <a-input v-model="queryParam.jzdxId" allow-clear placeholder="请输入矫正对象id"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="方案类型（0-入矫方案；1-在矫方案）">
                <a-input v-model="queryParam.type" allow-clear placeholder="请输入方案类型（0-入矫方案；1-在矫方案）"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="基本情况标签">
                  <a-input v-model="queryParam.basicLabel" allow-clear placeholder="请输入基本情况标签"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="个人行为及心理特征标签

">
                  <a-input v-model="queryParam.psychologyLabel" allow-clear placeholder="请输入个人行为及心理特征标签

"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="工作、家庭、经济及社会关系标签">
                  <a-input v-model="queryParam.socialLabel" allow-clear placeholder="请输入工作、家庭、经济及社会关系标签"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="处罚及其他情况标签">
                  <a-input v-model="queryParam.punishmentLabel" allow-clear placeholder="请输入处罚及其他情况标签"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="监管措施">
                  <a-input v-model="queryParam.regulatoryMeasure" allow-clear placeholder="请输入监管措施"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="其他措施">
                  <a-input v-model="queryParam.otherMeasure" allow-clear placeholder="请输入其他措施"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否实施其他措施（0-否；1-是）">
                  <a-input v-model="queryParam.otherStatus" allow-clear placeholder="请输入是否实施其他措施（0-否；1-是）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="其他措施实施情况">
                  <a-input v-model="queryParam.otherMeasureCondition" allow-clear placeholder="请输入其他措施实施情况"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="方案效果评估">
                  <a-input v-model="queryParam.evaluation" allow-clear placeholder="请输入方案效果评估"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="状态（0-暂存；1-启用）">
                  <a-input v-model="queryParam.status" allow-clear placeholder="请输入状态（0-暂存；1-启用）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="开始月份">
                  <a-date-picker style="width: 100%" placeholder="请选择开始月份" v-model="queryParam.beginMonthDate" @change="onChangebeginMonth"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="结束月份">
                  <a-date-picker style="width: 100%" placeholder="请选择结束月份" v-model="queryParam.endMonthDate" @change="onChangeendMonth"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('correctionPlanInfo:add')" >
          <a-button type="primary" v-if="hasPerm('correctionPlanInfo:add')" icon="plus" @click="$refs.addForm.add()">新增矫正方案2.0详情</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('correctionPlanInfo:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('correctionPlanInfo:edit') & hasPerm('correctionPlanInfo:delete')"/>
          <a-popconfirm v-if="hasPerm('correctionPlanInfo:delete')" placement="topRight" title="确认删除？" @confirm="() => correctionPlanInfoDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { correctionPlanInfoPage, correctionPlanInfoDelete } from '@/api/modular/main/correctionplaninfo/correctionPlanInfoManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '方案类型（0-入矫方案；1-在矫方案）',
            align: 'center',
            dataIndex: 'type'
          },
          {
            title: '基本情况标签',
            align: 'center',
            dataIndex: 'basicLabel'
          },
          {
            title: '个人行为及心理特征标签

',
            align: 'center',
            dataIndex: 'psychologyLabel'
          },
          {
            title: '工作、家庭、经济及社会关系标签',
            align: 'center',
            dataIndex: 'socialLabel'
          },
          {
            title: '处罚及其他情况标签',
            align: 'center',
            dataIndex: 'punishmentLabel'
          },
          {
            title: '监管措施',
            align: 'center',
            dataIndex: 'regulatoryMeasure'
          },
          {
            title: '其他措施',
            align: 'center',
            dataIndex: 'otherMeasure'
          },
          {
            title: '是否实施其他措施（0-否；1-是）',
            align: 'center',
            dataIndex: 'otherStatus'
          },
          {
            title: '其他措施实施情况',
            align: 'center',
            dataIndex: 'otherMeasureCondition'
          },
          {
            title: '方案效果评估',
            align: 'center',
            dataIndex: 'evaluation'
          },
          {
            title: '状态（0-暂存；1-启用）',
            align: 'center',
            dataIndex: 'status'
          },
          {
            title: '开始月份',
            align: 'center',
            dataIndex: 'beginMonth'
          },
          {
            title: '结束月份',
            align: 'center',
            dataIndex: 'endMonth'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return correctionPlanInfoPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('correctionPlanInfo:edit') || this.hasPerm('correctionPlanInfo:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const queryParambeginMonth = this.queryParam.beginMonthDate
        if (queryParambeginMonth != null) {
            this.queryParam.beginMonth = moment(queryParambeginMonth).format('YYYY-MM-DD')
            if (queryParambeginMonth.length < 1) {
                delete this.queryParam.beginMonth
            }
        }
        const queryParamendMonth = this.queryParam.endMonthDate
        if (queryParamendMonth != null) {
            this.queryParam.endMonth = moment(queryParamendMonth).format('YYYY-MM-DD')
            if (queryParamendMonth.length < 1) {
                delete this.queryParam.endMonth
            }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        return obj
      },
      correctionPlanInfoDelete (record) {
        correctionPlanInfoDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      onChangebeginMonth(date, dateString) {
        this.beginMonthDateString = dateString
      },
      onChangeendMonth(date, dateString) {
        this.endMonthDateString = dateString
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
