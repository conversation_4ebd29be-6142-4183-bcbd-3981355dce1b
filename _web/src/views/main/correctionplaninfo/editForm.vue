<template>
  <a-modal
    title="编辑矫正方案2.0详情"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="矫正对象id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正对象id" v-decorator="['jzdxId']" />
        </a-form-item>
        <a-form-item
          label="方案类型（0-入矫方案；1-在矫方案）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入方案类型（0-入矫方案；1-在矫方案）" v-decorator="['type', {rules: [{required: true, message: '请输入方案类型（0-入矫方案；1-在矫方案）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="基本情况标签"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入基本情况标签" v-decorator="['basicLabel', {rules: [{required: true, message: '请输入基本情况标签！'}]}]" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="个人行为及心理特征标签

"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入个人行为及心理特征标签

" v-decorator="['psychologyLabel', {rules: [{required: true, message: '请输入个人行为及心理特征标签

！'}]}]" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="工作、家庭、经济及社会关系标签"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入工作、家庭、经济及社会关系标签" v-decorator="['socialLabel', {rules: [{required: true, message: '请输入工作、家庭、经济及社会关系标签！'}]}]" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="处罚及其他情况标签"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入处罚及其他情况标签" v-decorator="['punishmentLabel', {rules: [{required: true, message: '请输入处罚及其他情况标签！'}]}]" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="监管措施"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入监管措施" v-decorator="['regulatoryMeasure', {rules: [{required: true, message: '请输入监管措施！'}]}]" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="其他措施"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入其他措施" v-decorator="['otherMeasure', {rules: [{required: true, message: '请输入其他措施！'}]}]" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="是否实施其他措施（0-否；1-是）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否实施其他措施（0-否；1-是）" v-decorator="['otherStatus', {rules: [{required: true, message: '请输入是否实施其他措施（0-否；1-是）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="其他措施实施情况"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入其他措施实施情况" v-decorator="['otherMeasureCondition', {rules: [{required: true, message: '请输入其他措施实施情况！'}]}]" />
        </a-form-item>
        <a-form-item
          label="方案效果评估"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入方案效果评估" v-decorator="['evaluation', {rules: [{required: true, message: '请输入方案效果评估！'}]}]" />
        </a-form-item>
        <a-form-item
          label="状态（0-暂存；1-启用）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入状态（0-暂存；1-启用）" v-decorator="['status', {rules: [{required: true, message: '请输入状态（0-暂存；1-启用）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="开始月份"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择开始月份" v-decorator="['beginMonth',{rules: [{ required: true, message: '请选择开始月份！' }]}]" @change="onChangebeginMonth"/>
        </a-form-item>
        <a-form-item
          label="结束月份"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择结束月份" v-decorator="['endMonth',{rules: [{ required: true, message: '请选择结束月份！' }]}]" @change="onChangeendMonth"/>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { correctionPlanInfoEdit } from '@/api/modular/main/correctionplaninfo/correctionPlanInfoManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        beginMonthDateString: '',
        endMonthDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              jzdxId: record.jzdxId,
              type: record.type,
              basicLabel: record.basicLabel,
              psychologyLabel: record.psychologyLabel,
              socialLabel: record.socialLabel,
              punishmentLabel: record.punishmentLabel,
              regulatoryMeasure: record.regulatoryMeasure,
              otherMeasure: record.otherMeasure,
              otherStatus: record.otherStatus,
              otherMeasureCondition: record.otherMeasureCondition,
              evaluation: record.evaluation,
              status: record.status
            }
          )
        }, 100)
        // 时间单独处理
        if (record.beginMonth != null) {
            this.form.getFieldDecorator('beginMonth', { initialValue: moment(record.beginMonth, 'YYYY-MM-DD') })
        }
        this.beginMonthDateString = moment(record.beginMonth).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.endMonth != null) {
            this.form.getFieldDecorator('endMonth', { initialValue: moment(record.endMonth, 'YYYY-MM-DD') })
        }
        this.endMonthDateString = moment(record.endMonth).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.beginMonth = this.beginMonthDateString
            values.endMonth = this.endMonthDateString
            correctionPlanInfoEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangebeginMonth(date, dateString) {
        this.beginMonthDateString = dateString
      },
      onChangeendMonth(date, dateString) {
        this.endMonthDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
