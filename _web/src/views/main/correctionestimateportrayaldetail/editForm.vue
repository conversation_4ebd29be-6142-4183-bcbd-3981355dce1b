<template>
  <a-modal
    title="编辑画像明细"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="画像类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入画像类型" v-decorator="['type', {rules: [{required: true, message: '请输入画像类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="画像id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入画像id" v-decorator="['portrayalId', {rules: [{required: true, message: '请输入画像id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="画像描述"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入画像描述" v-decorator="['hxms', {rules: [{required: true, message: '请输入画像描述！'}]}]" />
        </a-form-item>
        <a-form-item
          label="轨迹趋势"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入轨迹趋势" v-decorator="['gjqs', {rules: [{required: true, message: '请输入轨迹趋势！'}]}]" />
        </a-form-item>
        <a-form-item
          label="轨迹风险"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入轨迹风险" v-decorator="['xlhxGjfx', {rules: [{required: true, message: '请输入轨迹风险！'}]}]" />
        </a-form-item>
        <a-form-item
          label="改善建议"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入改善建议" v-decorator="['jy', {rules: [{required: true, message: '请输入改善建议！'}]}]" />
        </a-form-item>
        <a-form-item
          label="扣分明细"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入扣分明细" v-decorator="['kfmx', {rules: [{required: true, message: '请输入扣分明细！'}]}]" />
        </a-form-item>
        <a-form-item
          label="权重总分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入权重总分" v-decorator="['zf', {rules: [{required: true, message: '请输入权重总分！'}]}]" />
        </a-form-item>
        <a-form-item
          label="扣分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入扣分" v-decorator="['kf', {rules: [{required: true, message: '请输入扣分！'}]}]" />
        </a-form-item>
        <a-form-item
          label="得分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入得分" v-decorator="['df', {rules: [{required: true, message: '请输入得分！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { correctionEstimatePortrayalDetailEdit } from '@/api/modular/main/correctionestimateportrayaldetail/correctionEstimatePortrayalDetailManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              type: record.type,
              portrayalId: record.portrayalId,
              hxms: record.hxms,
              gjqs: record.gjqs,
              xlhxGjfx: record.xlhxGjfx,
              jy: record.jy,
              kfmx: record.kfmx,
              zf: record.zf,
              kf: record.kf,
              df: record.df
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            correctionEstimatePortrayalDetailEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
