<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('correctionEstimatePortrayalDetail:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="画像类型">
                <a-input v-model="queryParam.type" allow-clear placeholder="请输入画像类型"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="画像id">
                <a-input v-model="queryParam.portrayalId" allow-clear placeholder="请输入画像id"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="画像描述">
                  <a-input v-model="queryParam.hxms" allow-clear placeholder="请输入画像描述"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="轨迹趋势">
                  <a-input v-model="queryParam.gjqs" allow-clear placeholder="请输入轨迹趋势"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="轨迹风险">
                  <a-input v-model="queryParam.xlhxGjfx" allow-clear placeholder="请输入轨迹风险"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="改善建议">
                  <a-input v-model="queryParam.jy" allow-clear placeholder="请输入改善建议"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="扣分明细">
                  <a-input v-model="queryParam.kfmx" allow-clear placeholder="请输入扣分明细"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="权重总分">
                  <a-input v-model="queryParam.zf" allow-clear placeholder="请输入权重总分"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="扣分">
                  <a-input v-model="queryParam.kf" allow-clear placeholder="请输入扣分"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="得分">
                  <a-input v-model="queryParam.df" allow-clear placeholder="请输入得分"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('correctionEstimatePortrayalDetail:add')" >
          <a-button type="primary" v-if="hasPerm('correctionEstimatePortrayalDetail:add')" icon="plus" @click="$refs.addForm.add()">新增画像明细</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('correctionEstimatePortrayalDetail:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('correctionEstimatePortrayalDetail:edit') & hasPerm('correctionEstimatePortrayalDetail:delete')"/>
          <a-popconfirm v-if="hasPerm('correctionEstimatePortrayalDetail:delete')" placement="topRight" title="确认删除？" @confirm="() => correctionEstimatePortrayalDetailDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { correctionEstimatePortrayalDetailPage, correctionEstimatePortrayalDetailDelete } from '@/api/modular/main/correctionestimateportrayaldetail/correctionEstimatePortrayalDetailManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '画像类型',
            align: 'center',
            dataIndex: 'type'
          },
          {
            title: '画像id',
            align: 'center',
            dataIndex: 'portrayalId'
          },
          {
            title: '画像描述',
            align: 'center',
            dataIndex: 'hxms'
          },
          {
            title: '轨迹趋势',
            align: 'center',
            dataIndex: 'gjqs'
          },
          {
            title: '轨迹风险',
            align: 'center',
            dataIndex: 'xlhxGjfx'
          },
          {
            title: '改善建议',
            align: 'center',
            dataIndex: 'jy'
          },
          {
            title: '扣分明细',
            align: 'center',
            dataIndex: 'kfmx'
          },
          {
            title: '权重总分',
            align: 'center',
            dataIndex: 'zf'
          },
          {
            title: '扣分',
            align: 'center',
            dataIndex: 'kf'
          },
          {
            title: '得分',
            align: 'center',
            dataIndex: 'df'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return correctionEstimatePortrayalDetailPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('correctionEstimatePortrayalDetail:edit') || this.hasPerm('correctionEstimatePortrayalDetail:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      correctionEstimatePortrayalDetailDelete (record) {
        correctionEstimatePortrayalDetailDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
