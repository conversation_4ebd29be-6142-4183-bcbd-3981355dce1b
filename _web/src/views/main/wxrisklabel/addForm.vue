<template>
  <a-modal
    title="新增风险标签关联表"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="分类id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入分类id" v-decorator="['typeId', {rules: [{required: true, message: '请输入分类id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="分类名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入分类名称" v-decorator="['typeName', {rules: [{required: true, message: '请输入分类名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="结果"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入结果" v-decorator="['result', {rules: [{required: true, message: '请输入结果！'}]}]" />
        </a-form-item>
        <a-form-item
          label="结果描述"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入结果描述" v-decorator="['resultName', {rules: [{required: true, message: '请输入结果描述！'}]}]" />
        </a-form-item>
        <a-form-item
          label="结果类型（1-字段，2-数量，3-文字"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入结果类型（1-字段，2-数量，3-文字" v-decorator="['resultType', {rules: [{required: true, message: '请输入结果类型（1-字段，2-数量，3-文字！'}]}]" />
        </a-form-item>
        <a-form-item
          label="标签id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入标签id" v-decorator="['labelId', {rules: [{required: true, message: '请输入标签id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="标签名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入标签名称" v-decorator="['labelName', {rules: [{required: true, message: '请输入标签名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="出现次数（仅结果类型为2-数量）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入出现次数（仅结果类型为2-数量）" v-decorator="['amount', {rules: [{required: true, message: '请输入出现次数（仅结果类型为2-数量）！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { wxRiskLabelAdd } from '@/api/modular/main/wxrisklabel/wxRiskLabelManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            wxRiskLabelAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
