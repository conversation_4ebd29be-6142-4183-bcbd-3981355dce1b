<template>
  <a-modal
    title="编辑风险评估表"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="矫正对象id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正对象id" v-decorator="['jzdxId', {rules: [{required: true, message: '请输入矫正对象id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['xm', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正单位id" v-decorator="['jzjg', {rules: [{required: true, message: '请输入矫正单位id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正单位" v-decorator="['jzjgName', {rules: [{required: true, message: '请输入矫正单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="评估月份"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入评估月份" v-decorator="['assessMonth', {rules: [{required: true, message: '请输入评估月份！'}]}]" />
        </a-form-item>
        <a-form-item
          label="风险码（1-绿码，2-蓝，3-黄，4-橙，5-红"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入风险码（1-绿码，2-蓝，3-黄，4-橙，5-红" v-decorator="['riskCode', {rules: [{required: true, message: '请输入风险码（1-绿码，2-蓝，3-黄，4-橙，5-红！'}]}]" />
        </a-form-item>
        <a-form-item
          label="风险等级（1-低风险，2-较低风险，3-中风险，4-较高风险，5-高风险"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入风险等级（1-低风险，2-较低风险，3-中风险，4-较高风险，5-高风险" v-decorator="['riskLevel', {rules: [{required: true, message: '请输入风险等级（1-低风险，2-较低风险，3-中风险，4-较高风险，5-高风险！'}]}]" />
        </a-form-item>
        <a-form-item
          label="变码原因"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入变码原因" v-decorator="['changeReason']" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="风险原因"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入风险原因" v-decorator="['riskReason']" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="监管措施"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入监管措施" v-decorator="['regulatoryMeasure']" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="处理状态"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入处理状态" v-decorator="['dealStatus']" />
        </a-form-item>
        <a-form-item
          label="附件id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入附件id" v-decorator="['attachment']" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { wxRiskAssessmentEdit } from '@/api/modular/main/wxriskassessment/wxRiskAssessmentManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              jzdxId: record.jzdxId,
              xm: record.xm,
              jzjg: record.jzjg,
              jzjgName: record.jzjgName,
              assessMonth: record.assessMonth,
              riskCode: record.riskCode,
              riskLevel: record.riskLevel,
              changeReason: record.changeReason,
              riskReason: record.riskReason,
              regulatoryMeasure: record.regulatoryMeasure,
              dealStatus: record.dealStatus,
              attachment: record.attachment
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            wxRiskAssessmentEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
