<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('wxRiskAssessment:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位id">
                <a-input v-model="queryParam.jzjg" allow-clear placeholder="请输入矫正单位id"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="评估月份">
                  <a-input v-model="queryParam.assessMonth" allow-clear placeholder="请输入评估月份"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="风险码（1-绿码，2-蓝，3-黄，4-橙，5-红">
                  <a-input v-model="queryParam.riskCode" allow-clear placeholder="请输入风险码（1-绿码，2-蓝，3-黄，4-橙，5-红"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="变码原因">
                  <a-input v-model="queryParam.changeReason" allow-clear placeholder="请输入变码原因"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="风险原因">
                  <a-input v-model="queryParam.riskReason" allow-clear placeholder="请输入风险原因"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="监管措施">
                  <a-input v-model="queryParam.regulatoryMeasure" allow-clear placeholder="请输入监管措施"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="处理状态">
                  <a-input v-model="queryParam.dealStatus" allow-clear placeholder="请输入处理状态"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="附件id">
                  <a-input v-model="queryParam.attachment" allow-clear placeholder="请输入附件id"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('wxRiskAssessment:add')" >
          <a-button type="primary" v-if="hasPerm('wxRiskAssessment:add')" icon="plus" @click="$refs.addForm.add()">新增风险评估表</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('wxRiskAssessment:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('wxRiskAssessment:edit') & hasPerm('wxRiskAssessment:delete')"/>
          <a-popconfirm v-if="hasPerm('wxRiskAssessment:delete')" placement="topRight" title="确认删除？" @confirm="() => wxRiskAssessmentDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { wxRiskAssessmentPage, wxRiskAssessmentDelete } from '@/api/modular/main/wxriskassessment/wxRiskAssessmentManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '姓名',
            align: 'center',
            dataIndex: 'xm'
          },
          {
            title: '矫正单位',
            align: 'center',
            dataIndex: 'jzjgName'
          },
          {
            title: '评估月份',
            align: 'center',
            dataIndex: 'assessMonth'
          },
          {
            title: '风险码（1-绿码，2-蓝，3-黄，4-橙，5-红',
            align: 'center',
            dataIndex: 'riskCode'
          },
          {
            title: '风险等级（1-低风险，2-较低风险，3-中风险，4-较高风险，5-高风险',
            align: 'center',
            dataIndex: 'riskLevel'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return wxRiskAssessmentPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('wxRiskAssessment:edit') || this.hasPerm('wxRiskAssessment:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      wxRiskAssessmentDelete (record) {
        wxRiskAssessmentDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
