<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('riskConfigurationExecutionRecord:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="配置管理ID">
                <a-input v-model="queryParam.configId" allow-clear placeholder="请输入配置管理ID"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="序号">
                <a-input v-model="queryParam.sequenceNo" allow-clear placeholder="请输入序号"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="发送时间">
                  <a-date-picker style="width: 100%" placeholder="请选择发送时间" v-model="queryParam.sendTimeDate" @change="onChangesendTime"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="发送人员（多个人员用逗号分隔）">
                  <a-input v-model="queryParam.sendUsers" allow-clear placeholder="请输入发送人员（多个人员用逗号分隔）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="发送状态（成功、失败等）">
                  <a-input v-model="queryParam.sendStatus" allow-clear placeholder="请输入发送状态（成功、失败等）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="重试次数">
                  <a-input v-model="queryParam.retryCount" allow-clear placeholder="请输入重试次数"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="错误信息">
                  <a-input v-model="queryParam.errorMessage" allow-clear placeholder="请输入错误信息"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="执行结果详情">
                  <a-input v-model="queryParam.executionResult" allow-clear placeholder="请输入执行结果详情"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="状态（0正常 1停用 2删除）">
                  <a-input v-model="queryParam.status" allow-clear placeholder="请输入状态（0正常 1停用 2删除）"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('riskConfigurationExecutionRecord:add')" >
          <a-button type="primary" v-if="hasPerm('riskConfigurationExecutionRecord:add')" icon="plus" @click="$refs.addForm.add()">新增风险配置任务执行记录表</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('riskConfigurationExecutionRecord:edit') && record.sendStatus !== '成功'" @click="resendRecord(record)">重新发送</a>
          <a-divider type="vertical" v-if="hasPerm('riskConfigurationExecutionRecord:edit') && record.sendStatus !== '成功'"/>
          <a v-if="hasPerm('riskConfigurationExecutionRecord:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('riskConfigurationExecutionRecord:edit') & hasPerm('riskConfigurationExecutionRecord:delete')"/>
          <a-popconfirm v-if="hasPerm('riskConfigurationExecutionRecord:delete')" placement="topRight" title="确认删除？" @confirm="() => riskConfigurationExecutionRecordDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { riskConfigurationExecutionRecordPage, riskConfigurationExecutionRecordDelete, riskConfigurationExecutionRecordResend } from '@/api/gen/main/riskconfigurationexecutionrecord/riskConfigurationExecutionRecordManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '配置管理ID',
            align: 'center',
            dataIndex: 'configId'
          },
          {
            title: '序号',
            align: 'center',
            dataIndex: 'sequenceNo'
          },
          {
            title: '发送时间',
            align: 'center',
            dataIndex: 'sendTime'
          },
          {
            title: '发送人员（多个人员用逗号分隔）',
            align: 'center',
            dataIndex: 'sendUsers'
          },
          {
            title: '发送状态（成功、失败等）',
            align: 'center',
            dataIndex: 'sendStatus'
          },
          {
            title: '重试次数',
            align: 'center',
            dataIndex: 'retryCount'
          },
          {
            title: '错误信息',
            align: 'center',
            dataIndex: 'errorMessage'
          },
          {
            title: '执行结果详情',
            align: 'center',
            dataIndex: 'executionResult'
          },
          {
            title: '状态（0正常 1停用 2删除）',
            align: 'center',
            dataIndex: 'status'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return riskConfigurationExecutionRecordPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('riskConfigurationExecutionRecord:edit') || this.hasPerm('riskConfigurationExecutionRecord:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const queryParamsendTime = this.queryParam.sendTimeDate
        if (queryParamsendTime != null) {
            this.queryParam.sendTime = moment(queryParamsendTime).format('YYYY-MM-DD')
            if (queryParamsendTime.length < 1) {
                delete this.queryParam.sendTime
            }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        return obj
      },
      riskConfigurationExecutionRecordDelete (record) {
        riskConfigurationExecutionRecordDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      resendRecord (record) {
        riskConfigurationExecutionRecordResend(record).then((res) => {
          if (res.success) {
            this.$message.success('重新发送成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('重新发送失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      onChangesendTime(date, dateString) {
        this.sendTimeDateString = dateString
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
