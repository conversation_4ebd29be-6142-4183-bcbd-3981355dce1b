<template>
  <a-modal
    title="新增风险配置任务执行记录表"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="配置管理ID"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入配置管理ID" v-decorator="['configId', {rules: [{required: true, message: '请输入配置管理ID！'}]}]" />
        </a-form-item>
        <a-form-item
          label="序号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入序号" v-decorator="['sequenceNo', {rules: [{required: true, message: '请输入序号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="发送时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择发送时间" v-decorator="['sendTime',{rules: [{ required: true, message: '请选择发送时间！' }]}]" @change="onChangesendTime"/>
        </a-form-item>
        <a-form-item
          label="发送人员（多个人员用逗号分隔）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入发送人员（多个人员用逗号分隔）" v-decorator="['sendUsers', {rules: [{required: true, message: '请输入发送人员（多个人员用逗号分隔）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="发送状态（成功、失败等）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入发送状态（成功、失败等）" v-decorator="['sendStatus', {rules: [{required: true, message: '请输入发送状态（成功、失败等）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="重试次数"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入重试次数" v-decorator="['retryCount', {rules: [{required: true, message: '请输入重试次数！'}]}]" />
        </a-form-item>
        <a-form-item
          label="错误信息"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入错误信息" v-decorator="['errorMessage', {rules: [{required: true, message: '请输入错误信息！'}]}]" />
        </a-form-item>
        <a-form-item
          label="执行结果详情"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入执行结果详情" v-decorator="['executionResult', {rules: [{required: true, message: '请输入执行结果详情！'}]}]" />
        </a-form-item>
        <a-form-item
          label="状态（0正常 1停用 2删除）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入状态（0正常 1停用 2删除）" v-decorator="['status', {rules: [{required: true, message: '请输入状态（0正常 1停用 2删除）！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { riskConfigurationExecutionRecordAdd } from '@/api/gen/main/riskconfigurationexecutionrecord/riskConfigurationExecutionRecordManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        sendTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.sendTime = this.sendTimeDateString
            riskConfigurationExecutionRecordAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangesendTime(date, dateString) {
        this.sendTimeDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
