<template>
  <a-modal
    title="新增标签关联表格和字段"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="标签关联id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入标签关联id" v-decorator="['relationId', {rules: [{required: true, message: '请输入标签关联id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="表id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入表id" v-decorator="['tableId', {rules: [{required: true, message: '请输入表id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="表名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入表名" v-decorator="['tableName', {rules: [{required: true, message: '请输入表名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="表名中文值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入表名中文值" v-decorator="['tableNameCn', {rules: [{required: true, message: '请输入表名中文值！'}]}]" />
        </a-form-item>
        <a-form-item
          label="字段id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入字段id" v-decorator="['fieldId', {rules: [{required: true, message: '请输入字段id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="字段名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入字段名称" v-decorator="['fieldName', {rules: [{required: true, message: '请输入字段名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="字段名中文值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入字段名中文值" v-decorator="['fieldNameCn', {rules: [{required: true, message: '请输入字段名中文值！'}]}]" />
        </a-form-item>
        <a-form-item
          label="选项id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入选项id" v-decorator="['itemId', {rules: [{required: true, message: '请输入选项id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="选项名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入选项名称" v-decorator="['itemName', {rules: [{required: true, message: '请输入选项名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="选项名称中文值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入选项名称中文值" v-decorator="['itemNameCn', {rules: [{required: true, message: '请输入选项名称中文值！'}]}]" />
        </a-form-item>
        <a-form-item
          label="字典id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入字典id" v-decorator="['dictId', {rules: [{required: true, message: '请输入字典id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="字典名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入字典名称" v-decorator="['dictName', {rules: [{required: true, message: '请输入字典名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="字典名称中文值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入字典名称中文值" v-decorator="['dictNameCn', {rules: [{required: true, message: '请输入字典名称中文值！'}]}]" />
        </a-form-item>
        <a-form-item
          label="层级"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入层级" v-decorator="['level', {rules: [{required: true, message: '请输入层级！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { correctionLabelTableFieldAdd } from '@/api/modular/main/correctionlabeltablefield/correctionLabelTableFieldManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            correctionLabelTableFieldAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
