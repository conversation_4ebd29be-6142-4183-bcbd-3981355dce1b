<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('correctionLabelTableField:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="标签关联id">
                <a-input v-model="queryParam.relationId" allow-clear placeholder="请输入标签关联id"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="表id">
                <a-input v-model="queryParam.tableId" allow-clear placeholder="请输入表id"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="表名">
                  <a-input v-model="queryParam.tableName" allow-clear placeholder="请输入表名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="表名中文值">
                  <a-input v-model="queryParam.tableNameCn" allow-clear placeholder="请输入表名中文值"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="字段id">
                  <a-input v-model="queryParam.fieldId" allow-clear placeholder="请输入字段id"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="字段名称">
                  <a-input v-model="queryParam.fieldName" allow-clear placeholder="请输入字段名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="字段名中文值">
                  <a-input v-model="queryParam.fieldNameCn" allow-clear placeholder="请输入字段名中文值"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="选项id">
                  <a-input v-model="queryParam.itemId" allow-clear placeholder="请输入选项id"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="选项名称">
                  <a-input v-model="queryParam.itemName" allow-clear placeholder="请输入选项名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="选项名称中文值">
                  <a-input v-model="queryParam.itemNameCn" allow-clear placeholder="请输入选项名称中文值"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="字典id">
                  <a-input v-model="queryParam.dictId" allow-clear placeholder="请输入字典id"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="字典名称">
                  <a-input v-model="queryParam.dictName" allow-clear placeholder="请输入字典名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="字典名称中文值">
                  <a-input v-model="queryParam.dictNameCn" allow-clear placeholder="请输入字典名称中文值"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="层级">
                  <a-input v-model="queryParam.level" allow-clear placeholder="请输入层级"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('correctionLabelTableField:add')" >
          <a-button type="primary" v-if="hasPerm('correctionLabelTableField:add')" icon="plus" @click="$refs.addForm.add()">新增标签关联表格和字段</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('correctionLabelTableField:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('correctionLabelTableField:edit') & hasPerm('correctionLabelTableField:delete')"/>
          <a-popconfirm v-if="hasPerm('correctionLabelTableField:delete')" placement="topRight" title="确认删除？" @confirm="() => correctionLabelTableFieldDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { correctionLabelTableFieldPage, correctionLabelTableFieldDelete } from '@/api/modular/main/correctionlabeltablefield/correctionLabelTableFieldManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '标签关联id',
            align: 'center',
            dataIndex: 'relationId'
          },
          {
            title: '表id',
            align: 'center',
            dataIndex: 'tableId'
          },
          {
            title: '表名',
            align: 'center',
            dataIndex: 'tableName'
          },
          {
            title: '表名中文值',
            align: 'center',
            dataIndex: 'tableNameCn'
          },
          {
            title: '字段id',
            align: 'center',
            dataIndex: 'fieldId'
          },
          {
            title: '字段名称',
            align: 'center',
            dataIndex: 'fieldName'
          },
          {
            title: '字段名中文值',
            align: 'center',
            dataIndex: 'fieldNameCn'
          },
          {
            title: '选项id',
            align: 'center',
            dataIndex: 'itemId'
          },
          {
            title: '选项名称',
            align: 'center',
            dataIndex: 'itemName'
          },
          {
            title: '选项名称中文值',
            align: 'center',
            dataIndex: 'itemNameCn'
          },
          {
            title: '字典id',
            align: 'center',
            dataIndex: 'dictId'
          },
          {
            title: '字典名称',
            align: 'center',
            dataIndex: 'dictName'
          },
          {
            title: '字典名称中文值',
            align: 'center',
            dataIndex: 'dictNameCn'
          },
          {
            title: '层级',
            align: 'center',
            dataIndex: 'level'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return correctionLabelTableFieldPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('correctionLabelTableField:edit') || this.hasPerm('correctionLabelTableField:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      correctionLabelTableFieldDelete (record) {
        correctionLabelTableFieldDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
