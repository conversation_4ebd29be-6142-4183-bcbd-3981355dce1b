<template>
  <a-modal
    title="编辑矫正方案2.0标签分类与原始标签绑定表"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="矫正方案标签大类（1-基本情况；2-个人行为及心理特征；3-工作、家庭、经济及社会关系；4-处罚及其他情况）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正方案标签大类（1-基本情况；2-个人行为及心理特征；3-工作、家庭、经济及社会关系；4-处罚及其他情况）" v-decorator="['planLabelType', {rules: [{required: true, message: '请输入矫正方案标签大类（1-基本情况；2-个人行为及心理特征；3-工作、家庭、经济及社会关系；4-处罚及其他情况）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="标签id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入标签id" v-decorator="['labelId', {rules: [{required: true, message: '请输入标签id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="标签名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入标签名称" v-decorator="['labelName', {rules: [{required: true, message: '请输入标签名称！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { correctionPlanLabelTypeEdit } from '@/api/modular/main/correctionplanlabeltype/correctionPlanLabelTypeManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              planLabelType: record.planLabelType,
              labelId: record.labelId,
              labelName: record.labelName
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            correctionPlanLabelTypeEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
