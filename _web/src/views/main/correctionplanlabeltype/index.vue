<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('correctionPlanLabelType:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正方案标签大类（1-基本情况；2-个人行为及心理特征；3-工作、家庭、经济及社会关系；4-处罚及其他情况）">
                <a-input v-model="queryParam.planLabelType" allow-clear placeholder="请输入矫正方案标签大类（1-基本情况；2-个人行为及心理特征；3-工作、家庭、经济及社会关系；4-处罚及其他情况）"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="标签id">
                <a-input v-model="queryParam.labelId" allow-clear placeholder="请输入标签id"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="标签名称">
                  <a-input v-model="queryParam.labelName" allow-clear placeholder="请输入标签名称"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('correctionPlanLabelType:add')" >
          <a-button type="primary" v-if="hasPerm('correctionPlanLabelType:add')" icon="plus" @click="$refs.addForm.add()">新增矫正方案2.0标签分类与原始标签绑定表</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('correctionPlanLabelType:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('correctionPlanLabelType:edit') & hasPerm('correctionPlanLabelType:delete')"/>
          <a-popconfirm v-if="hasPerm('correctionPlanLabelType:delete')" placement="topRight" title="确认删除？" @confirm="() => correctionPlanLabelTypeDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { correctionPlanLabelTypePage, correctionPlanLabelTypeDelete } from '@/api/modular/main/correctionplanlabeltype/correctionPlanLabelTypeManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '矫正方案标签大类（1-基本情况；2-个人行为及心理特征；3-工作、家庭、经济及社会关系；4-处罚及其他情况）',
            align: 'center',
            dataIndex: 'planLabelType'
          },
          {
            title: '标签id',
            align: 'center',
            dataIndex: 'labelId'
          },
          {
            title: '标签名称',
            align: 'center',
            dataIndex: 'labelName'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return correctionPlanLabelTypePage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('correctionPlanLabelType:edit') || this.hasPerm('correctionPlanLabelType:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      correctionPlanLabelTypeDelete (record) {
        correctionPlanLabelTypeDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
