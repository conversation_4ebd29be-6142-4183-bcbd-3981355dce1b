<template>
  <a-modal
    title="编辑风险评估细则表"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="一级指标id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入一级指标id" v-decorator="['levelOneId', {rules: [{required: true, message: '请输入一级指标id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="一级指标名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入一级指标名称" v-decorator="['levelOneLabel', {rules: [{required: true, message: '请输入一级指标名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="一级指标得分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入一级指标得分" v-decorator="['levelOneScore', {rules: [{required: true, message: '请输入一级指标得分！'}]}]" />
        </a-form-item>
        <a-form-item
          label="二级指标id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入二级指标id" v-decorator="['levelTwoId', {rules: [{required: true, message: '请输入二级指标id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="二级指标名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入二级指标名称" v-decorator="['levelTwoLabel', {rules: [{required: true, message: '请输入二级指标名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="二级指标得分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入二级指标得分" v-decorator="['levelTwoScore', {rules: [{required: true, message: '请输入二级指标得分！'}]}]" />
        </a-form-item>
        <a-form-item
          label="三级指标id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入三级指标id" v-decorator="['levelThreeId', {rules: [{required: true, message: '请输入三级指标id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="三级指标名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入三级指标名称" v-decorator="['levelThreeLabel', {rules: [{required: true, message: '请输入三级指标名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="三级指标得分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入三级指标得分" v-decorator="['levelThreeScore', {rules: [{required: true, message: '请输入三级指标得分！'}]}]" />
        </a-form-item>
        <a-form-item
          label="得分类型（0-加分，1-减分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入得分类型（0-加分，1-减分" v-decorator="['scoreType', {rules: [{required: true, message: '请输入得分类型（0-加分，1-减分！'}]}]" />
        </a-form-item>
        <a-form-item
          label="评分模式(0-按次数平均，1-按次数随机，2-按关键字一次性，3-按变动，4-特殊情况1，5-特殊情况2)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入评分模式(0-按次数平均，1-按次数随机，2-按关键字一次性，3-按变动，4-特殊情况1，5-特殊情况2)" v-decorator="['scoreMode', {rules: [{required: true, message: '请输入评分模式(0-按次数平均，1-按次数随机，2-按关键字一次性，3-按变动，4-特殊情况1，5-特殊情况2)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="数据源"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数据源" v-decorator="['ds', {rules: [{required: true, message: '请输入数据源！'}]}]" />
        </a-form-item>
        <a-form-item
          label="数据源名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数据源名称" v-decorator="['dsName', {rules: [{required: true, message: '请输入数据源名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="表名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入表名" v-decorator="['dsTable', {rules: [{required: true, message: '请输入表名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="sql查询语句"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入sql查询语句" v-decorator="['sqlInfo', {rules: [{required: true, message: '请输入sql查询语句！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { wxRiskRuleEdit } from '@/api/modular/main/wxriskrule/wxRiskRuleManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              levelOneId: record.levelOneId,
              levelOneLabel: record.levelOneLabel,
              levelOneScore: record.levelOneScore,
              levelTwoId: record.levelTwoId,
              levelTwoLabel: record.levelTwoLabel,
              levelTwoScore: record.levelTwoScore,
              levelThreeId: record.levelThreeId,
              levelThreeLabel: record.levelThreeLabel,
              levelThreeScore: record.levelThreeScore,
              scoreType: record.scoreType,
              scoreMode: record.scoreMode,
              ds: record.ds,
              dsName: record.dsName,
              dsTable: record.dsTable,
              sqlInfo: record.sqlInfo
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            wxRiskRuleEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
