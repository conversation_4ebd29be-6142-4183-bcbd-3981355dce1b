<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('wxRiskRule:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="一级指标id">
                <a-input v-model="queryParam.levelOneId" allow-clear placeholder="请输入一级指标id"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="一级指标名称">
                <a-input v-model="queryParam.levelOneLabel" allow-clear placeholder="请输入一级指标名称"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="一级指标得分">
                  <a-input v-model="queryParam.levelOneScore" allow-clear placeholder="请输入一级指标得分"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="二级指标id">
                  <a-input v-model="queryParam.levelTwoId" allow-clear placeholder="请输入二级指标id"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="二级指标名称">
                  <a-input v-model="queryParam.levelTwoLabel" allow-clear placeholder="请输入二级指标名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="二级指标得分">
                  <a-input v-model="queryParam.levelTwoScore" allow-clear placeholder="请输入二级指标得分"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="三级指标id">
                  <a-input v-model="queryParam.levelThreeId" allow-clear placeholder="请输入三级指标id"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="三级指标名称">
                  <a-input v-model="queryParam.levelThreeLabel" allow-clear placeholder="请输入三级指标名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="三级指标得分">
                  <a-input v-model="queryParam.levelThreeScore" allow-clear placeholder="请输入三级指标得分"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="得分类型（0-加分，1-减分">
                  <a-input v-model="queryParam.scoreType" allow-clear placeholder="请输入得分类型（0-加分，1-减分"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="评分模式(0-按次数平均，1-按次数随机，2-按关键字一次性，3-按变动，4-特殊情况1，5-特殊情况2)">
                  <a-input v-model="queryParam.scoreMode" allow-clear placeholder="请输入评分模式(0-按次数平均，1-按次数随机，2-按关键字一次性，3-按变动，4-特殊情况1，5-特殊情况2)"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="数据源">
                  <a-input v-model="queryParam.ds" allow-clear placeholder="请输入数据源"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="数据源名称">
                  <a-input v-model="queryParam.dsName" allow-clear placeholder="请输入数据源名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="表名">
                  <a-input v-model="queryParam.dsTable" allow-clear placeholder="请输入表名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="sql查询语句">
                  <a-input v-model="queryParam.sqlInfo" allow-clear placeholder="请输入sql查询语句"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('wxRiskRule:add')" >
          <a-button type="primary" v-if="hasPerm('wxRiskRule:add')" icon="plus" @click="$refs.addForm.add()">新增风险评估细则表</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('wxRiskRule:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('wxRiskRule:edit') & hasPerm('wxRiskRule:delete')"/>
          <a-popconfirm v-if="hasPerm('wxRiskRule:delete')" placement="topRight" title="确认删除？" @confirm="() => wxRiskRuleDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { wxRiskRulePage, wxRiskRuleDelete } from '@/api/modular/main/wxriskrule/wxRiskRuleManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '一级指标id',
            align: 'center',
            dataIndex: 'levelOneId'
          },
          {
            title: '一级指标名称',
            align: 'center',
            dataIndex: 'levelOneLabel'
          },
          {
            title: '一级指标得分',
            align: 'center',
            dataIndex: 'levelOneScore'
          },
          {
            title: '二级指标id',
            align: 'center',
            dataIndex: 'levelTwoId'
          },
          {
            title: '二级指标名称',
            align: 'center',
            dataIndex: 'levelTwoLabel'
          },
          {
            title: '二级指标得分',
            align: 'center',
            dataIndex: 'levelTwoScore'
          },
          {
            title: '三级指标id',
            align: 'center',
            dataIndex: 'levelThreeId'
          },
          {
            title: '三级指标名称',
            align: 'center',
            dataIndex: 'levelThreeLabel'
          },
          {
            title: '三级指标得分',
            align: 'center',
            dataIndex: 'levelThreeScore'
          },
          {
            title: '得分类型（0-加分，1-减分',
            align: 'center',
            dataIndex: 'scoreType'
          },
          {
            title: '评分模式(0-按次数平均，1-按次数随机，2-按关键字一次性，3-按变动，4-特殊情况1，5-特殊情况2)',
            align: 'center',
            dataIndex: 'scoreMode'
          },
          {
            title: '数据源',
            align: 'center',
            dataIndex: 'ds'
          },
          {
            title: '数据源名称',
            align: 'center',
            dataIndex: 'dsName'
          },
          {
            title: '表名',
            align: 'center',
            dataIndex: 'dsTable'
          },
          {
            title: 'sql查询语句',
            align: 'center',
            dataIndex: 'sqlInfo'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return wxRiskRulePage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('wxRiskRule:edit') || this.hasPerm('wxRiskRule:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      wxRiskRuleDelete (record) {
        wxRiskRuleDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
