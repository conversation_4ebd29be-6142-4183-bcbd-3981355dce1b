<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('correctionEstimateReportDetail:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="评估报告id">
                <a-input v-model="queryParam.reportId" allow-clear placeholder="请输入评估报告id"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="指标">
                <a-input v-model="queryParam.index" allow-clear placeholder="请输入指标"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="指标名称">
                  <a-input v-model="queryParam.indexName" allow-clear placeholder="请输入指标名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="情况描述">
                  <a-input v-model="queryParam.condition" allow-clear placeholder="请输入情况描述"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('correctionEstimateReportDetail:add')" >
          <a-button type="primary" v-if="hasPerm('correctionEstimateReportDetail:add')" icon="plus" @click="$refs.addForm.add()">新增评估报告详情</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('correctionEstimateReportDetail:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('correctionEstimateReportDetail:edit') & hasPerm('correctionEstimateReportDetail:delete')"/>
          <a-popconfirm v-if="hasPerm('correctionEstimateReportDetail:delete')" placement="topRight" title="确认删除？" @confirm="() => correctionEstimateReportDetailDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { correctionEstimateReportDetailPage, correctionEstimateReportDetailDelete } from '@/api/modular/main/correctionestimatereportdetail/correctionEstimateReportDetailManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '评估报告id',
            align: 'center',
            dataIndex: 'reportId'
          },
          {
            title: '指标',
            align: 'center',
            dataIndex: 'index'
          },
          {
            title: '指标名称',
            align: 'center',
            dataIndex: 'indexName'
          },
          {
            title: '当前分数',
            align: 'center',
            dataIndex: 'currentScore'
          },
          {
            title: '情况描述',
            align: 'center',
            dataIndex: 'condition'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return correctionEstimateReportDetailPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('correctionEstimateReportDetail:edit') || this.hasPerm('correctionEstimateReportDetail:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      correctionEstimateReportDetailDelete (record) {
        correctionEstimateReportDetailDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
