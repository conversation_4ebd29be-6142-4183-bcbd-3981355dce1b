<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('correctionLabelDatabaseTable:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="标签id">
                <a-input v-model="queryParam.pid" allow-clear placeholder="请输入标签id"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="数据库">
                <a-input v-model="queryParam.database" allow-clear placeholder="请输入数据库"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="名称">
                  <a-input v-model="queryParam.name" allow-clear placeholder="请输入名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="表名称">
                  <a-input v-model="queryParam.tableName" allow-clear placeholder="请输入表名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="用户身份字段">
                  <a-input v-model="queryParam.identityField" allow-clear placeholder="请输入用户身份字段"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="标签匹配字段">
                  <a-input v-model="queryParam.labelField" allow-clear placeholder="请输入标签匹配字段"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否限制时间">
                  <a-input v-model="queryParam.isLimitedTime" allow-clear placeholder="请输入是否限制时间"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="时间字段">
                  <a-input v-model="queryParam.limitedTimeField" allow-clear placeholder="请输入时间字段"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="层级">
                  <a-input v-model="queryParam.level" allow-clear placeholder="请输入层级"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('correctionLabelDatabaseTable:add')" >
          <a-button type="primary" v-if="hasPerm('correctionLabelDatabaseTable:add')" icon="plus" @click="$refs.addForm.add()">新增标签与数据库表绑定表</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('correctionLabelDatabaseTable:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('correctionLabelDatabaseTable:edit') & hasPerm('correctionLabelDatabaseTable:delete')"/>
          <a-popconfirm v-if="hasPerm('correctionLabelDatabaseTable:delete')" placement="topRight" title="确认删除？" @confirm="() => correctionLabelDatabaseTableDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { correctionLabelDatabaseTablePage, correctionLabelDatabaseTableDelete } from '@/api/modular/main/correctionlabeldatabasetable/correctionLabelDatabaseTableManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '标签id',
            align: 'center',
            dataIndex: 'pid'
          },
          {
            title: '数据库',
            align: 'center',
            dataIndex: 'database'
          },
          {
            title: '名称',
            align: 'center',
            dataIndex: 'name'
          },
          {
            title: '表名称',
            align: 'center',
            dataIndex: 'tableName'
          },
          {
            title: '用户身份字段',
            align: 'center',
            dataIndex: 'identityField'
          },
          {
            title: '标签匹配字段',
            align: 'center',
            dataIndex: 'labelField'
          },
          {
            title: '是否限制时间',
            align: 'center',
            dataIndex: 'isLimitedTime'
          },
          {
            title: '时间字段',
            align: 'center',
            dataIndex: 'limitedTimeField'
          },
          {
            title: '层级',
            align: 'center',
            dataIndex: 'level'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return correctionLabelDatabaseTablePage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('correctionLabelDatabaseTable:edit') || this.hasPerm('correctionLabelDatabaseTable:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      correctionLabelDatabaseTableDelete (record) {
        correctionLabelDatabaseTableDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
