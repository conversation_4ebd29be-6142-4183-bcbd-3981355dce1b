<template>
  <a-modal
    title="新增标签与数据库表绑定表"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="标签id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入标签id" v-decorator="['pid', {rules: [{required: true, message: '请输入标签id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="数据库"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数据库" v-decorator="['database', {rules: [{required: true, message: '请输入数据库！'}]}]" />
        </a-form-item>
        <a-form-item
          label="名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入名称" v-decorator="['name', {rules: [{required: true, message: '请输入名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="表名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入表名称" v-decorator="['tableName', {rules: [{required: true, message: '请输入表名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="用户身份字段"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入用户身份字段" v-decorator="['identityField', {rules: [{required: true, message: '请输入用户身份字段！'}]}]" />
        </a-form-item>
        <a-form-item
          label="标签匹配字段"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入标签匹配字段" v-decorator="['labelField', {rules: [{required: true, message: '请输入标签匹配字段！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否限制时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否限制时间" v-decorator="['isLimitedTime', {rules: [{required: true, message: '请输入是否限制时间！'}]}]" />
        </a-form-item>
        <a-form-item
          label="时间字段"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入时间字段" v-decorator="['limitedTimeField', {rules: [{required: true, message: '请输入时间字段！'}]}]" />
        </a-form-item>
        <a-form-item
          label="层级"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入层级" v-decorator="['level', {rules: [{required: true, message: '请输入层级！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { correctionLabelDatabaseTableAdd } from '@/api/modular/main/correctionlabeldatabasetable/correctionLabelDatabaseTableManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            correctionLabelDatabaseTableAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
