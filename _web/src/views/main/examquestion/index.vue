<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('examQuestion:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="">
                <a-input v-model="queryParam.typeId" allow-clear placeholder="请输入"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="">
                <a-input v-model="queryParam.typeName" allow-clear placeholder="请输入"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="题型">
                  <a-input v-model="queryParam.questionType" allow-clear placeholder="请输入题型"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="">
                  <a-input v-model="queryParam.questionTypeName" allow-clear placeholder="请输入"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="题干（长度不够改为text）">
                  <a-input v-model="queryParam.stem" allow-clear placeholder="请输入题干（长度不够改为text）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="答案（多选题用逗号隔开）">
                  <a-input v-model="queryParam.answer" allow-clear placeholder="请输入答案（多选题用逗号隔开）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="">
                  <a-input v-model="queryParam.createBy" allow-clear placeholder="请输入"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="">
                  <a-input v-model="queryParam.updateBy" allow-clear placeholder="请输入"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="创建所属人">
                  <a-input v-model="queryParam.createDepts" allow-clear placeholder="请输入创建所属人"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('examQuestion:add')" >
          <a-button type="primary" v-if="hasPerm('examQuestion:add')" icon="plus" @click="$refs.addForm.add()">新增考试题目</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('examQuestion:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('examQuestion:edit') & hasPerm('examQuestion:delete')"/>
          <a-popconfirm v-if="hasPerm('examQuestion:delete')" placement="topRight" title="确认删除？" @confirm="() => examQuestionDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { examQuestionPage, examQuestionDelete } from '@/api/modular/main/examquestion/examQuestionManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '',
            align: 'center',
            dataIndex: 'typeId'
          },
          {
            title: '',
            align: 'center',
            dataIndex: 'typeName'
          },
          {
            title: '题型',
            align: 'center',
            dataIndex: 'questionType'
          },
          {
            title: '',
            align: 'center',
            dataIndex: 'questionTypeName'
          },
          {
            title: '排序字段',
            align: 'center',
            dataIndex: 'sort'
          },
          {
            title: '题干（长度不够改为text）',
            align: 'center',
            dataIndex: 'stem'
          },
          {
            title: '答案（多选题用逗号隔开）',
            align: 'center',
            dataIndex: 'answer'
          },
          {
            title: '',
            align: 'center',
            dataIndex: 'createBy'
          },
          {
            title: '',
            align: 'center',
            dataIndex: 'updateBy'
          },
          {
            title: '创建所属人',
            align: 'center',
            dataIndex: 'createDepts'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return examQuestionPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('examQuestion:edit') || this.hasPerm('examQuestion:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      examQuestionDelete (record) {
        examQuestionDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
