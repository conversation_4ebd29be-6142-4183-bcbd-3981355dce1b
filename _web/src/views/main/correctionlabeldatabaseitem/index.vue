<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('correctionLabelDatabaseItem:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="标签id">
                <a-input v-model="queryParam.labelId" allow-clear placeholder="请输入标签id"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="标签">
                <a-input v-model="queryParam.labelName" allow-clear placeholder="请输入标签"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="数据库名称">
                  <a-input v-model="queryParam.databaseName" allow-clear placeholder="请输入数据库名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="表名称">
                  <a-input v-model="queryParam.tableName" allow-clear placeholder="请输入表名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="用户身份字段">
                  <a-input v-model="queryParam.identifyField" allow-clear placeholder="请输入用户身份字段"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="标签匹配字段">
                  <a-input v-model="queryParam.labelField" allow-clear placeholder="请输入标签匹配字段"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="统计类型（1-文本匹配；2-次数统计）">
                  <a-input v-model="queryParam.genType" allow-clear placeholder="请输入统计类型（1-文本匹配；2-次数统计）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="时间起始">
                  <a-input v-model="queryParam.timeFrom" allow-clear placeholder="请输入时间起始"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="文本匹配类型（1-全量匹配；2-模糊匹配；3-多关键字匹配；4-电话核查内容匹配）">
                  <a-input v-model="queryParam.genKeyType" allow-clear placeholder="请输入文本匹配类型（1-全量匹配；2-模糊匹配；3-多关键字匹配；4-电话核查内容匹配）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="最低次数">
                  <a-input v-model="queryParam.genCountLim" allow-clear placeholder="请输入最低次数"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电话核查问题">
                  <a-input v-model="queryParam.genPhoneQuestion" allow-clear placeholder="请输入电话核查问题"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电话核查答案关键字">
                  <a-input v-model="queryParam.genPhoneKeyword" allow-clear placeholder="请输入电话核查答案关键字"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('correctionLabelDatabaseItem:add')" >
          <a-button type="primary" v-if="hasPerm('correctionLabelDatabaseItem:add')" icon="plus" @click="$refs.addForm.add()">新增具体标签与数据库字段绑定表</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('correctionLabelDatabaseItem:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('correctionLabelDatabaseItem:edit') & hasPerm('correctionLabelDatabaseItem:delete')"/>
          <a-popconfirm v-if="hasPerm('correctionLabelDatabaseItem:delete')" placement="topRight" title="确认删除？" @confirm="() => correctionLabelDatabaseItemDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { correctionLabelDatabaseItemPage, correctionLabelDatabaseItemDelete } from '@/api/modular/main/correctionlabeldatabaseitem/correctionLabelDatabaseItemManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '标签id',
            align: 'center',
            dataIndex: 'labelId'
          },
          {
            title: '标签',
            align: 'center',
            dataIndex: 'labelName'
          },
          {
            title: '数据库名称',
            align: 'center',
            dataIndex: 'databaseName'
          },
          {
            title: '表名称',
            align: 'center',
            dataIndex: 'tableName'
          },
          {
            title: '用户身份字段',
            align: 'center',
            dataIndex: 'identifyField'
          },
          {
            title: '标签匹配字段',
            align: 'center',
            dataIndex: 'labelField'
          },
          {
            title: '统计类型（1-文本匹配；2-次数统计）',
            align: 'center',
            dataIndex: 'genType'
          },
          {
            title: '时间起始',
            align: 'center',
            dataIndex: 'timeFrom'
          },
          {
            title: '文本匹配类型（1-全量匹配；2-模糊匹配；3-多关键字匹配；4-电话核查内容匹配）',
            align: 'center',
            dataIndex: 'genKeyType'
          },
          {
            title: '最低次数',
            align: 'center',
            dataIndex: 'genCountLim'
          },
          {
            title: '电话核查问题',
            align: 'center',
            dataIndex: 'genPhoneQuestion'
          },
          {
            title: '电话核查答案关键字',
            align: 'center',
            dataIndex: 'genPhoneKeyword'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return correctionLabelDatabaseItemPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('correctionLabelDatabaseItem:edit') || this.hasPerm('correctionLabelDatabaseItem:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      correctionLabelDatabaseItemDelete (record) {
        correctionLabelDatabaseItemDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
