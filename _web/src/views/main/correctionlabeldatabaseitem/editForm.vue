<template>
  <a-modal
    title="编辑具体标签与数据库字段绑定表"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="标签id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入标签id" v-decorator="['labelId', {rules: [{required: true, message: '请输入标签id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="标签"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入标签" v-decorator="['labelName', {rules: [{required: true, message: '请输入标签！'}]}]" />
        </a-form-item>
        <a-form-item
          label="数据库名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数据库名称" v-decorator="['databaseName', {rules: [{required: true, message: '请输入数据库名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="表名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入表名称" v-decorator="['tableName', {rules: [{required: true, message: '请输入表名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="用户身份字段"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入用户身份字段" v-decorator="['identifyField', {rules: [{required: true, message: '请输入用户身份字段！'}]}]" />
        </a-form-item>
        <a-form-item
          label="标签匹配字段"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入标签匹配字段" v-decorator="['labelField', {rules: [{required: true, message: '请输入标签匹配字段！'}]}]" />
        </a-form-item>
        <a-form-item
          label="统计类型（1-文本匹配；2-次数统计）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入统计类型（1-文本匹配；2-次数统计）" v-decorator="['genType', {rules: [{required: true, message: '请输入统计类型（1-文本匹配；2-次数统计）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="时间起始"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入时间起始" v-decorator="['timeFrom', {rules: [{required: true, message: '请输入时间起始！'}]}]" />
        </a-form-item>
        <a-form-item
          label="文本匹配类型（1-全量匹配；2-模糊匹配；3-多关键字匹配；4-电话核查内容匹配）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入文本匹配类型（1-全量匹配；2-模糊匹配；3-多关键字匹配；4-电话核查内容匹配）" v-decorator="['genKeyType', {rules: [{required: true, message: '请输入文本匹配类型（1-全量匹配；2-模糊匹配；3-多关键字匹配；4-电话核查内容匹配）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="最低次数"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入最低次数" v-decorator="['genCountLim', {rules: [{required: true, message: '请输入最低次数！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电话核查问题"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电话核查问题" v-decorator="['genPhoneQuestion', {rules: [{required: true, message: '请输入电话核查问题！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电话核查答案关键字"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电话核查答案关键字" v-decorator="['genPhoneKeyword', {rules: [{required: true, message: '请输入电话核查答案关键字！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { correctionLabelDatabaseItemEdit } from '@/api/modular/main/correctionlabeldatabaseitem/correctionLabelDatabaseItemManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              labelId: record.labelId,
              labelName: record.labelName,
              databaseName: record.databaseName,
              tableName: record.tableName,
              identifyField: record.identifyField,
              labelField: record.labelField,
              genType: record.genType,
              timeFrom: record.timeFrom,
              genKeyType: record.genKeyType,
              genCountLim: record.genCountLim,
              genPhoneQuestion: record.genPhoneQuestion,
              genPhoneKeyword: record.genPhoneKeyword
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            correctionLabelDatabaseItemEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
