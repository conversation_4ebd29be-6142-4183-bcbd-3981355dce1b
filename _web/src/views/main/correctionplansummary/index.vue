<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('correctionPlanSummary:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="身份证号">
                <a-input v-model="queryParam.sfzh" allow-clear placeholder="请输入身份证号"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="性别">
                  <a-input v-model="queryParam.xb" allow-clear placeholder="请输入性别"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="年龄">
                  <a-input v-model="queryParam.age" allow-clear placeholder="请输入年龄"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="出生日期">
                  <a-date-picker style="width: 100%" placeholder="请选择出生日期" v-model="queryParam.csrqDate" @change="onChangecsrq"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正单位id">
                  <a-input v-model="queryParam.jzjg" allow-clear placeholder="请输入矫正单位id"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正单位">
                  <a-input v-model="queryParam.jzjgName" allow-clear placeholder="请输入矫正单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="具体罪名">
                  <a-input v-model="queryParam.jtzm" allow-clear placeholder="请输入具体罪名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="具体罪名名称">
                  <a-input v-model="queryParam.jtzmName" allow-clear placeholder="请输入具体罪名名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="入矫日期">
                  <a-date-picker style="width: 100%" placeholder="请选择入矫日期" v-model="queryParam.rujiaoriqiDate" @change="onChangerujiaoriqi"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="主要犯罪事实">
                  <a-input v-model="queryParam.zyfzss" allow-clear placeholder="请输入主要犯罪事实"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="现实表现">
                  <a-input v-model="queryParam.realisticPerformance" allow-clear placeholder="请输入现实表现"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="社会反应及心理测试情况">
                  <a-input v-model="queryParam.reactionAndPsychological" allow-clear placeholder="请输入社会反应及心理测试情况"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正意见">
                  <a-input v-model="queryParam.correctionOpinion" allow-clear placeholder="请输入矫正意见"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="上次方案生成时间">
                  <a-date-picker style="width: 100%" placeholder="请选择上次方案生成时间" v-model="queryParam.lastPlanTimeDate" @change="onChangelastPlanTime"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="入矫方案制定状态（0-待制定；1-已制定）">
                  <a-input v-model="queryParam.startPlanStatus" allow-clear placeholder="请输入入矫方案制定状态（0-待制定；1-已制定）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="入矫方案制定时间">
                  <a-date-picker style="width: 100%" placeholder="请选择入矫方案制定时间" v-model="queryParam.startPlanTimeDate" @change="onChangestartPlanTime"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('correctionPlanSummary:add')" >
          <a-button type="primary" v-if="hasPerm('correctionPlanSummary:add')" icon="plus" @click="$refs.addForm.add()">新增矫正方案2.0汇总</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('correctionPlanSummary:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('correctionPlanSummary:edit') & hasPerm('correctionPlanSummary:delete')"/>
          <a-popconfirm v-if="hasPerm('correctionPlanSummary:delete')" placement="topRight" title="确认删除？" @confirm="() => correctionPlanSummaryDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { correctionPlanSummaryPage, correctionPlanSummaryDelete } from '@/api/modular/main/correctionplansummary/correctionPlanSummaryManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '姓名',
            align: 'center',
            dataIndex: 'xm'
          },
          {
            title: '身份证号',
            align: 'center',
            dataIndex: 'sfzh'
          },
          {
            title: '性别',
            align: 'center',
            dataIndex: 'xb'
          },
          {
            title: '年龄',
            align: 'center',
            dataIndex: 'age'
          },
          {
            title: '出生日期',
            align: 'center',
            dataIndex: 'csrq'
          },
          {
            title: '矫正单位id',
            align: 'center',
            dataIndex: 'jzjg'
          },
          {
            title: '矫正单位',
            align: 'center',
            dataIndex: 'jzjgName'
          },
          {
            title: '具体罪名',
            align: 'center',
            dataIndex: 'jtzm'
          },
          {
            title: '具体罪名名称',
            align: 'center',
            dataIndex: 'jtzmName'
          },
          {
            title: '入矫日期',
            align: 'center',
            dataIndex: 'rujiaoriqi'
          },
          {
            title: '主要犯罪事实',
            align: 'center',
            dataIndex: 'zyfzss'
          },
          {
            title: '现实表现',
            align: 'center',
            dataIndex: 'realisticPerformance'
          },
          {
            title: '社会反应及心理测试情况',
            align: 'center',
            dataIndex: 'reactionAndPsychological'
          },
          {
            title: '矫正意见',
            align: 'center',
            dataIndex: 'correctionOpinion'
          },
          {
            title: '上次方案生成时间',
            align: 'center',
            dataIndex: 'lastPlanTime'
          },
          {
            title: '入矫方案制定状态（0-待制定；1-已制定）',
            align: 'center',
            dataIndex: 'startPlanStatus'
          },
          {
            title: '入矫方案制定时间',
            align: 'center',
            dataIndex: 'startPlanTime'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return correctionPlanSummaryPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('correctionPlanSummary:edit') || this.hasPerm('correctionPlanSummary:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const queryParamcsrq = this.queryParam.csrqDate
        if (queryParamcsrq != null) {
            this.queryParam.csrq = moment(queryParamcsrq).format('YYYY-MM-DD')
            if (queryParamcsrq.length < 1) {
                delete this.queryParam.csrq
            }
        }
        const queryParamrujiaoriqi = this.queryParam.rujiaoriqiDate
        if (queryParamrujiaoriqi != null) {
            this.queryParam.rujiaoriqi = moment(queryParamrujiaoriqi).format('YYYY-MM-DD')
            if (queryParamrujiaoriqi.length < 1) {
                delete this.queryParam.rujiaoriqi
            }
        }
        const queryParamlastPlanTime = this.queryParam.lastPlanTimeDate
        if (queryParamlastPlanTime != null) {
            this.queryParam.lastPlanTime = moment(queryParamlastPlanTime).format('YYYY-MM-DD')
            if (queryParamlastPlanTime.length < 1) {
                delete this.queryParam.lastPlanTime
            }
        }
        const queryParamstartPlanTime = this.queryParam.startPlanTimeDate
        if (queryParamstartPlanTime != null) {
            this.queryParam.startPlanTime = moment(queryParamstartPlanTime).format('YYYY-MM-DD')
            if (queryParamstartPlanTime.length < 1) {
                delete this.queryParam.startPlanTime
            }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        return obj
      },
      correctionPlanSummaryDelete (record) {
        correctionPlanSummaryDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      onChangecsrq(date, dateString) {
        this.csrqDateString = dateString
      },
      onChangerujiaoriqi(date, dateString) {
        this.rujiaoriqiDateString = dateString
      },
      onChangelastPlanTime(date, dateString) {
        this.lastPlanTimeDateString = dateString
      },
      onChangestartPlanTime(date, dateString) {
        this.startPlanTimeDateString = dateString
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
