<template>
  <a-modal
    title="编辑矫正方案2.0汇总"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['xm', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="身份证号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入身份证号" v-decorator="['sfzh', {rules: [{required: true, message: '请输入身份证号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="性别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入性别" v-decorator="['xb', {rules: [{required: true, message: '请输入性别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="年龄"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入年龄" v-decorator="['age', {rules: [{required: true, message: '请输入年龄！'}]}]" />
        </a-form-item>
        <a-form-item
          label="出生日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择出生日期" v-decorator="['csrq',{rules: [{ required: true, message: '请选择出生日期！' }]}]" @change="onChangecsrq"/>
        </a-form-item>
        <a-form-item
          label="矫正单位id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正单位id" v-decorator="['jzjg', {rules: [{required: true, message: '请输入矫正单位id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正单位" v-decorator="['jzjgName', {rules: [{required: true, message: '请输入矫正单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="具体罪名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入具体罪名" v-decorator="['jtzm', {rules: [{required: true, message: '请输入具体罪名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="具体罪名名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入具体罪名名称" v-decorator="['jtzmName', {rules: [{required: true, message: '请输入具体罪名名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="入矫日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择入矫日期" v-decorator="['rujiaoriqi',{rules: [{ required: true, message: '请选择入矫日期！' }]}]" @change="onChangerujiaoriqi"/>
        </a-form-item>
        <a-form-item
          label="主要犯罪事实"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入主要犯罪事实" v-decorator="['zyfzss', {rules: [{required: true, message: '请输入主要犯罪事实！'}]}]" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="现实表现"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入现实表现" v-decorator="['realisticPerformance', {rules: [{required: true, message: '请输入现实表现！'}]}]" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="社会反应及心理测试情况"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入社会反应及心理测试情况" v-decorator="['reactionAndPsychological', {rules: [{required: true, message: '请输入社会反应及心理测试情况！'}]}]" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="矫正意见"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-textarea placeholder="请输入矫正意见" v-decorator="['correctionOpinion', {rules: [{required: true, message: '请输入矫正意见！'}]}]" :auto-size="{ minRows: 3, maxRows: 6 }"/>
        </a-form-item>
        <a-form-item
          label="上次方案生成时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择上次方案生成时间" v-decorator="['lastPlanTime',{rules: [{ required: true, message: '请选择上次方案生成时间！' }]}]" @change="onChangelastPlanTime"/>
        </a-form-item>
        <a-form-item
          label="入矫方案制定状态（0-待制定；1-已制定）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入入矫方案制定状态（0-待制定；1-已制定）" v-decorator="['startPlanStatus', {rules: [{required: true, message: '请输入入矫方案制定状态（0-待制定；1-已制定）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="入矫方案制定时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择入矫方案制定时间" v-decorator="['startPlanTime',{rules: [{ required: true, message: '请选择入矫方案制定时间！' }]}]" @change="onChangestartPlanTime"/>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { correctionPlanSummaryEdit } from '@/api/modular/main/correctionplansummary/correctionPlanSummaryManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        csrqDateString: '',
        rujiaoriqiDateString: '',
        lastPlanTimeDateString: '',
        startPlanTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              xm: record.xm,
              sfzh: record.sfzh,
              xb: record.xb,
              age: record.age,
              jzjg: record.jzjg,
              jzjgName: record.jzjgName,
              jtzm: record.jtzm,
              jtzmName: record.jtzmName,
              zyfzss: record.zyfzss,
              realisticPerformance: record.realisticPerformance,
              reactionAndPsychological: record.reactionAndPsychological,
              correctionOpinion: record.correctionOpinion,
              startPlanStatus: record.startPlanStatus
            }
          )
        }, 100)
        // 时间单独处理
        if (record.csrq != null) {
            this.form.getFieldDecorator('csrq', { initialValue: moment(record.csrq, 'YYYY-MM-DD') })
        }
        this.csrqDateString = moment(record.csrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.rujiaoriqi != null) {
            this.form.getFieldDecorator('rujiaoriqi', { initialValue: moment(record.rujiaoriqi, 'YYYY-MM-DD') })
        }
        this.rujiaoriqiDateString = moment(record.rujiaoriqi).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.lastPlanTime != null) {
            this.form.getFieldDecorator('lastPlanTime', { initialValue: moment(record.lastPlanTime, 'YYYY-MM-DD') })
        }
        this.lastPlanTimeDateString = moment(record.lastPlanTime).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.startPlanTime != null) {
            this.form.getFieldDecorator('startPlanTime', { initialValue: moment(record.startPlanTime, 'YYYY-MM-DD') })
        }
        this.startPlanTimeDateString = moment(record.startPlanTime).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.csrq = this.csrqDateString
            values.rujiaoriqi = this.rujiaoriqiDateString
            values.lastPlanTime = this.lastPlanTimeDateString
            values.startPlanTime = this.startPlanTimeDateString
            correctionPlanSummaryEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangecsrq(date, dateString) {
        this.csrqDateString = dateString
      },
      onChangerujiaoriqi(date, dateString) {
        this.rujiaoriqiDateString = dateString
      },
      onChangelastPlanTime(date, dateString) {
        this.lastPlanTimeDateString = dateString
      },
      onChangestartPlanTime(date, dateString) {
        this.startPlanTimeDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
