<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('questionCategory:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="父分类id">
                <a-input v-model="queryParam.parentId" allow-clear placeholder="请输入父分类id"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="分类名">
                <a-input v-model="queryParam.typeName" allow-clear placeholder="请输入分类名"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="部门id">
                  <a-input v-model="queryParam.depId" allow-clear placeholder="请输入部门id"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="排序">
                  <a-input v-model="queryParam.indexId" allow-clear placeholder="请输入排序"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="分类等级">
                  <a-input v-model="queryParam.rank" allow-clear placeholder="请输入分类等级"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否删除">
                  <a-input v-model="queryParam.isDel" allow-clear placeholder="请输入是否删除"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="">
                  <a-input v-model="queryParam.createBy" allow-clear placeholder="请输入"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="">
                  <a-input v-model="queryParam.updateBy" allow-clear placeholder="请输入"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('questionCategory:add')" >
          <a-button type="primary" v-if="hasPerm('questionCategory:add')" icon="plus" @click="$refs.addForm.add()">新增题库管理</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('questionCategory:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('questionCategory:edit') & hasPerm('questionCategory:delete')"/>
          <a-popconfirm v-if="hasPerm('questionCategory:delete')" placement="topRight" title="确认删除？" @confirm="() => questionCategoryDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { questionCategoryPage, questionCategoryDelete } from '@/api/modular/main/questioncategory/questionCategoryManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '父分类id',
            align: 'center',
            dataIndex: 'parentId'
          },
          {
            title: '分类名',
            align: 'center',
            dataIndex: 'typeName'
          },
          {
            title: '部门id',
            align: 'center',
            dataIndex: 'depId'
          },
          {
            title: '排序',
            align: 'center',
            dataIndex: 'indexId'
          },
          {
            title: '分类等级',
            align: 'center',
            dataIndex: 'rank'
          },
          {
            title: '是否删除',
            align: 'center',
            dataIndex: 'isDel'
          },
          {
            title: '',
            align: 'center',
            dataIndex: 'createBy'
          },
          {
            title: '',
            align: 'center',
            dataIndex: 'updateBy'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return questionCategoryPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('questionCategory:edit') || this.hasPerm('questionCategory:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      questionCategoryDelete (record) {
        questionCategoryDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
