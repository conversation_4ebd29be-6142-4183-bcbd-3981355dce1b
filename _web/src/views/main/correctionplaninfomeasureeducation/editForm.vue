<template>
  <a-modal
    title="编辑矫正方案2.0监管措施_教育帮扶每月课件分类学习记录"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="矫正对象id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正对象id" v-decorator="['jzdxId', {rules: [{required: true, message: '请输入矫正对象id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="月份"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入月份" v-decorator="['month', {rules: [{required: true, message: '请输入月份！'}]}]" />
        </a-form-item>
        <a-form-item
          label="课件分类id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入课件分类id" v-decorator="['categoryId', {rules: [{required: true, message: '请输入课件分类id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="课件分类"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入课件分类" v-decorator="['categoryName', {rules: [{required: true, message: '请输入课件分类！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { correctionPlanInfoMeasureEducationEdit } from '@/api/modular/main/correctionplaninfomeasureeducation/correctionPlanInfoMeasureEducationManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              jzdxId: record.jzdxId,
              month: record.month,
              categoryId: record.categoryId,
              categoryName: record.categoryName
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            correctionPlanInfoMeasureEducationEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
