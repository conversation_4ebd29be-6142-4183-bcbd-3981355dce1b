<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('riskTranscriptManagement:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正对象id">
                <a-input v-model="queryParam.jzdxId" allow-clear placeholder="请输入矫正对象id"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正单位id">
                  <a-input v-model="queryParam.jzjg" allow-clear placeholder="请输入矫正单位id"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正单位">
                  <a-input v-model="queryParam.jzjgName" allow-clear placeholder="请输入矫正单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="笔录名称">
                  <a-input v-model="queryParam.transcriptName" allow-clear placeholder="请输入笔录名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="笔录类型">
                  <a-input v-model="queryParam.transcriptType" allow-clear placeholder="请输入笔录类型"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="询问时间">
                  <a-date-picker style="width: 100%" placeholder="请选择询问时间" v-model="queryParam.transcriptTimeDate" @change="onChangetranscriptTime"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="主事项id">
                  <a-input v-model="queryParam.pid" allow-clear placeholder="请输入主事项id"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="">
                  <a-input v-model="queryParam.createUserName" allow-clear placeholder="请输入"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="">
                  <a-input v-model="queryParam.updateUserName" allow-clear placeholder="请输入"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('riskTranscriptManagement:add')" >
          <a-button type="primary" v-if="hasPerm('riskTranscriptManagement:add')" icon="plus" @click="$refs.addForm.add()">新增危险性评估-笔录管理</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('riskTranscriptManagement:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('riskTranscriptManagement:edit') & hasPerm('riskTranscriptManagement:delete')"/>
          <a-popconfirm v-if="hasPerm('riskTranscriptManagement:delete')" placement="topRight" title="确认删除？" @confirm="() => riskTranscriptManagementDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { riskTranscriptManagementPage, riskTranscriptManagementDelete } from '@/api/modular/main/risktranscriptmanagement/riskTranscriptManagementManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '姓名',
            align: 'center',
            dataIndex: 'xm'
          },
          {
            title: '矫正单位',
            align: 'center',
            dataIndex: 'jzjgName'
          },
          {
            title: '笔录名称',
            align: 'center',
            dataIndex: 'transcriptName'
          },
          {
            title: '笔录类型',
            align: 'center',
            dataIndex: 'transcriptType'
          },
          {
            title: '询问时间',
            align: 'center',
            dataIndex: 'transcriptTime'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return riskTranscriptManagementPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('riskTranscriptManagement:edit') || this.hasPerm('riskTranscriptManagement:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const queryParamtranscriptTime = this.queryParam.transcriptTimeDate
        if (queryParamtranscriptTime != null) {
            this.queryParam.transcriptTime = moment(queryParamtranscriptTime).format('YYYY-MM-DD')
            if (queryParamtranscriptTime.length < 1) {
                delete this.queryParam.transcriptTime
            }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        return obj
      },
      riskTranscriptManagementDelete (record) {
        riskTranscriptManagementDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      onChangetranscriptTime(date, dateString) {
        this.transcriptTimeDateString = dateString
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
