<template>
  <a-modal
    title="新增危险性评估-笔录管理"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="矫正对象id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正对象id" v-decorator="['jzdxId', {rules: [{required: true, message: '请输入矫正对象id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['xm', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正单位id" v-decorator="['jzjg', {rules: [{required: true, message: '请输入矫正单位id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正单位" v-decorator="['jzjgName', {rules: [{required: true, message: '请输入矫正单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="笔录名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入笔录名称" v-decorator="['transcriptName', {rules: [{required: true, message: '请输入笔录名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="笔录类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入笔录类型" v-decorator="['transcriptType', {rules: [{required: true, message: '请输入笔录类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="询问时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择询问时间" v-decorator="['transcriptTime',{rules: [{ required: true, message: '请选择询问时间！' }]}]" @change="onChangetranscriptTime"/>
        </a-form-item>
        <a-form-item
          label="主事项id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入主事项id" v-decorator="['pid', {rules: [{required: true, message: '请输入主事项id！'}]}]" />
        </a-form-item>
        <a-form-item
          label=""
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入" v-decorator="['createUserName', {rules: [{required: true, message: '请输入！'}]}]" />
        </a-form-item>
        <a-form-item
          label=""
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入" v-decorator="['updateUserName', {rules: [{required: true, message: '请输入！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { riskTranscriptManagementAdd } from '@/api/modular/main/risktranscriptmanagement/riskTranscriptManagementManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        transcriptTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.transcriptTime = this.transcriptTimeDateString
            riskTranscriptManagementAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangetranscriptTime(date, dateString) {
        this.transcriptTimeDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
