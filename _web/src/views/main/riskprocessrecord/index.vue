<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('riskProcessRecord:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="父级id">
                <a-input v-model="queryParam.pid" allow-clear placeholder="请输入父级id"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="流程名称">
                <a-input v-model="queryParam.processName" allow-clear placeholder="请输入流程名称"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="流程编码">
                  <a-input v-model="queryParam.processCode" allow-clear placeholder="请输入流程编码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="当前步骤">
                  <a-input v-model="queryParam.currentStep" allow-clear placeholder="请输入当前步骤"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="流程状态（0-进行中 1-已完成 2-已暂停 3-已终止）">
                  <a-input v-model="queryParam.processStatus" allow-clear placeholder="请输入流程状态（0-进行中 1-已完成 2-已暂停 3-已终止）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="流程开始时间">
                  <a-date-picker style="width: 100%" placeholder="请选择流程开始时间" v-model="queryParam.startTimeDate" @change="onChangestartTime"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="流程结束时间">
                  <a-date-picker style="width: 100%" placeholder="请选择流程结束时间" v-model="queryParam.endTimeDate" @change="onChangeendTime"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="笔录采集时间">
                  <a-date-picker style="width: 100%" placeholder="请选择笔录采集时间" v-model="queryParam.recordCollectionTimeDate" @change="onChangerecordCollectionTime"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="笔录采集人">
                  <a-input v-model="queryParam.recordCollector" allow-clear placeholder="请输入笔录采集人"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="表单采集时间">
                  <a-date-picker style="width: 100%" placeholder="请选择表单采集时间" v-model="queryParam.formCollectionTimeDate" @change="onChangeformCollectionTime"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="表单采集人">
                  <a-input v-model="queryParam.formCollector" allow-clear placeholder="请输入表单采集人"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="评估时间">
                  <a-date-picker style="width: 100%" placeholder="请选择评估时间" v-model="queryParam.assessmentTimeDate" @change="onChangeassessmentTime"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="评估人">
                  <a-input v-model="queryParam.assessor" allow-clear placeholder="请输入评估人"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="风险等级（低风险、中风险、高风险）">
                  <a-input v-model="queryParam.riskLevel" allow-clear placeholder="请输入风险等级（低风险、中风险、高风险）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="评估结果">
                  <a-input v-model="queryParam.assessmentResult" allow-clear placeholder="请输入评估结果"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="参与人姓名">
                  <a-input v-model="queryParam.participantName" allow-clear placeholder="请输入参与人姓名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="参与人机构">
                  <a-input v-model="queryParam.participantOrg" allow-clear placeholder="请输入参与人机构"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正机构">
                  <a-input v-model="queryParam.jzjg" allow-clear placeholder="请输入矫正机构"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="备注">
                  <a-input v-model="queryParam.remark" allow-clear placeholder="请输入备注"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="状态（字典 0正常 1停用 2删除）">
                  <a-input v-model="queryParam.status" allow-clear placeholder="请输入状态（字典 0正常 1停用 2删除）"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('riskProcessRecord:add')" >
          <a-button type="primary" v-if="hasPerm('riskProcessRecord:add')" icon="plus" @click="$refs.addForm.add()">新增风险流程记录表</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('riskProcessRecord:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('riskProcessRecord:edit') & hasPerm('riskProcessRecord:delete')"/>
          <a-popconfirm v-if="hasPerm('riskProcessRecord:delete')" placement="topRight" title="确认删除？" @confirm="() => riskProcessRecordDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { riskProcessRecordPage, riskProcessRecordDelete } from '@/api/modular/main/riskprocessrecord/riskProcessRecordManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '父级id',
            align: 'center',
            dataIndex: 'pid'
          },
          {
            title: '流程名称',
            align: 'center',
            dataIndex: 'processName'
          },
          {
            title: '流程编码',
            align: 'center',
            dataIndex: 'processCode'
          },
          {
            title: '当前步骤',
            align: 'center',
            dataIndex: 'currentStep'
          },
          {
            title: '流程状态（0-进行中 1-已完成 2-已暂停 3-已终止）',
            align: 'center',
            dataIndex: 'processStatus'
          },
          {
            title: '流程开始时间',
            align: 'center',
            dataIndex: 'startTime'
          },
          {
            title: '流程结束时间',
            align: 'center',
            dataIndex: 'endTime'
          },
          {
            title: '笔录采集时间',
            align: 'center',
            dataIndex: 'recordCollectionTime'
          },
          {
            title: '笔录采集人',
            align: 'center',
            dataIndex: 'recordCollector'
          },
          {
            title: '笔录内容',
            align: 'center',
            dataIndex: 'recordContent'
          },
          {
            title: '表单采集时间',
            align: 'center',
            dataIndex: 'formCollectionTime'
          },
          {
            title: '表单采集人',
            align: 'center',
            dataIndex: 'formCollector'
          },
          {
            title: '表单内容',
            align: 'center',
            dataIndex: 'formContent'
          },
          {
            title: '评估时间',
            align: 'center',
            dataIndex: 'assessmentTime'
          },
          {
            title: '评估人',
            align: 'center',
            dataIndex: 'assessor'
          },
          {
            title: '人工评分',
            align: 'center',
            dataIndex: 'manualScore'
          },
          {
            title: '系统评分',
            align: 'center',
            dataIndex: 'systemScore'
          },
          {
            title: '总评分',
            align: 'center',
            dataIndex: 'totalScore'
          },
          {
            title: '风险等级（低风险、中风险、高风险）',
            align: 'center',
            dataIndex: 'riskLevel'
          },
          {
            title: '评估结果',
            align: 'center',
            dataIndex: 'assessmentResult'
          },
          {
            title: '参与人姓名',
            align: 'center',
            dataIndex: 'participantName'
          },
          {
            title: '参与人机构',
            align: 'center',
            dataIndex: 'participantOrg'
          },
          {
            title: '矫正机构',
            align: 'center',
            dataIndex: 'jzjg'
          },
          {
            title: '备注',
            align: 'center',
            dataIndex: 'remark'
          },
          {
            title: '状态（字典 0正常 1停用 2删除）',
            align: 'center',
            dataIndex: 'status'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return riskProcessRecordPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('riskProcessRecord:edit') || this.hasPerm('riskProcessRecord:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const queryParamstartTime = this.queryParam.startTimeDate
        if (queryParamstartTime != null) {
            this.queryParam.startTime = moment(queryParamstartTime).format('YYYY-MM-DD')
            if (queryParamstartTime.length < 1) {
                delete this.queryParam.startTime
            }
        }
        const queryParamendTime = this.queryParam.endTimeDate
        if (queryParamendTime != null) {
            this.queryParam.endTime = moment(queryParamendTime).format('YYYY-MM-DD')
            if (queryParamendTime.length < 1) {
                delete this.queryParam.endTime
            }
        }
        const queryParamrecordCollectionTime = this.queryParam.recordCollectionTimeDate
        if (queryParamrecordCollectionTime != null) {
            this.queryParam.recordCollectionTime = moment(queryParamrecordCollectionTime).format('YYYY-MM-DD')
            if (queryParamrecordCollectionTime.length < 1) {
                delete this.queryParam.recordCollectionTime
            }
        }
        const queryParamformCollectionTime = this.queryParam.formCollectionTimeDate
        if (queryParamformCollectionTime != null) {
            this.queryParam.formCollectionTime = moment(queryParamformCollectionTime).format('YYYY-MM-DD')
            if (queryParamformCollectionTime.length < 1) {
                delete this.queryParam.formCollectionTime
            }
        }
        const queryParamassessmentTime = this.queryParam.assessmentTimeDate
        if (queryParamassessmentTime != null) {
            this.queryParam.assessmentTime = moment(queryParamassessmentTime).format('YYYY-MM-DD')
            if (queryParamassessmentTime.length < 1) {
                delete this.queryParam.assessmentTime
            }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        return obj
      },
      riskProcessRecordDelete (record) {
        riskProcessRecordDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      onChangestartTime(date, dateString) {
        this.startTimeDateString = dateString
      },
      onChangeendTime(date, dateString) {
        this.endTimeDateString = dateString
      },
      onChangerecordCollectionTime(date, dateString) {
        this.recordCollectionTimeDateString = dateString
      },
      onChangeformCollectionTime(date, dateString) {
        this.formCollectionTimeDateString = dateString
      },
      onChangeassessmentTime(date, dateString) {
        this.assessmentTimeDateString = dateString
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
