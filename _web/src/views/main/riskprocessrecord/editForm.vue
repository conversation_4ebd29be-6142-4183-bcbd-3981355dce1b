<template>
  <a-modal
    title="编辑风险流程记录表"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="父级id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入父级id" v-decorator="['pid', {rules: [{required: true, message: '请输入父级id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="流程名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入流程名称" v-decorator="['processName', {rules: [{required: true, message: '请输入流程名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="流程编码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入流程编码" v-decorator="['processCode', {rules: [{required: true, message: '请输入流程编码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="当前步骤"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入当前步骤" v-decorator="['currentStep', {rules: [{required: true, message: '请输入当前步骤！'}]}]" />
        </a-form-item>
        <a-form-item
          label="流程状态（0-进行中 1-已完成 2-已暂停 3-已终止）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入流程状态（0-进行中 1-已完成 2-已暂停 3-已终止）" v-decorator="['processStatus', {rules: [{required: true, message: '请输入流程状态（0-进行中 1-已完成 2-已暂停 3-已终止）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="流程开始时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择流程开始时间" v-decorator="['startTime',{rules: [{ required: true, message: '请选择流程开始时间！' }]}]" @change="onChangestartTime"/>
        </a-form-item>
        <a-form-item
          label="流程结束时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择流程结束时间" v-decorator="['endTime',{rules: [{ required: true, message: '请选择流程结束时间！' }]}]" @change="onChangeendTime"/>
        </a-form-item>
        <a-form-item
          label="笔录采集时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择笔录采集时间" v-decorator="['recordCollectionTime',{rules: [{ required: true, message: '请选择笔录采集时间！' }]}]" @change="onChangerecordCollectionTime"/>
        </a-form-item>
        <a-form-item
          label="笔录采集人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入笔录采集人" v-decorator="['recordCollector', {rules: [{required: true, message: '请输入笔录采集人！'}]}]" />
        </a-form-item>
        <a-form-item
          label="笔录内容"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="表单采集时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择表单采集时间" v-decorator="['formCollectionTime',{rules: [{ required: true, message: '请选择表单采集时间！' }]}]" @change="onChangeformCollectionTime"/>
        </a-form-item>
        <a-form-item
          label="表单采集人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入表单采集人" v-decorator="['formCollector', {rules: [{required: true, message: '请输入表单采集人！'}]}]" />
        </a-form-item>
        <a-form-item
          label="表单内容"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="评估时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择评估时间" v-decorator="['assessmentTime',{rules: [{ required: true, message: '请选择评估时间！' }]}]" @change="onChangeassessmentTime"/>
        </a-form-item>
        <a-form-item
          label="评估人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入评估人" v-decorator="['assessor', {rules: [{required: true, message: '请输入评估人！'}]}]" />
        </a-form-item>
        <a-form-item
          label="人工评分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="系统评分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="总评分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="风险等级（低风险、中风险、高风险）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入风险等级（低风险、中风险、高风险）" v-decorator="['riskLevel', {rules: [{required: true, message: '请输入风险等级（低风险、中风险、高风险）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="评估结果"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入评估结果" v-decorator="['assessmentResult', {rules: [{required: true, message: '请输入评估结果！'}]}]" />
        </a-form-item>
        <a-form-item
          label="参与人姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入参与人姓名" v-decorator="['participantName', {rules: [{required: true, message: '请输入参与人姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="参与人机构"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入参与人机构" v-decorator="['participantOrg', {rules: [{required: true, message: '请输入参与人机构！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正机构"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正机构" v-decorator="['jzjg', {rules: [{required: true, message: '请输入矫正机构！'}]}]" />
        </a-form-item>
        <a-form-item
          label="备注"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入备注" v-decorator="['remark', {rules: [{required: true, message: '请输入备注！'}]}]" />
        </a-form-item>
        <a-form-item
          label="状态（字典 0正常 1停用 2删除）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入状态（字典 0正常 1停用 2删除）" v-decorator="['status', {rules: [{required: true, message: '请输入状态（字典 0正常 1停用 2删除）！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { riskProcessRecordEdit } from '@/api/modular/main/riskprocessrecord/riskProcessRecordManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        startTimeDateString: '',
        endTimeDateString: '',
        recordCollectionTimeDateString: '',
        formCollectionTimeDateString: '',
        assessmentTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              pid: record.pid,
              processName: record.processName,
              processCode: record.processCode,
              currentStep: record.currentStep,
              processStatus: record.processStatus,
              recordCollector: record.recordCollector,
              recordContent: record.recordContent,
              formCollector: record.formCollector,
              formContent: record.formContent,
              assessor: record.assessor,
              manualScore: record.manualScore,
              systemScore: record.systemScore,
              totalScore: record.totalScore,
              riskLevel: record.riskLevel,
              assessmentResult: record.assessmentResult,
              participantName: record.participantName,
              participantOrg: record.participantOrg,
              jzjg: record.jzjg,
              remark: record.remark,
              status: record.status
            }
          )
        }, 100)
        // 时间单独处理
        if (record.startTime != null) {
            this.form.getFieldDecorator('startTime', { initialValue: moment(record.startTime, 'YYYY-MM-DD') })
        }
        this.startTimeDateString = moment(record.startTime).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.endTime != null) {
            this.form.getFieldDecorator('endTime', { initialValue: moment(record.endTime, 'YYYY-MM-DD') })
        }
        this.endTimeDateString = moment(record.endTime).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.recordCollectionTime != null) {
            this.form.getFieldDecorator('recordCollectionTime', { initialValue: moment(record.recordCollectionTime, 'YYYY-MM-DD') })
        }
        this.recordCollectionTimeDateString = moment(record.recordCollectionTime).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.formCollectionTime != null) {
            this.form.getFieldDecorator('formCollectionTime', { initialValue: moment(record.formCollectionTime, 'YYYY-MM-DD') })
        }
        this.formCollectionTimeDateString = moment(record.formCollectionTime).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.assessmentTime != null) {
            this.form.getFieldDecorator('assessmentTime', { initialValue: moment(record.assessmentTime, 'YYYY-MM-DD') })
        }
        this.assessmentTimeDateString = moment(record.assessmentTime).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.startTime = this.startTimeDateString
            values.endTime = this.endTimeDateString
            values.recordCollectionTime = this.recordCollectionTimeDateString
            values.formCollectionTime = this.formCollectionTimeDateString
            values.assessmentTime = this.assessmentTimeDateString
            riskProcessRecordEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangestartTime(date, dateString) {
        this.startTimeDateString = dateString
      },
      onChangeendTime(date, dateString) {
        this.endTimeDateString = dateString
      },
      onChangerecordCollectionTime(date, dateString) {
        this.recordCollectionTimeDateString = dateString
      },
      onChangeformCollectionTime(date, dateString) {
        this.formCollectionTimeDateString = dateString
      },
      onChangeassessmentTime(date, dateString) {
        this.assessmentTimeDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
