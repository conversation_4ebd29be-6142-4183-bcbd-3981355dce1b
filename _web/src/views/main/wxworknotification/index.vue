<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('wxWorkNotification:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="风险评估id">
                <a-input v-model="queryParam.riskId" allow-clear placeholder="请输入风险评估id"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="通知内容">
                <a-input v-model="queryParam.content" allow-clear placeholder="请输入通知内容"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="接收人id">
                  <a-input v-model="queryParam.receiverId" allow-clear placeholder="请输入接收人id"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="接收人姓名">
                  <a-input v-model="queryParam.receiver" allow-clear placeholder="请输入接收人姓名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="发送结果">
                  <a-input v-model="queryParam.sendResult" allow-clear placeholder="请输入发送结果"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('wxWorkNotification:add')" >
          <a-button type="primary" v-if="hasPerm('wxWorkNotification:add')" icon="plus" @click="$refs.addForm.add()">新增浙政钉通知表</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('wxWorkNotification:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('wxWorkNotification:edit') & hasPerm('wxWorkNotification:delete')"/>
          <a-popconfirm v-if="hasPerm('wxWorkNotification:delete')" placement="topRight" title="确认删除？" @confirm="() => wxWorkNotificationDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { wxWorkNotificationPage, wxWorkNotificationDelete } from '@/api/modular/main/wxworknotification/wxWorkNotificationManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '风险评估id',
            align: 'center',
            dataIndex: 'riskId'
          },
          {
            title: '通知内容',
            align: 'center',
            dataIndex: 'content'
          },
          {
            title: '接收人id',
            align: 'center',
            dataIndex: 'receiverId'
          },
          {
            title: '接收人姓名',
            align: 'center',
            dataIndex: 'receiver'
          },
          {
            title: '发送结果',
            align: 'center',
            dataIndex: 'sendResult'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return wxWorkNotificationPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('wxWorkNotification:edit') || this.hasPerm('wxWorkNotification:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      wxWorkNotificationDelete (record) {
        wxWorkNotificationDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
