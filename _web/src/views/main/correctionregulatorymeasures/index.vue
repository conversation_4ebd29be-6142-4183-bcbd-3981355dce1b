<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('correctionRegulatoryMeasures:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="监管类型">
                <a-input v-model="queryParam.regulatoryType" allow-clear placeholder="请输入监管类型"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="监管类型中文值">
                <a-input v-model="queryParam.regulatoryName" allow-clear placeholder="请输入监管类型中文值"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="有效期">
                  <a-input v-model="queryParam.termOfValidity" allow-clear placeholder="请输入有效期"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="优先级">
                  <a-input v-model="queryParam.priority" allow-clear placeholder="请输入优先级"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="有效期开始时间">
                  <a-date-picker style="width: 100%" placeholder="请选择有效期开始时间" v-model="queryParam.startTimeDate" @change="onChangestartTime"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="有效期结束时间">
                  <a-date-picker style="width: 100%" placeholder="请选择有效期结束时间" v-model="queryParam.endTimeDate" @change="onChangeendTime"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('correctionRegulatoryMeasures:add')" >
          <a-button type="primary" v-if="hasPerm('correctionRegulatoryMeasures:add')" icon="plus" @click="$refs.addForm.add()">新增监管措施</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('correctionRegulatoryMeasures:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('correctionRegulatoryMeasures:edit') & hasPerm('correctionRegulatoryMeasures:delete')"/>
          <a-popconfirm v-if="hasPerm('correctionRegulatoryMeasures:delete')" placement="topRight" title="确认删除？" @confirm="() => correctionRegulatoryMeasuresDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { correctionRegulatoryMeasuresPage, correctionRegulatoryMeasuresDelete } from '@/api/modular/main/correctionregulatorymeasures/correctionRegulatoryMeasuresManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '监管类型',
            align: 'center',
            dataIndex: 'regulatoryType'
          },
          {
            title: '监管类型中文值',
            align: 'center',
            dataIndex: 'regulatoryName'
          },
          {
            title: '监管措施',
            align: 'center',
            dataIndex: 'regulatoryMeasures'
          },
          {
            title: '有效期',
            align: 'center',
            dataIndex: 'termOfValidity'
          },
          {
            title: '优先级',
            align: 'center',
            dataIndex: 'priority'
          },
          {
            title: '有效期开始时间',
            align: 'center',
            dataIndex: 'startTime'
          },
          {
            title: '有效期结束时间',
            align: 'center',
            dataIndex: 'endTime'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return correctionRegulatoryMeasuresPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('correctionRegulatoryMeasures:edit') || this.hasPerm('correctionRegulatoryMeasures:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const queryParamstartTime = this.queryParam.startTimeDate
        if (queryParamstartTime != null) {
            this.queryParam.startTime = moment(queryParamstartTime).format('YYYY-MM-DD')
            if (queryParamstartTime.length < 1) {
                delete this.queryParam.startTime
            }
        }
        const queryParamendTime = this.queryParam.endTimeDate
        if (queryParamendTime != null) {
            this.queryParam.endTime = moment(queryParamendTime).format('YYYY-MM-DD')
            if (queryParamendTime.length < 1) {
                delete this.queryParam.endTime
            }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        return obj
      },
      correctionRegulatoryMeasuresDelete (record) {
        correctionRegulatoryMeasuresDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      onChangestartTime(date, dateString) {
        this.startTimeDateString = dateString
      },
      onChangeendTime(date, dateString) {
        this.endTimeDateString = dateString
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
