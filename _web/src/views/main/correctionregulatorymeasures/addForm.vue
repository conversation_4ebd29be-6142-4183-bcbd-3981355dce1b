<template>
  <a-modal
    title="新增监管措施"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="监管类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入监管类型" v-decorator="['regulatoryType', {rules: [{required: true, message: '请输入监管类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="监管类型中文值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入监管类型中文值" v-decorator="['regulatoryName', {rules: [{required: true, message: '请输入监管类型中文值！'}]}]" />
        </a-form-item>
        <a-form-item
          label="监管措施"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="有效期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入有效期" v-decorator="['termOfValidity', {rules: [{required: true, message: '请输入有效期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="优先级"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入优先级" v-decorator="['priority', {rules: [{required: true, message: '请输入优先级！'}]}]" />
        </a-form-item>
        <a-form-item
          label="有效期开始时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择有效期开始时间" v-decorator="['startTime',{rules: [{ required: true, message: '请选择有效期开始时间！' }]}]" @change="onChangestartTime"/>
        </a-form-item>
        <a-form-item
          label="有效期结束时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择有效期结束时间" v-decorator="['endTime',{rules: [{ required: true, message: '请选择有效期结束时间！' }]}]" @change="onChangeendTime"/>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { correctionRegulatoryMeasuresAdd } from '@/api/modular/main/correctionregulatorymeasures/correctionRegulatoryMeasuresManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        startTimeDateString: '',
        endTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.startTime = this.startTimeDateString
            values.endTime = this.endTimeDateString
            correctionRegulatoryMeasuresAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangestartTime(date, dateString) {
        this.startTimeDateString = dateString
      },
      onChangeendTime(date, dateString) {
        this.endTimeDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
