<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('correctionLlmPlan:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正对象id">
                <a-input v-model="queryParam.jzdxId" allow-clear placeholder="请输入矫正对象id"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="基本信息">
                  <a-input v-model="queryParam.basicInfo" allow-clear placeholder="请输入基本信息"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="主要犯罪事实">
                  <a-input v-model="queryParam.zyfzss" allow-clear placeholder="请输入主要犯罪事实"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('correctionLlmPlan:add')" >
          <a-button type="primary" v-if="hasPerm('correctionLlmPlan:add')" icon="plus" @click="$refs.addForm.add()">新增大模型方案生成</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('correctionLlmPlan:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('correctionLlmPlan:edit') & hasPerm('correctionLlmPlan:delete')"/>
          <a-popconfirm v-if="hasPerm('correctionLlmPlan:delete')" placement="topRight" title="确认删除？" @confirm="() => correctionLlmPlanDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { correctionLlmPlanPage, correctionLlmPlanDelete } from '@/api/modular/main/correctionllmplan/correctionLlmPlanManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '矫正对象id',
            align: 'center',
            dataIndex: 'jzdxId'
          },
          {
            title: '姓名',
            align: 'center',
            dataIndex: 'xm'
          },
          {
            title: '基本信息',
            align: 'center',
            dataIndex: 'basicInfo'
          },
          {
            title: '主要犯罪事实',
            align: 'center',
            dataIndex: 'zyfzss'
          },
          {
            title: '定位信息',
            align: 'center',
            dataIndex: 'locationInfo'
          },
          {
            title: '定位分析',
            align: 'center',
            dataIndex: 'locationAnalyse'
          },
          {
            title: '日记信息',
            align: 'center',
            dataIndex: 'diaryInfo'
          },
          {
            title: '日记分析',
            align: 'center',
            dataIndex: 'diaryAnalyse'
          },
          {
            title: '电话信息',
            align: 'center',
            dataIndex: 'phoneInfo'
          },
          {
            title: '电话分析',
            align: 'center',
            dataIndex: 'phoneAnalyse'
          },
          {
            title: '最终输入信息',
            align: 'center',
            dataIndex: 'finalDescription'
          },
          {
            title: '方案信息',
            align: 'center',
            dataIndex: 'planInfo'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return correctionLlmPlanPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('correctionLlmPlan:edit') || this.hasPerm('correctionLlmPlan:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      correctionLlmPlanDelete (record) {
        correctionLlmPlanDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
