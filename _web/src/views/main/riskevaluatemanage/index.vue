<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('riskEvaluateManage:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位id">
                <a-input v-model="queryParam.jzjg" allow-clear placeholder="请输入矫正单位id"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正单位">
                  <a-input v-model="queryParam.jzjgName" allow-clear placeholder="请输入矫正单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="截止时间">
                  <a-date-picker style="width: 100%" placeholder="请选择截止时间" v-model="queryParam.deadlineDate" @change="onChangedeadline"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="进度百分比">
                  <a-input v-model="queryParam.prograss" allow-clear placeholder="请输入进度百分比"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="状态">
                  <a-input v-model="queryParam.status" allow-clear placeholder="请输入状态"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="结论">
                  <a-input v-model="queryParam.conclusion" allow-clear placeholder="请输入结论"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="创建人姓名">
                  <a-input v-model="queryParam.createUserName" allow-clear placeholder="请输入创建人姓名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="更新人姓名">
                  <a-input v-model="queryParam.updateUserName" allow-clear placeholder="请输入更新人姓名"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('riskEvaluateManage:add')" >
          <a-button type="primary" v-if="hasPerm('riskEvaluateManage:add')" icon="plus" @click="$refs.addForm.add()">新增危险性评估管理</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('riskEvaluateManage:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('riskEvaluateManage:edit') & hasPerm('riskEvaluateManage:delete')"/>
          <a-popconfirm v-if="hasPerm('riskEvaluateManage:delete')" placement="topRight" title="确认删除？" @confirm="() => riskEvaluateManageDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { riskEvaluateManagePage, riskEvaluateManageDelete } from '@/api/modular/main/riskevaluatemanage/riskEvaluateManageManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '姓名',
            align: 'center',
            dataIndex: 'xm'
          },
          {
            title: '矫正单位',
            align: 'center',
            dataIndex: 'jzjgName'
          },
          {
            title: '截止时间',
            align: 'center',
            dataIndex: 'deadline'
          },
          {
            title: '进度百分比',
            align: 'center',
            dataIndex: 'prograss'
          },
          {
            title: '状态',
            align: 'center',
            dataIndex: 'status'
          },
          {
            title: '结论',
            align: 'center',
            dataIndex: 'conclusion'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return riskEvaluateManagePage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('riskEvaluateManage:edit') || this.hasPerm('riskEvaluateManage:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const queryParamdeadline = this.queryParam.deadlineDate
        if (queryParamdeadline != null) {
            this.queryParam.deadline = moment(queryParamdeadline).format('YYYY-MM-DD')
            if (queryParamdeadline.length < 1) {
                delete this.queryParam.deadline
            }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        return obj
      },
      riskEvaluateManageDelete (record) {
        riskEvaluateManageDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      onChangedeadline(date, dateString) {
        this.deadlineDateString = dateString
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
