<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('riskConfigurationManagement:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="配置名称">
                <a-input v-model="queryParam.configName" allow-clear placeholder="请输入配置名称"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="配置类型（个别谈话等）">
                <a-input v-model="queryParam.configType" allow-clear placeholder="请输入配置类型（个别谈话等）"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正阶段（在矫-含入矫、解矫-解除前30日）">
                  <a-input v-model="queryParam.correctionStage" allow-clear placeholder="请输入矫正阶段（在矫-含入矫、解矫-解除前30日）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="风险等级（低风险、中风险、高风险）">
                  <a-input v-model="queryParam.riskLevel" allow-clear placeholder="请输入风险等级（低风险、中风险、高风险）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="执行周期（按周期执行/每日、按月执行等）">
                  <a-input v-model="queryParam.executionPeriod" allow-clear placeholder="请输入执行周期（按周期执行/每日、按月执行等）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="执行状态（待执行、执行中）">
                  <a-input v-model="queryParam.executionStatus" allow-clear placeholder="请输入执行状态（待执行、执行中）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="使用单位">
                  <a-input v-model="queryParam.usedUnit" allow-clear placeholder="请输入使用单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="任务周期（仅一次执行、周期执行）">
                  <a-input v-model="queryParam.taskPeriod" allow-clear placeholder="请输入任务周期（仅一次执行、周期执行）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="执行频段">
                  <a-input v-model="queryParam.executionFrequency" allow-clear placeholder="请输入执行频段"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="频率间隔（每X月）">
                  <a-input v-model="queryParam.frequencyInterval" allow-clear placeholder="请输入频率间隔（每X月）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="执行日期（每月的第几天，JSON格式存储）">
                  <a-input v-model="queryParam.executionDays" allow-clear placeholder="请输入执行日期（每月的第几天，JSON格式存储）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="提醒方式（浙政钉）">
                  <a-input v-model="queryParam.reminderMethod" allow-clear placeholder="请输入提醒方式（浙政钉）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="临期天数">
                  <a-input v-model="queryParam.reminderDays" allow-clear placeholder="请输入临期天数"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="提醒单位（天、提醒）">
                  <a-input v-model="queryParam.reminderUnit" allow-clear placeholder="请输入提醒单位（天、提醒）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="发布人">
                  <a-input v-model="queryParam.publisher" allow-clear placeholder="请输入发布人"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="自动获取当前账号（Y-是，N-否）">
                  <a-input v-model="queryParam.autoPublisher" allow-clear placeholder="请输入自动获取当前账号（Y-是，N-否）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="自动获取当前时间（Y-是，N-否）">
                  <a-input v-model="queryParam.createTimeAuto" allow-clear placeholder="请输入自动获取当前时间（Y-是，N-否）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="状态（0正常 1停用 2删除）">
                  <a-input v-model="queryParam.status" allow-clear placeholder="请输入状态（0正常 1停用 2删除）"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('riskConfigurationManagement:add')" >
          <a-button type="primary" v-if="hasPerm('riskConfigurationManagement:add')" icon="plus" @click="$refs.addForm.add()">新增风险配置管理表</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('riskConfigurationManagement:edit')" @click="toggleStatus(record)">
            {{ record.executionStatus === '执行中' ? '暂停' : '启用' }}
          </a>
          <a-divider type="vertical" v-if="hasPerm('riskConfigurationManagement:edit')"/>
          <a v-if="hasPerm('riskConfigurationManagement:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('riskConfigurationManagement:edit') & hasPerm('riskConfigurationManagement:delete')"/>
          <a-popconfirm v-if="hasPerm('riskConfigurationManagement:delete')" placement="topRight" title="确认删除？" @confirm="() => riskConfigurationManagementDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { riskConfigurationManagementPage, riskConfigurationManagementDelete, riskConfigurationManagementToggleStatus } from '@/api/gen/main/riskconfigurationmanagement/riskConfigurationManagementManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '配置名称',
            align: 'center',
            dataIndex: 'configName'
          },
          {
            title: '配置类型（个别谈话等）',
            align: 'center',
            dataIndex: 'configType'
          },
          {
            title: '矫正阶段（在矫-含入矫、解矫-解除前30日）',
            align: 'center',
            dataIndex: 'correctionStage'
          },
          {
            title: '风险等级（低风险、中风险、高风险）',
            align: 'center',
            dataIndex: 'riskLevel'
          },
          {
            title: '执行周期（按周期执行/每日、按月执行等）',
            align: 'center',
            dataIndex: 'executionPeriod'
          },
          {
            title: '执行状态（待执行、执行中）',
            align: 'center',
            dataIndex: 'executionStatus'
          },
          {
            title: '使用单位',
            align: 'center',
            dataIndex: 'usedUnit'
          },
          {
            title: '任务周期（仅一次执行、周期执行）',
            align: 'center',
            dataIndex: 'taskPeriod'
          },
          {
            title: '执行频段',
            align: 'center',
            dataIndex: 'executionFrequency'
          },
          {
            title: '频率间隔（每X月）',
            align: 'center',
            dataIndex: 'frequencyInterval'
          },
          {
            title: '执行日期（每月的第几天，JSON格式存储）',
            align: 'center',
            dataIndex: 'executionDays'
          },
          {
            title: '提醒方式（浙政钉）',
            align: 'center',
            dataIndex: 'reminderMethod'
          },
          {
            title: '临期天数',
            align: 'center',
            dataIndex: 'reminderDays'
          },
          {
            title: '提醒单位（天、提醒）',
            align: 'center',
            dataIndex: 'reminderUnit'
          },
          {
            title: '发布人',
            align: 'center',
            dataIndex: 'publisher'
          },
          {
            title: '自动获取当前账号（Y-是，N-否）',
            align: 'center',
            dataIndex: 'autoPublisher'
          },
          {
            title: '自动获取当前时间（Y-是，N-否）',
            align: 'center',
            dataIndex: 'createTimeAuto'
          },
          {
            title: '状态（0正常 1停用 2删除）',
            align: 'center',
            dataIndex: 'status'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return riskConfigurationManagementPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('riskConfigurationManagement:edit') || this.hasPerm('riskConfigurationManagement:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      riskConfigurationManagementDelete (record) {
        riskConfigurationManagementDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleStatus (record) {
        riskConfigurationManagementToggleStatus(record).then((res) => {
          if (res.success) {
            const action = record.executionStatus === '执行中' ? '暂停' : '启用'
            this.$message.success(action + '成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('操作失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
