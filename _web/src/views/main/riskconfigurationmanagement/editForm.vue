<template>
  <a-modal
    title="编辑风险配置管理表"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="配置名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入配置名称" v-decorator="['configName', {rules: [{required: true, message: '请输入配置名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="配置类型（个别谈话等）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入配置类型（个别谈话等）" v-decorator="['configType', {rules: [{required: true, message: '请输入配置类型（个别谈话等）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正阶段（在矫-含入矫、解矫-解除前30日）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正阶段（在矫-含入矫、解矫-解除前30日）" v-decorator="['correctionStage', {rules: [{required: true, message: '请输入矫正阶段（在矫-含入矫、解矫-解除前30日）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="风险等级（低风险、中风险、高风险）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入风险等级（低风险、中风险、高风险）" v-decorator="['riskLevel', {rules: [{required: true, message: '请输入风险等级（低风险、中风险、高风险）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="执行周期（按周期执行/每日、按月执行等）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入执行周期（按周期执行/每日、按月执行等）" v-decorator="['executionPeriod', {rules: [{required: true, message: '请输入执行周期（按周期执行/每日、按月执行等）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="执行状态（待执行、执行中）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入执行状态（待执行、执行中）" v-decorator="['executionStatus', {rules: [{required: true, message: '请输入执行状态（待执行、执行中）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="使用单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入使用单位" v-decorator="['usedUnit', {rules: [{required: true, message: '请输入使用单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="任务周期（仅一次执行、周期执行）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入任务周期（仅一次执行、周期执行）" v-decorator="['taskPeriod', {rules: [{required: true, message: '请输入任务周期（仅一次执行、周期执行）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="执行频段"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入执行频段" v-decorator="['executionFrequency', {rules: [{required: true, message: '请输入执行频段！'}]}]" />
        </a-form-item>
        <a-form-item
          label="频率间隔（每X月）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入频率间隔（每X月）" v-decorator="['frequencyInterval', {rules: [{required: true, message: '请输入频率间隔（每X月）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="执行日期（每月的第几天，JSON格式存储）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入执行日期（每月的第几天，JSON格式存储）" v-decorator="['executionDays', {rules: [{required: true, message: '请输入执行日期（每月的第几天，JSON格式存储）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="提醒方式（浙政钉）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入提醒方式（浙政钉）" v-decorator="['reminderMethod', {rules: [{required: true, message: '请输入提醒方式（浙政钉）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="临期天数"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入临期天数" v-decorator="['reminderDays', {rules: [{required: true, message: '请输入临期天数！'}]}]" />
        </a-form-item>
        <a-form-item
          label="提醒单位（天、提醒）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入提醒单位（天、提醒）" v-decorator="['reminderUnit', {rules: [{required: true, message: '请输入提醒单位（天、提醒）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="发布人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入发布人" v-decorator="['publisher', {rules: [{required: true, message: '请输入发布人！'}]}]" />
        </a-form-item>
        <a-form-item
          label="自动获取当前账号（Y-是，N-否）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入自动获取当前账号（Y-是，N-否）" v-decorator="['autoPublisher', {rules: [{required: true, message: '请输入自动获取当前账号（Y-是，N-否）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="自动获取当前时间（Y-是，N-否）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入自动获取当前时间（Y-是，N-否）" v-decorator="['createTimeAuto', {rules: [{required: true, message: '请输入自动获取当前时间（Y-是，N-否）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="状态（0正常 1停用 2删除）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入状态（0正常 1停用 2删除）" v-decorator="['status', {rules: [{required: true, message: '请输入状态（0正常 1停用 2删除）！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { riskConfigurationManagementEdit } from '@/api/gen/main/riskconfigurationmanagement/riskConfigurationManagementManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              configName: record.configName,
              configType: record.configType,
              correctionStage: record.correctionStage,
              riskLevel: record.riskLevel,
              executionPeriod: record.executionPeriod,
              executionStatus: record.executionStatus,
              usedUnit: record.usedUnit,
              taskPeriod: record.taskPeriod,
              executionFrequency: record.executionFrequency,
              frequencyInterval: record.frequencyInterval,
              executionDays: record.executionDays,
              reminderMethod: record.reminderMethod,
              reminderDays: record.reminderDays,
              reminderUnit: record.reminderUnit,
              publisher: record.publisher,
              autoPublisher: record.autoPublisher,
              createTimeAuto: record.createTimeAuto,
              status: record.status
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            riskConfigurationManagementEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
