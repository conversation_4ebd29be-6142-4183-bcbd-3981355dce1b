import { axios } from '@/utils/request'

/**
 * 查询矫正方案2.0汇总
 *
 * <AUTHOR>
 * @date 2024-07-24 13:46:56
 */
export function correctionPlanSummaryPage (parameter) {
  return axios({
    url: '/correctionPlanSummary/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正方案2.0汇总列表
 *
 * <AUTHOR>
 * @date 2024-07-24 13:46:56
 */
export function correctionPlanSummaryList (parameter) {
  return axios({
    url: '/correctionPlanSummary/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加矫正方案2.0汇总
 *
 * <AUTHOR>
 * @date 2024-07-24 13:46:56
 */
export function correctionPlanSummaryAdd (parameter) {
  return axios({
    url: '/correctionPlanSummary/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑矫正方案2.0汇总
 *
 * <AUTHOR>
 * @date 2024-07-24 13:46:56
 */
export function correctionPlanSummaryEdit (parameter) {
  return axios({
    url: '/correctionPlanSummary/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除矫正方案2.0汇总
 *
 * <AUTHOR>
 * @date 2024-07-24 13:46:56
 */
export function correctionPlanSummaryDelete (parameter) {
  return axios({
    url: '/correctionPlanSummary/delete',
    method: 'post',
    data: parameter
  })
}
