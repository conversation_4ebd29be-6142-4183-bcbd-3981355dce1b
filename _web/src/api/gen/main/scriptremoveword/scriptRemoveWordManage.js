import { axios } from '@/utils/request'

/**
 * 查询电话核查话术分词排除表
 *
 * <AUTHOR>
 * @date 2024-12-02 16:43:13
 */
export function scriptRemoveWordPage (parameter) {
  return axios({
    url: '/scriptRemoveWord/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 电话核查话术分词排除表列表
 *
 * <AUTHOR>
 * @date 2024-12-02 16:43:13
 */
export function scriptRemoveWordList (parameter) {
  return axios({
    url: '/scriptRemoveWord/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加电话核查话术分词排除表
 *
 * <AUTHOR>
 * @date 2024-12-02 16:43:13
 */
export function scriptRemoveWordAdd (parameter) {
  return axios({
    url: '/scriptRemoveWord/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑电话核查话术分词排除表
 *
 * <AUTHOR>
 * @date 2024-12-02 16:43:13
 */
export function scriptRemoveWordEdit (parameter) {
  return axios({
    url: '/scriptRemoveWord/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除电话核查话术分词排除表
 *
 * <AUTHOR>
 * @date 2024-12-02 16:43:13
 */
export function scriptRemoveWordDelete (parameter) {
  return axios({
    url: '/scriptRemoveWord/delete',
    method: 'post',
    data: parameter
  })
}
