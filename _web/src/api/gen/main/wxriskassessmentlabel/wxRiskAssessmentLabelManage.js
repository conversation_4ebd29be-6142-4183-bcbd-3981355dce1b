import { axios } from '@/utils/request'

/**
 * 查询每月风险画像标签
 *
 * <AUTHOR>
 * @date 2024-03-01 11:23:50
 */
export function wxRiskAssessmentLabelPage (parameter) {
  return axios({
    url: '/wxRiskAssessmentLabel/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 每月风险画像标签列表
 *
 * <AUTHOR>
 * @date 2024-03-01 11:23:50
 */
export function wxRiskAssessmentLabelList (parameter) {
  return axios({
    url: '/wxRiskAssessmentLabel/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加每月风险画像标签
 *
 * <AUTHOR>
 * @date 2024-03-01 11:23:50
 */
export function wxRiskAssessmentLabelAdd (parameter) {
  return axios({
    url: '/wxRiskAssessmentLabel/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑每月风险画像标签
 *
 * <AUTHOR>
 * @date 2024-03-01 11:23:50
 */
export function wxRiskAssessmentLabelEdit (parameter) {
  return axios({
    url: '/wxRiskAssessmentLabel/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除每月风险画像标签
 *
 * <AUTHOR>
 * @date 2024-03-01 11:23:50
 */
export function wxRiskAssessmentLabelDelete (parameter) {
  return axios({
    url: '/wxRiskAssessmentLabel/delete',
    method: 'post',
    data: parameter
  })
}
