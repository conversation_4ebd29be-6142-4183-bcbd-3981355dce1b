import { axios } from '@/utils/request'

/**
 * 查询措施管理
 *
 * <AUTHOR>
 * @date 2024-01-29 10:07:07
 */
export function wxMeasureManagementPage (parameter) {
  return axios({
    url: '/wxMeasureManagement/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 措施管理列表
 *
 * <AUTHOR>
 * @date 2024-01-29 10:07:07
 */
export function wxMeasureManagementList (parameter) {
  return axios({
    url: '/wxMeasureManagement/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加措施管理
 *
 * <AUTHOR>
 * @date 2024-01-29 10:07:07
 */
export function wxMeasureManagementAdd (parameter) {
  return axios({
    url: '/wxMeasureManagement/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑措施管理
 *
 * <AUTHOR>
 * @date 2024-01-29 10:07:07
 */
export function wxMeasureManagementEdit (parameter) {
  return axios({
    url: '/wxMeasureManagement/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除措施管理
 *
 * <AUTHOR>
 * @date 2024-01-29 10:07:07
 */
export function wxMeasureManagementDelete (parameter) {
  return axios({
    url: '/wxMeasureManagement/delete',
    method: 'post',
    data: parameter
  })
}
