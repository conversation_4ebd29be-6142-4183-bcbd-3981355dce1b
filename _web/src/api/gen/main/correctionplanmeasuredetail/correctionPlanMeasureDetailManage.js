import { axios } from '@/utils/request'

/**
 * 查询矫正方案2.0监管措施明细
 *
 * <AUTHOR>
 * @date 2024-07-26 15:41:08
 */
export function correctionPlanMeasureDetailPage (parameter) {
  return axios({
    url: '/correctionPlanMeasureDetail/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正方案2.0监管措施明细列表
 *
 * <AUTHOR>
 * @date 2024-07-26 15:41:08
 */
export function correctionPlanMeasureDetailList (parameter) {
  return axios({
    url: '/correctionPlanMeasureDetail/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加矫正方案2.0监管措施明细
 *
 * <AUTHOR>
 * @date 2024-07-26 15:41:08
 */
export function correctionPlanMeasureDetailAdd (parameter) {
  return axios({
    url: '/correctionPlanMeasureDetail/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑矫正方案2.0监管措施明细
 *
 * <AUTHOR>
 * @date 2024-07-26 15:41:08
 */
export function correctionPlanMeasureDetailEdit (parameter) {
  return axios({
    url: '/correctionPlanMeasureDetail/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除矫正方案2.0监管措施明细
 *
 * <AUTHOR>
 * @date 2024-07-26 15:41:08
 */
export function correctionPlanMeasureDetailDelete (parameter) {
  return axios({
    url: '/correctionPlanMeasureDetail/delete',
    method: 'post',
    data: parameter
  })
}
