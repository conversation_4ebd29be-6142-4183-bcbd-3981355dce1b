import { axios } from '@/utils/request'

/**
 * 查询危险性评估管理
 *
 * <AUTHOR>
 * @date 2025-07-14 15:58:13
 */
export function riskEvaluateManagePage (parameter) {
  return axios({
    url: '/riskEvaluateManage/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 危险性评估管理列表
 *
 * <AUTHOR>
 * @date 2025-07-14 15:58:13
 */
export function riskEvaluateManageList (parameter) {
  return axios({
    url: '/riskEvaluateManage/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加危险性评估管理
 *
 * <AUTHOR>
 * @date 2025-07-14 15:58:13
 */
export function riskEvaluateManageAdd (parameter) {
  return axios({
    url: '/riskEvaluateManage/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑危险性评估管理
 *
 * <AUTHOR>
 * @date 2025-07-14 15:58:13
 */
export function riskEvaluateManageEdit (parameter) {
  return axios({
    url: '/riskEvaluateManage/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除危险性评估管理
 *
 * <AUTHOR>
 * @date 2025-07-14 15:58:13
 */
export function riskEvaluateManageDelete (parameter) {
  return axios({
    url: '/riskEvaluateManage/delete',
    method: 'post',
    data: parameter
  })
}
