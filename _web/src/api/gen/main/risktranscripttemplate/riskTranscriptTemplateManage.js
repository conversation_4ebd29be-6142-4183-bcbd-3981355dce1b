import { axios } from '@/utils/request'

/**
 * 查询危险性评估-笔录模板
 *
 * <AUTHOR>
 * @date 2025-07-14 16:00:15
 */
export function riskTranscriptTemplatePage (parameter) {
  return axios({
    url: '/riskTranscriptTemplate/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 危险性评估-笔录模板列表
 *
 * <AUTHOR>
 * @date 2025-07-14 16:00:15
 */
export function riskTranscriptTemplateList (parameter) {
  return axios({
    url: '/riskTranscriptTemplate/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加危险性评估-笔录模板
 *
 * <AUTHOR>
 * @date 2025-07-14 16:00:15
 */
export function riskTranscriptTemplateAdd (parameter) {
  return axios({
    url: '/riskTranscriptTemplate/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑危险性评估-笔录模板
 *
 * <AUTHOR>
 * @date 2025-07-14 16:00:15
 */
export function riskTranscriptTemplateEdit (parameter) {
  return axios({
    url: '/riskTranscriptTemplate/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除危险性评估-笔录模板
 *
 * <AUTHOR>
 * @date 2025-07-14 16:00:15
 */
export function riskTranscriptTemplateDelete (parameter) {
  return axios({
    url: '/riskTranscriptTemplate/delete',
    method: 'post',
    data: parameter
  })
}

/**
 * 预览危险性评估-笔录模板
 *
 * <AUTHOR>
 * @date 2025-07-17 16:00:15
 */
export function riskTranscriptTemplatePreview (templateId) {
  return axios({
    url: '/riskTranscriptTemplate/preview',
    method: 'get',
    params: { templateId },
    responseType: 'blob'
  })
}
