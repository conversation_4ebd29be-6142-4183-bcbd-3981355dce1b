import { axios } from '@/utils/request'

/**
 * 查询评估报告详情
 *
 * <AUTHOR>
 * @date 2023-01-10 15:55:35
 */
export function correctionEstimateReportDetailPage (parameter) {
  return axios({
    url: '/correctionEstimateReportDetail/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 评估报告详情列表
 *
 * <AUTHOR>
 * @date 2023-01-10 15:55:35
 */
export function correctionEstimateReportDetailList (parameter) {
  return axios({
    url: '/correctionEstimateReportDetail/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加评估报告详情
 *
 * <AUTHOR>
 * @date 2023-01-10 15:55:35
 */
export function correctionEstimateReportDetailAdd (parameter) {
  return axios({
    url: '/correctionEstimateReportDetail/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑评估报告详情
 *
 * <AUTHOR>
 * @date 2023-01-10 15:55:35
 */
export function correctionEstimateReportDetailEdit (parameter) {
  return axios({
    url: '/correctionEstimateReportDetail/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除评估报告详情
 *
 * <AUTHOR>
 * @date 2023-01-10 15:55:35
 */
export function correctionEstimateReportDetailDelete (parameter) {
  return axios({
    url: '/correctionEstimateReportDetail/delete',
    method: 'post',
    data: parameter
  })
}
