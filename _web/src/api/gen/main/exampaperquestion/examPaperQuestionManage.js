import { axios } from '@/utils/request'

/**
 * 查询量表题目内容
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:05
 */
export function examPaperQuestionPage (parameter) {
  return axios({
    url: '/examPaperQuestion/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 量表题目内容列表
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:05
 */
export function examPaperQuestionList (parameter) {
  return axios({
    url: '/examPaperQuestion/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加量表题目内容
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:05
 */
export function examPaperQuestionAdd (parameter) {
  return axios({
    url: '/examPaperQuestion/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑量表题目内容
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:05
 */
export function examPaperQuestionEdit (parameter) {
  return axios({
    url: '/examPaperQuestion/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除量表题目内容
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:05
 */
export function examPaperQuestionDelete (parameter) {
  return axios({
    url: '/examPaperQuestion/delete',
    method: 'post',
    data: parameter
  })
}
