import { axios } from '@/utils/request'

/**
 * 查询风险评估表
 *
 * <AUTHOR>
 * @date 2024-01-29 10:09:45
 */
export function wxRiskAssessmentPage (parameter) {
  return axios({
    url: '/wxRiskAssessment/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 风险评估表列表
 *
 * <AUTHOR>
 * @date 2024-01-29 10:09:45
 */
export function wxRiskAssessmentList (parameter) {
  return axios({
    url: '/wxRiskAssessment/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加风险评估表
 *
 * <AUTHOR>
 * @date 2024-01-29 10:09:45
 */
export function wxRiskAssessmentAdd (parameter) {
  return axios({
    url: '/wxRiskAssessment/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑风险评估表
 *
 * <AUTHOR>
 * @date 2024-01-29 10:09:45
 */
export function wxRiskAssessmentEdit (parameter) {
  return axios({
    url: '/wxRiskAssessment/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除风险评估表
 *
 * <AUTHOR>
 * @date 2024-01-29 10:09:45
 */
export function wxRiskAssessmentDelete (parameter) {
  return axios({
    url: '/wxRiskAssessment/delete',
    method: 'post',
    data: parameter
  })
}
