import { axios } from '@/utils/request'

/**
 * 查询矫正方案2.0标签分类与原始标签绑定表
 *
 * <AUTHOR>
 * @date 2024-12-03 13:21:09
 */
export function correctionPlanLabelTypePage (parameter) {
  return axios({
    url: '/correctionPlanLabelType/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正方案2.0标签分类与原始标签绑定表列表
 *
 * <AUTHOR>
 * @date 2024-12-03 13:21:09
 */
export function correctionPlanLabelTypeList (parameter) {
  return axios({
    url: '/correctionPlanLabelType/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加矫正方案2.0标签分类与原始标签绑定表
 *
 * <AUTHOR>
 * @date 2024-12-03 13:21:09
 */
export function correctionPlanLabelTypeAdd (parameter) {
  return axios({
    url: '/correctionPlanLabelType/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑矫正方案2.0标签分类与原始标签绑定表
 *
 * <AUTHOR>
 * @date 2024-12-03 13:21:09
 */
export function correctionPlanLabelTypeEdit (parameter) {
  return axios({
    url: '/correctionPlanLabelType/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除矫正方案2.0标签分类与原始标签绑定表
 *
 * <AUTHOR>
 * @date 2024-12-03 13:21:09
 */
export function correctionPlanLabelTypeDelete (parameter) {
  return axios({
    url: '/correctionPlanLabelType/delete',
    method: 'post',
    data: parameter
  })
}
