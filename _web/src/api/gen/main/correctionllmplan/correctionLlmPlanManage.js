import { axios } from '@/utils/request'

/**
 * 查询大模型方案生成
 *
 * <AUTHOR>
 * @date 2025-03-08 11:35:23
 */
export function correctionLlmPlanPage (parameter) {
  return axios({
    url: '/correctionLlmPlan/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 大模型方案生成列表
 *
 * <AUTHOR>
 * @date 2025-03-08 11:35:23
 */
export function correctionLlmPlanList (parameter) {
  return axios({
    url: '/correctionLlmPlan/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加大模型方案生成
 *
 * <AUTHOR>
 * @date 2025-03-08 11:35:23
 */
export function correctionLlmPlanAdd (parameter) {
  return axios({
    url: '/correctionLlmPlan/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑大模型方案生成
 *
 * <AUTHOR>
 * @date 2025-03-08 11:35:23
 */
export function correctionLlmPlanEdit (parameter) {
  return axios({
    url: '/correctionLlmPlan/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除大模型方案生成
 *
 * <AUTHOR>
 * @date 2025-03-08 11:35:23
 */
export function correctionLlmPlanDelete (parameter) {
  return axios({
    url: '/correctionLlmPlan/delete',
    method: 'post',
    data: parameter
  })
}
