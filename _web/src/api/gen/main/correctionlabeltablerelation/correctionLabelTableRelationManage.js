import { axios } from '@/utils/request'

/**
 * 查询标签关联
 *
 * <AUTHOR>
 * @date 2023-04-25 14:35:42
 */
export function correctionLabelTableRelationPage (parameter) {
  return axios({
    url: '/correctionLabelTableRelation/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 标签关联列表
 *
 * <AUTHOR>
 * @date 2023-04-25 14:35:42
 */
export function correctionLabelTableRelationList (parameter) {
  return axios({
    url: '/correctionLabelTableRelation/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加标签关联
 *
 * <AUTHOR>
 * @date 2023-04-25 14:35:42
 */
export function correctionLabelTableRelationAdd (parameter) {
  return axios({
    url: '/correctionLabelTableRelation/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑标签关联
 *
 * <AUTHOR>
 * @date 2023-04-25 14:35:42
 */
export function correctionLabelTableRelationEdit (parameter) {
  return axios({
    url: '/correctionLabelTableRelation/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除标签关联
 *
 * <AUTHOR>
 * @date 2023-04-25 14:35:42
 */
export function correctionLabelTableRelationDelete (parameter) {
  return axios({
    url: '/correctionLabelTableRelation/delete',
    method: 'post',
    data: parameter
  })
}
