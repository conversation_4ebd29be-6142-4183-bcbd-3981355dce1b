import { axios } from '@/utils/request'

/**
 * 查询具体标签与数据库字段绑定表
 *
 * <AUTHOR>
 * @date 2024-11-19 15:36:39
 */
export function correctionLabelDatabaseItemPage (parameter) {
  return axios({
    url: '/correctionLabelDatabaseItem/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 具体标签与数据库字段绑定表列表
 *
 * <AUTHOR>
 * @date 2024-11-19 15:36:39
 */
export function correctionLabelDatabaseItemList (parameter) {
  return axios({
    url: '/correctionLabelDatabaseItem/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加具体标签与数据库字段绑定表
 *
 * <AUTHOR>
 * @date 2024-11-19 15:36:39
 */
export function correctionLabelDatabaseItemAdd (parameter) {
  return axios({
    url: '/correctionLabelDatabaseItem/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑具体标签与数据库字段绑定表
 *
 * <AUTHOR>
 * @date 2024-11-19 15:36:39
 */
export function correctionLabelDatabaseItemEdit (parameter) {
  return axios({
    url: '/correctionLabelDatabaseItem/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除具体标签与数据库字段绑定表
 *
 * <AUTHOR>
 * @date 2024-11-19 15:36:39
 */
export function correctionLabelDatabaseItemDelete (parameter) {
  return axios({
    url: '/correctionLabelDatabaseItem/delete',
    method: 'post',
    data: parameter
  })
}
