import { axios } from '@/utils/request'

/**
 * 查询矫正方案2.0监管措施
 *
 * <AUTHOR>
 * @date 2024-07-24 13:45:55
 */
export function correctionPlanMeasurePage (parameter) {
  return axios({
    url: '/correctionPlanMeasure/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正方案2.0监管措施列表
 *
 * <AUTHOR>
 * @date 2024-07-24 13:45:55
 */
export function correctionPlanMeasureList (parameter) {
  return axios({
    url: '/correctionPlanMeasure/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加矫正方案2.0监管措施
 *
 * <AUTHOR>
 * @date 2024-07-24 13:45:55
 */
export function correctionPlanMeasureAdd (parameter) {
  return axios({
    url: '/correctionPlanMeasure/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑矫正方案2.0监管措施
 *
 * <AUTHOR>
 * @date 2024-07-24 13:45:55
 */
export function correctionPlanMeasureEdit (parameter) {
  return axios({
    url: '/correctionPlanMeasure/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除矫正方案2.0监管措施
 *
 * <AUTHOR>
 * @date 2024-07-24 13:45:55
 */
export function correctionPlanMeasureDelete (parameter) {
  return axios({
    url: '/correctionPlanMeasure/delete',
    method: 'post',
    data: parameter
  })
}
