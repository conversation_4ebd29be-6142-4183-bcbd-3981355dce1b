import { axios } from '@/utils/request'

/**
 * 查询心理画像明细（新）
 *
 * <AUTHOR>
 * @date 2023-01-14 10:38:47
 */
export function correctionEstimatePortrayalParticularsPage (parameter) {
  return axios({
    url: '/correctionEstimatePortrayalParticulars/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 心理画像明细（新）列表
 *
 * <AUTHOR>
 * @date 2023-01-14 10:38:47
 */
export function correctionEstimatePortrayalParticularsList (parameter) {
  return axios({
    url: '/correctionEstimatePortrayalParticulars/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加心理画像明细（新）
 *
 * <AUTHOR>
 * @date 2023-01-14 10:38:47
 */
export function correctionEstimatePortrayalParticularsAdd (parameter) {
  return axios({
    url: '/correctionEstimatePortrayalParticulars/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑心理画像明细（新）
 *
 * <AUTHOR>
 * @date 2023-01-14 10:38:47
 */
export function correctionEstimatePortrayalParticularsEdit (parameter) {
  return axios({
    url: '/correctionEstimatePortrayalParticulars/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除心理画像明细（新）
 *
 * <AUTHOR>
 * @date 2023-01-14 10:38:47
 */
export function correctionEstimatePortrayalParticularsDelete (parameter) {
  return axios({
    url: '/correctionEstimatePortrayalParticulars/delete',
    method: 'post',
    data: parameter
  })
}
