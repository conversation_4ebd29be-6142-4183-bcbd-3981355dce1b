import { axios } from '@/utils/request'

/**
 * 查询画像明细
 *
 * <AUTHOR>
 * @date 2022-11-30 19:58:01
 */
export function correctionEstimatePortrayalDetailPage (parameter) {
  return axios({
    url: '/correctionEstimatePortrayalDetail/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 画像明细列表
 *
 * <AUTHOR>
 * @date 2022-11-30 19:58:01
 */
export function correctionEstimatePortrayalDetailList (parameter) {
  return axios({
    url: '/correctionEstimatePortrayalDetail/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加画像明细
 *
 * <AUTHOR>
 * @date 2022-11-30 19:58:01
 */
export function correctionEstimatePortrayalDetailAdd (parameter) {
  return axios({
    url: '/correctionEstimatePortrayalDetail/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑画像明细
 *
 * <AUTHOR>
 * @date 2022-11-30 19:58:01
 */
export function correctionEstimatePortrayalDetailEdit (parameter) {
  return axios({
    url: '/correctionEstimatePortrayalDetail/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除画像明细
 *
 * <AUTHOR>
 * @date 2022-11-30 19:58:01
 */
export function correctionEstimatePortrayalDetailDelete (parameter) {
  return axios({
    url: '/correctionEstimatePortrayalDetail/delete',
    method: 'post',
    data: parameter
  })
}
