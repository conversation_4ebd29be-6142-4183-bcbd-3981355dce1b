import { axios } from '@/utils/request'

/**
 * 查询矫正方案2.0详情
 *
 * <AUTHOR>
 * @date 2024-07-24 13:45:06
 */
export function correctionPlanInfoPage (parameter) {
  return axios({
    url: '/correctionPlanInfo/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正方案2.0详情列表
 *
 * <AUTHOR>
 * @date 2024-07-24 13:45:06
 */
export function correctionPlanInfoList (parameter) {
  return axios({
    url: '/correctionPlanInfo/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加矫正方案2.0详情
 *
 * <AUTHOR>
 * @date 2024-07-24 13:45:06
 */
export function correctionPlanInfoAdd (parameter) {
  return axios({
    url: '/correctionPlanInfo/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑矫正方案2.0详情
 *
 * <AUTHOR>
 * @date 2024-07-24 13:45:06
 */
export function correctionPlanInfoEdit (parameter) {
  return axios({
    url: '/correctionPlanInfo/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除矫正方案2.0详情
 *
 * <AUTHOR>
 * @date 2024-07-24 13:45:06
 */
export function correctionPlanInfoDelete (parameter) {
  return axios({
    url: '/correctionPlanInfo/delete',
    method: 'post',
    data: parameter
  })
}
