import { axios } from '@/utils/request'

/**
 * 查询风险预警日志
 *
 * <AUTHOR>
 * @date 2024-01-29 10:17:07
 */
export function wxRiskWarningLogPage (parameter) {
  return axios({
    url: '/wxRiskWarningLog/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 风险预警日志列表
 *
 * <AUTHOR>
 * @date 2024-01-29 10:17:07
 */
export function wxRiskWarningLogList (parameter) {
  return axios({
    url: '/wxRiskWarningLog/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加风险预警日志
 *
 * <AUTHOR>
 * @date 2024-01-29 10:17:07
 */
export function wxRiskWarningLogAdd (parameter) {
  return axios({
    url: '/wxRiskWarningLog/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑风险预警日志
 *
 * <AUTHOR>
 * @date 2024-01-29 10:17:07
 */
export function wxRiskWarningLogEdit (parameter) {
  return axios({
    url: '/wxRiskWarningLog/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除风险预警日志
 *
 * <AUTHOR>
 * @date 2024-01-29 10:17:07
 */
export function wxRiskWarningLogDelete (parameter) {
  return axios({
    url: '/wxRiskWarningLog/delete',
    method: 'post',
    data: parameter
  })
}
