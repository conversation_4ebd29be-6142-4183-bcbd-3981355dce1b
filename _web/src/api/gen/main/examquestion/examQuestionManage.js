import { axios } from '@/utils/request'

/**
 * 查询考试题目
 *
 * <AUTHOR>
 * @date 2023-01-03 15:52:58
 */
export function examQuestionPage (parameter) {
  return axios({
    url: '/examQuestion/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 考试题目列表
 *
 * <AUTHOR>
 * @date 2023-01-03 15:52:58
 */
export function examQuestionList (parameter) {
  return axios({
    url: '/examQuestion/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加考试题目
 *
 * <AUTHOR>
 * @date 2023-01-03 15:52:58
 */
export function examQuestionAdd (parameter) {
  return axios({
    url: '/examQuestion/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑考试题目
 *
 * <AUTHOR>
 * @date 2023-01-03 15:52:58
 */
export function examQuestionEdit (parameter) {
  return axios({
    url: '/examQuestion/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除考试题目
 *
 * <AUTHOR>
 * @date 2023-01-03 15:52:58
 */
export function examQuestionDelete (parameter) {
  return axios({
    url: '/examQuestion/delete',
    method: 'post',
    data: parameter
  })
}
