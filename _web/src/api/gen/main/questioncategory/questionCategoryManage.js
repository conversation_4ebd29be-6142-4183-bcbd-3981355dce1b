import { axios } from '@/utils/request'

/**
 * 查询题库管理
 *
 * <AUTHOR>
 * @date 2023-01-03 16:40:11
 */
export function questionCategoryPage (parameter) {
  return axios({
    url: '/questionCategory/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 题库管理列表
 *
 * <AUTHOR>
 * @date 2023-01-03 16:40:11
 */
export function questionCategoryList (parameter) {
  return axios({
    url: '/questionCategory/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加题库管理
 *
 * <AUTHOR>
 * @date 2023-01-03 16:40:11
 */
export function questionCategoryAdd (parameter) {
  return axios({
    url: '/questionCategory/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑题库管理
 *
 * <AUTHOR>
 * @date 2023-01-03 16:40:11
 */
export function questionCategoryEdit (parameter) {
  return axios({
    url: '/questionCategory/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除题库管理
 *
 * <AUTHOR>
 * @date 2023-01-03 16:40:11
 */
export function questionCategoryDelete (parameter) {
  return axios({
    url: '/questionCategory/delete',
    method: 'post',
    data: parameter
  })
}
