import { axios } from '@/utils/request'

/**
 * 查询监管措施
 *
 * <AUTHOR>
 * @date 2023-05-09 14:21:51
 */
export function correctionRegulatoryMeasuresPage (parameter) {
  return axios({
    url: '/correctionRegulatoryMeasures/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 监管措施列表
 *
 * <AUTHOR>
 * @date 2023-05-09 14:21:51
 */
export function correctionRegulatoryMeasuresList (parameter) {
  return axios({
    url: '/correctionRegulatoryMeasures/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加监管措施
 *
 * <AUTHOR>
 * @date 2023-05-09 14:21:51
 */
export function correctionRegulatoryMeasuresAdd (parameter) {
  return axios({
    url: '/correctionRegulatoryMeasures/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑监管措施
 *
 * <AUTHOR>
 * @date 2023-05-09 14:21:51
 */
export function correctionRegulatoryMeasuresEdit (parameter) {
  return axios({
    url: '/correctionRegulatoryMeasures/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除监管措施
 *
 * <AUTHOR>
 * @date 2023-05-09 14:21:51
 */
export function correctionRegulatoryMeasuresDelete (parameter) {
  return axios({
    url: '/correctionRegulatoryMeasures/delete',
    method: 'post',
    data: parameter
  })
}
