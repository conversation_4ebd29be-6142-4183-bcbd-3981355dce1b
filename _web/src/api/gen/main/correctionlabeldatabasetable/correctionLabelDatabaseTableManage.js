import { axios } from '@/utils/request'

/**
 * 查询标签与数据库表绑定表
 *
 * <AUTHOR>
 * @date 2024-11-19 15:34:00
 */
export function correctionLabelDatabaseTablePage (parameter) {
  return axios({
    url: '/correctionLabelDatabaseTable/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 标签与数据库表绑定表列表
 *
 * <AUTHOR>
 * @date 2024-11-19 15:34:00
 */
export function correctionLabelDatabaseTableList (parameter) {
  return axios({
    url: '/correctionLabelDatabaseTable/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加标签与数据库表绑定表
 *
 * <AUTHOR>
 * @date 2024-11-19 15:34:00
 */
export function correctionLabelDatabaseTableAdd (parameter) {
  return axios({
    url: '/correctionLabelDatabaseTable/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑标签与数据库表绑定表
 *
 * <AUTHOR>
 * @date 2024-11-19 15:34:00
 */
export function correctionLabelDatabaseTableEdit (parameter) {
  return axios({
    url: '/correctionLabelDatabaseTable/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除标签与数据库表绑定表
 *
 * <AUTHOR>
 * @date 2024-11-19 15:34:00
 */
export function correctionLabelDatabaseTableDelete (parameter) {
  return axios({
    url: '/correctionLabelDatabaseTable/delete',
    method: 'post',
    data: parameter
  })
}
