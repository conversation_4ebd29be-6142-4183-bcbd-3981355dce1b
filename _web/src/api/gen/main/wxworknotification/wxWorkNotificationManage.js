import { axios } from '@/utils/request'

/**
 * 查询浙政钉通知表
 *
 * <AUTHOR>
 * @date 2024-02-29 16:57:45
 */
export function wxWorkNotificationPage (parameter) {
  return axios({
    url: '/wxWorkNotification/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 浙政钉通知表列表
 *
 * <AUTHOR>
 * @date 2024-02-29 16:57:45
 */
export function wxWorkNotificationList (parameter) {
  return axios({
    url: '/wxWorkNotification/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加浙政钉通知表
 *
 * <AUTHOR>
 * @date 2024-02-29 16:57:45
 */
export function wxWorkNotificationAdd (parameter) {
  return axios({
    url: '/wxWorkNotification/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑浙政钉通知表
 *
 * <AUTHOR>
 * @date 2024-02-29 16:57:45
 */
export function wxWorkNotificationEdit (parameter) {
  return axios({
    url: '/wxWorkNotification/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除浙政钉通知表
 *
 * <AUTHOR>
 * @date 2024-02-29 16:57:45
 */
export function wxWorkNotificationDelete (parameter) {
  return axios({
    url: '/wxWorkNotification/delete',
    method: 'post',
    data: parameter
  })
}
