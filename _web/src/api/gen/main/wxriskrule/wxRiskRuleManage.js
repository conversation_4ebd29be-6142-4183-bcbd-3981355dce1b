import { axios } from '@/utils/request'

/**
 * 查询风险评估细则表
 *
 * <AUTHOR>
 * @date 2024-02-18 16:45:20
 */
export function wxRiskRulePage (parameter) {
  return axios({
    url: '/wxRiskRule/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 风险评估细则表列表
 *
 * <AUTHOR>
 * @date 2024-02-18 16:45:20
 */
export function wxRiskRuleList (parameter) {
  return axios({
    url: '/wxRiskRule/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加风险评估细则表
 *
 * <AUTHOR>
 * @date 2024-02-18 16:45:20
 */
export function wxRiskRuleAdd (parameter) {
  return axios({
    url: '/wxRiskRule/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑风险评估细则表
 *
 * <AUTHOR>
 * @date 2024-02-18 16:45:20
 */
export function wxRiskRuleEdit (parameter) {
  return axios({
    url: '/wxRiskRule/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除风险评估细则表
 *
 * <AUTHOR>
 * @date 2024-02-18 16:45:20
 */
export function wxRiskRuleDelete (parameter) {
  return axios({
    url: '/wxRiskRule/delete',
    method: 'post',
    data: parameter
  })
}
