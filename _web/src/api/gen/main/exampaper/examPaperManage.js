import { axios } from '@/utils/request'

/**
 * 查询新量表管理
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:03
 */
export function examPaperPage (parameter) {
  return axios({
    url: '/examPaper/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 新量表管理列表
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:03
 */
export function examPaperList (parameter) {
  return axios({
    url: '/examPaper/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加新量表管理
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:03
 */
export function examPaperAdd (parameter) {
  return axios({
    url: '/examPaper/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑新量表管理
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:03
 */
export function examPaperEdit (parameter) {
  return axios({
    url: '/examPaper/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除新量表管理
 *
 * <AUTHOR>
 * @date 2023-01-06 14:58:03
 */
export function examPaperDelete (parameter) {
  return axios({
    url: '/examPaper/delete',
    method: 'post',
    data: parameter
  })
}
