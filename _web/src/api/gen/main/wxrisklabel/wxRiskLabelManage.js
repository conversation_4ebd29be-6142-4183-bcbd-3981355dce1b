import { axios } from '@/utils/request'

/**
 * 查询风险标签关联表
 *
 * <AUTHOR>
 * @date 2024-03-01 11:23:54
 */
export function wxRiskLabelPage (parameter) {
  return axios({
    url: '/wxRiskLabel/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 风险标签关联表列表
 *
 * <AUTHOR>
 * @date 2024-03-01 11:23:54
 */
export function wxRiskLabelList (parameter) {
  return axios({
    url: '/wxRiskLabel/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加风险标签关联表
 *
 * <AUTHOR>
 * @date 2024-03-01 11:23:54
 */
export function wxRiskLabelAdd (parameter) {
  return axios({
    url: '/wxRiskLabel/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑风险标签关联表
 *
 * <AUTHOR>
 * @date 2024-03-01 11:23:54
 */
export function wxRiskLabelEdit (parameter) {
  return axios({
    url: '/wxRiskLabel/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除风险标签关联表
 *
 * <AUTHOR>
 * @date 2024-03-01 11:23:54
 */
export function wxRiskLabelDelete (parameter) {
  return axios({
    url: '/wxRiskLabel/delete',
    method: 'post',
    data: parameter
  })
}
