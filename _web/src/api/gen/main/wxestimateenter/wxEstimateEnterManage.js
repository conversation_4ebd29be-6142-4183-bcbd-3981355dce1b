import { axios } from '@/utils/request'

/**
 * 查询吴兴入矫评估
 *
 * <AUTHOR>
 * @date 2024-02-27 13:53:17
 */
export function wxEstimateEnterPage (parameter) {
  return axios({
    url: '/wxEstimateEnter/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 吴兴入矫评估列表
 *
 * <AUTHOR>
 * @date 2024-02-27 13:53:17
 */
export function wxEstimateEnterList (parameter) {
  return axios({
    url: '/wxEstimateEnter/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加吴兴入矫评估
 *
 * <AUTHOR>
 * @date 2024-02-27 13:53:17
 */
export function wxEstimateEnterAdd (parameter) {
  return axios({
    url: '/wxEstimateEnter/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑吴兴入矫评估
 *
 * <AUTHOR>
 * @date 2024-02-27 13:53:17
 */
export function wxEstimateEnterEdit (parameter) {
  return axios({
    url: '/wxEstimateEnter/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除吴兴入矫评估
 *
 * <AUTHOR>
 * @date 2024-02-27 13:53:17
 */
export function wxEstimateEnterDelete (parameter) {
  return axios({
    url: '/wxEstimateEnter/delete',
    method: 'post',
    data: parameter
  })
}
