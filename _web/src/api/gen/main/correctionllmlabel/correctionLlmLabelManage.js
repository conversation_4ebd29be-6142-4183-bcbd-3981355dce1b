import { axios } from '@/utils/request'

/**
 * 查询矫正标签管理
 *
 * <AUTHOR>
 * @date 2025-05-26 11:35:23
 */
export function correctionLlmLabelPage (parameter) {
  return axios({
    url: '/correctionLlmLabel/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正标签管理列表
 *
 * <AUTHOR>
 * @date 2025-05-26 11:35:23
 */
export function correctionLlmLabelList (parameter) {
  return axios({
    url: '/correctionLlmLabel/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加矫正标签管理
 *
 * <AUTHOR>
 * @date 2025-05-26 11:35:23
 */
export function correctionLlmLabelAdd (parameter) {
  return axios({
    url: '/correctionLlmLabel/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑矫正标签管理
 *
 * <AUTHOR>
 * @date 2025-05-26 11:35:23
 */
export function correctionLlmLabelEdit (parameter) {
  return axios({
    url: '/correctionLlmLabel/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除矫正标签管理
 *
 * <AUTHOR>
 * @date 2025-05-26 11:35:23
 */
export function correctionLlmLabelDelete (parameter) {
  return axios({
    url: '/correctionLlmLabel/delete',
    method: 'post',
    data: parameter
  })
} 