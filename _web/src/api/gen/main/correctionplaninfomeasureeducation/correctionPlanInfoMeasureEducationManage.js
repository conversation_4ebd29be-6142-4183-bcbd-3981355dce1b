import { axios } from '@/utils/request'

/**
 * 查询矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
 *
 * <AUTHOR>
 * @date 2024-11-21 16:55:24
 */
export function correctionPlanInfoMeasureEducationPage (parameter) {
  return axios({
    url: '/correctionPlanInfoMeasureEducation/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正方案2.0监管措施_教育帮扶每月课件分类学习记录列表
 *
 * <AUTHOR>
 * @date 2024-11-21 16:55:24
 */
export function correctionPlanInfoMeasureEducationList (parameter) {
  return axios({
    url: '/correctionPlanInfoMeasureEducation/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
 *
 * <AUTHOR>
 * @date 2024-11-21 16:55:24
 */
export function correctionPlanInfoMeasureEducationAdd (parameter) {
  return axios({
    url: '/correctionPlanInfoMeasureEducation/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
 *
 * <AUTHOR>
 * @date 2024-11-21 16:55:24
 */
export function correctionPlanInfoMeasureEducationEdit (parameter) {
  return axios({
    url: '/correctionPlanInfoMeasureEducation/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除矫正方案2.0监管措施_教育帮扶每月课件分类学习记录
 *
 * <AUTHOR>
 * @date 2024-11-21 16:55:24
 */
export function correctionPlanInfoMeasureEducationDelete (parameter) {
  return axios({
    url: '/correctionPlanInfoMeasureEducation/delete',
    method: 'post',
    data: parameter
  })
}
