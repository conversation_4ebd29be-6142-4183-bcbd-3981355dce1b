import { axios } from '@/utils/request'

/**
 * 查询标签表格与字典
 *
 * <AUTHOR>
 * @date 2023-04-25 14:35:29
 */
export function correctionLabelTablePage (parameter) {
  return axios({
    url: '/correctionLabelTable/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 标签表格与字典列表
 *
 * <AUTHOR>
 * @date 2023-04-25 14:35:29
 */
export function correctionLabelTableList (parameter) {
  return axios({
    url: '/correctionLabelTable/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加标签表格与字典
 *
 * <AUTHOR>
 * @date 2023-04-25 14:35:29
 */
export function correctionLabelTableAdd (parameter) {
  return axios({
    url: '/correctionLabelTable/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑标签表格与字典
 *
 * <AUTHOR>
 * @date 2023-04-25 14:35:29
 */
export function correctionLabelTableEdit (parameter) {
  return axios({
    url: '/correctionLabelTable/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除标签表格与字典
 *
 * <AUTHOR>
 * @date 2023-04-25 14:35:29
 */
export function correctionLabelTableDelete (parameter) {
  return axios({
    url: '/correctionLabelTable/delete',
    method: 'post',
    data: parameter
  })
}
