import { axios } from '@/utils/request'

/**
 * 查询矫正方案2.0措施详情
 *
 * <AUTHOR>
 * @date 2024-07-24 13:45:35
 */
export function correctionPlanInfoMeasurePage (parameter) {
  return axios({
    url: '/correctionPlanInfoMeasure/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正方案2.0措施详情列表
 *
 * <AUTHOR>
 * @date 2024-07-24 13:45:35
 */
export function correctionPlanInfoMeasureList (parameter) {
  return axios({
    url: '/correctionPlanInfoMeasure/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加矫正方案2.0措施详情
 *
 * <AUTHOR>
 * @date 2024-07-24 13:45:35
 */
export function correctionPlanInfoMeasureAdd (parameter) {
  return axios({
    url: '/correctionPlanInfoMeasure/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑矫正方案2.0措施详情
 *
 * <AUTHOR>
 * @date 2024-07-24 13:45:35
 */
export function correctionPlanInfoMeasureEdit (parameter) {
  return axios({
    url: '/correctionPlanInfoMeasure/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除矫正方案2.0措施详情
 *
 * <AUTHOR>
 * @date 2024-07-24 13:45:35
 */
export function correctionPlanInfoMeasureDelete (parameter) {
  return axios({
    url: '/correctionPlanInfoMeasure/delete',
    method: 'post',
    data: parameter
  })
}
