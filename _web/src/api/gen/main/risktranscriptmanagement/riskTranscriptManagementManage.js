import { axios } from '@/utils/request'

/**
 * 查询危险性评估-笔录管理
 *
 * <AUTHOR>
 * @date 2025-07-14 15:58:55
 */
export function riskTranscriptManagementPage (parameter) {
  return axios({
    url: '/riskTranscriptManagement/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 危险性评估-笔录管理列表
 *
 * <AUTHOR>
 * @date 2025-07-14 15:58:55
 */
export function riskTranscriptManagementList (parameter) {
  return axios({
    url: '/riskTranscriptManagement/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加危险性评估-笔录管理
 *
 * <AUTHOR>
 * @date 2025-07-14 15:58:55
 */
export function riskTranscriptManagementAdd (parameter) {
  return axios({
    url: '/riskTranscriptManagement/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑危险性评估-笔录管理
 *
 * <AUTHOR>
 * @date 2025-07-14 15:58:55
 */
export function riskTranscriptManagementEdit (parameter) {
  return axios({
    url: '/riskTranscriptManagement/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除危险性评估-笔录管理
 *
 * <AUTHOR>
 * @date 2025-07-14 15:58:55
 */
export function riskTranscriptManagementDelete (parameter) {
  return axios({
    url: '/riskTranscriptManagement/delete',
    method: 'post',
    data: parameter
  })
}
