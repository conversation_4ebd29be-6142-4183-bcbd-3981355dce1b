import { axios } from '@/utils/request'

/**
 * 查询矫正对象最新标签表
 *
 * <AUTHOR>
 * @date 2024-09-11 13:40:58
 */
export function correctionLabelUserNewPage (parameter) {
  return axios({
    url: '/correctionLabelUserNew/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正对象最新标签表列表
 *
 * <AUTHOR>
 * @date 2024-09-11 13:40:58
 */
export function correctionLabelUserNewList (parameter) {
  return axios({
    url: '/correctionLabelUserNew/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加矫正对象最新标签表
 *
 * <AUTHOR>
 * @date 2024-09-11 13:40:58
 */
export function correctionLabelUserNewAdd (parameter) {
  return axios({
    url: '/correctionLabelUserNew/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑矫正对象最新标签表
 *
 * <AUTHOR>
 * @date 2024-09-11 13:40:58
 */
export function correctionLabelUserNewEdit (parameter) {
  return axios({
    url: '/correctionLabelUserNew/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除矫正对象最新标签表
 *
 * <AUTHOR>
 * @date 2024-09-11 13:40:58
 */
export function correctionLabelUserNewDelete (parameter) {
  return axios({
    url: '/correctionLabelUserNew/delete',
    method: 'post',
    data: parameter
  })
}
