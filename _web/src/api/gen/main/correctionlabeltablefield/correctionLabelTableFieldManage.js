import { axios } from '@/utils/request'

/**
 * 查询标签关联表格和字段
 *
 * <AUTHOR>
 * @date 2023-05-06 16:11:13
 */
export function correctionLabelTableFieldPage (parameter) {
  return axios({
    url: '/correctionLabelTableField/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 标签关联表格和字段列表
 *
 * <AUTHOR>
 * @date 2023-05-06 16:11:13
 */
export function correctionLabelTableFieldList (parameter) {
  return axios({
    url: '/correctionLabelTableField/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加标签关联表格和字段
 *
 * <AUTHOR>
 * @date 2023-05-06 16:11:13
 */
export function correctionLabelTableFieldAdd (parameter) {
  return axios({
    url: '/correctionLabelTableField/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑标签关联表格和字段
 *
 * <AUTHOR>
 * @date 2023-05-06 16:11:13
 */
export function correctionLabelTableFieldEdit (parameter) {
  return axios({
    url: '/correctionLabelTableField/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除标签关联表格和字段
 *
 * <AUTHOR>
 * @date 2023-05-06 16:11:13
 */
export function correctionLabelTableFieldDelete (parameter) {
  return axios({
    url: '/correctionLabelTableField/delete',
    method: 'post',
    data: parameter
  })
}
