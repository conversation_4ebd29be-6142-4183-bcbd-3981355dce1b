import { axios } from '@/utils/request'

/**
 * 查询风险评估变动日志
 *
 * <AUTHOR>
 * @date 2024-03-01 11:23:52
 */
export function wxRiskAssessmentLogPage (parameter) {
  return axios({
    url: '/wxRiskAssessmentLog/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 风险评估变动日志列表
 *
 * <AUTHOR>
 * @date 2024-03-01 11:23:52
 */
export function wxRiskAssessmentLogList (parameter) {
  return axios({
    url: '/wxRiskAssessmentLog/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加风险评估变动日志
 *
 * <AUTHOR>
 * @date 2024-03-01 11:23:52
 */
export function wxRiskAssessmentLogAdd (parameter) {
  return axios({
    url: '/wxRiskAssessmentLog/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑风险评估变动日志
 *
 * <AUTHOR>
 * @date 2024-03-01 11:23:52
 */
export function wxRiskAssessmentLogEdit (parameter) {
  return axios({
    url: '/wxRiskAssessmentLog/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除风险评估变动日志
 *
 * <AUTHOR>
 * @date 2024-03-01 11:23:52
 */
export function wxRiskAssessmentLogDelete (parameter) {
  return axios({
    url: '/wxRiskAssessmentLog/delete',
    method: 'post',
    data: parameter
  })
}
