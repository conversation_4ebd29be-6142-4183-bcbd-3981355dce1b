import { axios } from '@/utils/request'

/**
 * 查询用户答题表
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:27
 */
export function examQuestionUserPage (parameter) {
  return axios({
    url: '/examQuestionUser/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 用户答题表列表
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:27
 */
export function examQuestionUserList (parameter) {
  return axios({
    url: '/examQuestionUser/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加用户答题表
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:27
 */
export function examQuestionUserAdd (parameter) {
  return axios({
    url: '/examQuestionUser/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑用户答题表
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:27
 */
export function examQuestionUserEdit (parameter) {
  return axios({
    url: '/examQuestionUser/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除用户答题表
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:27
 */
export function examQuestionUserDelete (parameter) {
  return axios({
    url: '/examQuestionUser/delete',
    method: 'post',
    data: parameter
  })
}
