import { axios } from '@/utils/request'

/**
 * 查询监管措施机构关联标
 *
 * <AUTHOR>
 * @date 2024-09-09 11:23:35
 */
export function correctionPlanMeasureOrgPage (parameter) {
  return axios({
    url: '/correctionPlanMeasureOrg/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 监管措施机构关联标列表
 *
 * <AUTHOR>
 * @date 2024-09-09 11:23:35
 */
export function correctionPlanMeasureOrgList (parameter) {
  return axios({
    url: '/correctionPlanMeasureOrg/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加监管措施机构关联标
 *
 * <AUTHOR>
 * @date 2024-09-09 11:23:35
 */
export function correctionPlanMeasureOrgAdd (parameter) {
  return axios({
    url: '/correctionPlanMeasureOrg/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑监管措施机构关联标
 *
 * <AUTHOR>
 * @date 2024-09-09 11:23:35
 */
export function correctionPlanMeasureOrgEdit (parameter) {
  return axios({
    url: '/correctionPlanMeasureOrg/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除监管措施机构关联标
 *
 * <AUTHOR>
 * @date 2024-09-09 11:23:35
 */
export function correctionPlanMeasureOrgDelete (parameter) {
  return axios({
    url: '/correctionPlanMeasureOrg/delete',
    method: 'post',
    data: parameter
  })
}
