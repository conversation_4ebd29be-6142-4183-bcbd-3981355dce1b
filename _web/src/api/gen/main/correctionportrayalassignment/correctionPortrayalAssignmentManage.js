import { axios } from '@/utils/request'

/**
 * 查询画像首页待处理任务
 *
 * <AUTHOR>
 * @date 2023-01-09 09:14:33
 */
export function correctionPortrayalAssignmentPage (parameter) {
  return axios({
    url: '/correctionPortrayalAssignment/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 画像首页待处理任务列表
 *
 * <AUTHOR>
 * @date 2023-01-09 09:14:33
 */
export function correctionPortrayalAssignmentList (parameter) {
  return axios({
    url: '/correctionPortrayalAssignment/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加画像首页待处理任务
 *
 * <AUTHOR>
 * @date 2023-01-09 09:14:33
 */
export function correctionPortrayalAssignmentAdd (parameter) {
  return axios({
    url: '/correctionPortrayalAssignment/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑画像首页待处理任务
 *
 * <AUTHOR>
 * @date 2023-01-09 09:14:33
 */
export function correctionPortrayalAssignmentEdit (parameter) {
  return axios({
    url: '/correctionPortrayalAssignment/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除画像首页待处理任务
 *
 * <AUTHOR>
 * @date 2023-01-09 09:14:33
 */
export function correctionPortrayalAssignmentDelete (parameter) {
  return axios({
    url: '/correctionPortrayalAssignment/delete',
    method: 'post',
    data: parameter
  })
}
