import { axios } from '@/utils/request'

/**
 * 查询问题选项
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:11
 */
export function examQuestionItemPage (parameter) {
  return axios({
    url: '/examQuestionItem/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 问题选项列表
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:11
 */
export function examQuestionItemList (parameter) {
  return axios({
    url: '/examQuestionItem/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加问题选项
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:11
 */
export function examQuestionItemAdd (parameter) {
  return axios({
    url: '/examQuestionItem/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑问题选项
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:11
 */
export function examQuestionItemEdit (parameter) {
  return axios({
    url: '/examQuestionItem/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除问题选项
 *
 * <AUTHOR>
 * @date 2023-01-03 15:53:11
 */
export function examQuestionItemDelete (parameter) {
  return axios({
    url: '/examQuestionItem/delete',
    method: 'post',
    data: parameter
  })
}
