import { axios } from '@/utils/request'

/**
 * 查询危险性评估-评估管理指标记录
 *
 * <AUTHOR>
 * @date 2025-07-25 17:13:29
 */
export function riskEvaluateIndexRecordPage (parameter) {
  return axios({
    url: '/riskEvaluateIndexRecord/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 危险性评估-评估管理指标记录列表
 *
 * <AUTHOR>
 * @date 2025-07-25 17:13:29
 */
export function riskEvaluateIndexRecordList (parameter) {
  return axios({
    url: '/riskEvaluateIndexRecord/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加危险性评估-评估管理指标记录
 *
 * <AUTHOR>
 * @date 2025-07-25 17:13:29
 */
export function riskEvaluateIndexRecordAdd (parameter) {
  return axios({
    url: '/riskEvaluateIndexRecord/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑危险性评估-评估管理指标记录
 *
 * <AUTHOR>
 * @date 2025-07-25 17:13:29
 */
export function riskEvaluateIndexRecordEdit (parameter) {
  return axios({
    url: '/riskEvaluateIndexRecord/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除危险性评估-评估管理指标记录
 *
 * <AUTHOR>
 * @date 2025-07-25 17:13:29
 */
export function riskEvaluateIndexRecordDelete (parameter) {
  return axios({
    url: '/riskEvaluateIndexRecord/delete',
    method: 'post',
    data: parameter
  })
}
