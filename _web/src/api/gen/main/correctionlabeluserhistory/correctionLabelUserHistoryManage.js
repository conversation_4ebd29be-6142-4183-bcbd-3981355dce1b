import { axios } from '@/utils/request'

/**
 * 查询矫正对象标签历史表
 *
 * <AUTHOR>
 * @date 2024-09-11 13:41:02
 */
export function correctionLabelUserHistoryPage (parameter) {
  return axios({
    url: '/correctionLabelUserHistory/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正对象标签历史表列表
 *
 * <AUTHOR>
 * @date 2024-09-11 13:41:02
 */
export function correctionLabelUserHistoryList (parameter) {
  return axios({
    url: '/correctionLabelUserHistory/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加矫正对象标签历史表
 *
 * <AUTHOR>
 * @date 2024-09-11 13:41:02
 */
export function correctionLabelUserHistoryAdd (parameter) {
  return axios({
    url: '/correctionLabelUserHistory/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑矫正对象标签历史表
 *
 * <AUTHOR>
 * @date 2024-09-11 13:41:02
 */
export function correctionLabelUserHistoryEdit (parameter) {
  return axios({
    url: '/correctionLabelUserHistory/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除矫正对象标签历史表
 *
 * <AUTHOR>
 * @date 2024-09-11 13:41:02
 */
export function correctionLabelUserHistoryDelete (parameter) {
  return axios({
    url: '/correctionLabelUserHistory/delete',
    method: 'post',
    data: parameter
  })
}
