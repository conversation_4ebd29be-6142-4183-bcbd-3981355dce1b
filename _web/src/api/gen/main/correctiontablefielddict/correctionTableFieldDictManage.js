import { axios } from '@/utils/request'

/**
 * 查询表字段与字典关联表
 *
 * <AUTHOR>
 * @date 2024-11-27 16:36:14
 */
export function correctionTableFieldDictPage (parameter) {
  return axios({
    url: '/correctionTableFieldDict/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 表字段与字典关联表列表
 *
 * <AUTHOR>
 * @date 2024-11-27 16:36:14
 */
export function correctionTableFieldDictList (parameter) {
  return axios({
    url: '/correctionTableFieldDict/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加表字段与字典关联表
 *
 * <AUTHOR>
 * @date 2024-11-27 16:36:14
 */
export function correctionTableFieldDictAdd (parameter) {
  return axios({
    url: '/correctionTableFieldDict/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑表字段与字典关联表
 *
 * <AUTHOR>
 * @date 2024-11-27 16:36:14
 */
export function correctionTableFieldDictEdit (parameter) {
  return axios({
    url: '/correctionTableFieldDict/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除表字段与字典关联表
 *
 * <AUTHOR>
 * @date 2024-11-27 16:36:14
 */
export function correctionTableFieldDictDelete (parameter) {
  return axios({
    url: '/correctionTableFieldDict/delete',
    method: 'post',
    data: parameter
  })
}
