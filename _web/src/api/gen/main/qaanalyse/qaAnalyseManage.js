import { axios } from '@/utils/request'

/**
 * 查询问题答案分析
 *
 * <AUTHOR>
 * @date 2025-01-14 14:03:52
 */
export function qaAnalysePage (parameter) {
  return axios({
    url: '/qaAnalyse/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 问题答案分析列表
 *
 * <AUTHOR>
 * @date 2025-01-14 14:03:52
 */
export function qaAnalyseList (parameter) {
  return axios({
    url: '/qaAnalyse/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加问题答案分析
 *
 * <AUTHOR>
 * @date 2025-01-14 14:03:52
 */
export function qaAnalyseAdd (parameter) {
  return axios({
    url: '/qaAnalyse/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑问题答案分析
 *
 * <AUTHOR>
 * @date 2025-01-14 14:03:52
 */
export function qaAnalyseEdit (parameter) {
  return axios({
    url: '/qaAnalyse/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除问题答案分析
 *
 * <AUTHOR>
 * @date 2025-01-14 14:03:52
 */
export function qaAnalyseDelete (parameter) {
  return axios({
    url: '/qaAnalyse/delete',
    method: 'post',
    data: parameter
  })
}
