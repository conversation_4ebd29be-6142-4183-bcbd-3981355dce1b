import { axios } from '@/utils/request'

/**
 * 查询风险配置管理表
 *
 * <AUTHOR>
 * @date 2025-07-20 22:54:09
 */
export function riskConfigurationManagementPage (parameter) {
  return axios({
    url: '/riskConfigurationManagement/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 风险配置管理表列表
 *
 * <AUTHOR>
 * @date 2025-07-20 22:54:09
 */
export function riskConfigurationManagementList (parameter) {
  return axios({
    url: '/riskConfigurationManagement/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加风险配置管理表
 *
 * <AUTHOR>
 * @date 2025-07-20 22:54:09
 */
export function riskConfigurationManagementAdd (parameter) {
  return axios({
    url: '/riskConfigurationManagement/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑风险配置管理表
 *
 * <AUTHOR>
 * @date 2025-07-20 22:54:09
 */
export function riskConfigurationManagementEdit (parameter) {
  return axios({
    url: '/riskConfigurationManagement/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除风险配置管理表
 *
 * <AUTHOR>
 * @date 2025-07-20 22:54:09
 */
export function riskConfigurationManagementDelete (parameter) {
  return axios({
    url: '/riskConfigurationManagement/delete',
    method: 'post',
    data: parameter
  })
}

/**
 * 启用/暂停风险配置
 *
 * <AUTHOR>
 * @date 2025-07-20 22:54:09
 */
export function riskConfigurationManagementToggleStatus (parameter) {
  return axios({
    url: '/riskConfigurationManagement/toggleStatus',
    method: 'post',
    data: parameter
  })
}

/**
 * 获取笔录名称列表
 *
 * <AUTHOR>
 * @date 2025-07-20 22:54:09
 */
export function getTranscriptNames () {
  return axios({
    url: '/riskConfigurationManagement/transcriptNames',
    method: 'get'
  })
}
