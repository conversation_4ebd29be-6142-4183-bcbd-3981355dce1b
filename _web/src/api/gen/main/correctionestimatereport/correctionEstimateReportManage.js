import { axios } from '@/utils/request'

/**
 * 查询评估报告
 *
 * <AUTHOR>
 * @date 2023-01-09 10:57:38
 */
export function correctionEstimateReportPage (parameter) {
  return axios({
    url: '/correctionEstimateReport/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 评估报告列表
 *
 * <AUTHOR>
 * @date 2023-01-09 10:57:38
 */
export function correctionEstimateReportList (parameter) {
  return axios({
    url: '/correctionEstimateReport/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加评估报告
 *
 * <AUTHOR>
 * @date 2023-01-09 10:57:38
 */
export function correctionEstimateReportAdd (parameter) {
  return axios({
    url: '/correctionEstimateReport/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑评估报告
 *
 * <AUTHOR>
 * @date 2023-01-09 10:57:38
 */
export function correctionEstimateReportEdit (parameter) {
  return axios({
    url: '/correctionEstimateReport/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除评估报告
 *
 * <AUTHOR>
 * @date 2023-01-09 10:57:38
 */
export function correctionEstimateReportDelete (parameter) {
  return axios({
    url: '/correctionEstimateReport/delete',
    method: 'post',
    data: parameter
  })
}
