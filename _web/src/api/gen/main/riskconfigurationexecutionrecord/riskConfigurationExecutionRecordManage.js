import { axios } from '@/utils/request'

/**
 * 查询风险配置任务执行记录表
 *
 * <AUTHOR>
 * @date 2025-07-20 22:54:06
 */
export function riskConfigurationExecutionRecordPage (parameter) {
  return axios({
    url: '/riskConfigurationExecutionRecord/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 风险配置任务执行记录表列表
 *
 * <AUTHOR>
 * @date 2025-07-20 22:54:06
 */
export function riskConfigurationExecutionRecordList (parameter) {
  return axios({
    url: '/riskConfigurationExecutionRecord/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加风险配置任务执行记录表
 *
 * <AUTHOR>
 * @date 2025-07-20 22:54:06
 */
export function riskConfigurationExecutionRecordAdd (parameter) {
  return axios({
    url: '/riskConfigurationExecutionRecord/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑风险配置任务执行记录表
 *
 * <AUTHOR>
 * @date 2025-07-20 22:54:06
 */
export function riskConfigurationExecutionRecordEdit (parameter) {
  return axios({
    url: '/riskConfigurationExecutionRecord/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除风险配置任务执行记录表
 *
 * <AUTHOR>
 * @date 2025-07-20 22:54:06
 */
export function riskConfigurationExecutionRecordDelete (parameter) {
  return axios({
    url: '/riskConfigurationExecutionRecord/delete',
    method: 'post',
    data: parameter
  })
}

/**
 * 重新发送执行记录
 *
 * <AUTHOR>
 * @date 2025-07-20 22:54:06
 */
export function riskConfigurationExecutionRecordResend (parameter) {
  return axios({
    url: '/riskConfigurationExecutionRecord/resend',
    method: 'post',
    data: parameter
  })
}

/**
 * 根据配置ID获取执行记录列表
 *
 * <AUTHOR>
 * @date 2025-07-20 22:54:06
 */
export function riskConfigurationExecutionRecordListByConfigId (parameter) {
  return axios({
    url: '/riskConfigurationExecutionRecord/listByConfigId',
    method: 'get',
    params: parameter
  })
}
