import { axios } from '@/utils/request'

/**
 * 查询风险流程记录表
 *
 * <AUTHOR>
 * @date 2025-07-15 11:05:44
 */
export function riskProcessRecordPage (parameter) {
  return axios({
    url: '/riskProcessRecord/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 风险流程记录表列表
 *
 * <AUTHOR>
 * @date 2025-07-15 11:05:44
 */
export function riskProcessRecordList (parameter) {
  return axios({
    url: '/riskProcessRecord/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加风险流程记录表
 *
 * <AUTHOR>
 * @date 2025-07-15 11:05:44
 */
export function riskProcessRecordAdd (parameter) {
  return axios({
    url: '/riskProcessRecord/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑风险流程记录表
 *
 * <AUTHOR>
 * @date 2025-07-15 11:05:44
 */
export function riskProcessRecordEdit (parameter) {
  return axios({
    url: '/riskProcessRecord/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除风险流程记录表
 *
 * <AUTHOR>
 * @date 2025-07-15 11:05:44
 */
export function riskProcessRecordDelete (parameter) {
  return axios({
    url: '/riskProcessRecord/delete',
    method: 'post',
    data: parameter
  })
}
