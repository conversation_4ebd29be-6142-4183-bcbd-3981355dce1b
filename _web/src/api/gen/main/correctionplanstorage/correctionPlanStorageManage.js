import { axios } from '@/utils/request'

/**
 * 查询矫正方案2.0方案库
 *
 * <AUTHOR>
 * @date 2024-07-24 13:46:14
 */
export function correctionPlanStoragePage (parameter) {
  return axios({
    url: '/correctionPlanStorage/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正方案2.0方案库列表
 *
 * <AUTHOR>
 * @date 2024-07-24 13:46:14
 */
export function correctionPlanStorageList (parameter) {
  return axios({
    url: '/correctionPlanStorage/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加矫正方案2.0方案库
 *
 * <AUTHOR>
 * @date 2024-07-24 13:46:14
 */
export function correctionPlanStorageAdd (parameter) {
  return axios({
    url: '/correctionPlanStorage/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑矫正方案2.0方案库
 *
 * <AUTHOR>
 * @date 2024-07-24 13:46:14
 */
export function correctionPlanStorageEdit (parameter) {
  return axios({
    url: '/correctionPlanStorage/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除矫正方案2.0方案库
 *
 * <AUTHOR>
 * @date 2024-07-24 13:46:14
 */
export function correctionPlanStorageDelete (parameter) {
  return axios({
    url: '/correctionPlanStorage/delete',
    method: 'post',
    data: parameter
  })
}
