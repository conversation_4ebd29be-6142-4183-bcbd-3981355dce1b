-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO "sys_menu" VALUES
("7882084587464134379", "0", "[0],", "风险流程记录表", "riskprocessrecord_index", "1", null, "/riskProcessRecord", "main/riskprocessrecord/index", null, "system_tool", "1", "Y", null, null, "1", "100", null, "0", null, null, null, null);

INSERT INTO `sys_menu` VALUES
("4916953114981280146", "7882084587464134379", "[0],[7882084587464134379],", "风险流程记录表查询", "riskprocessrecord_index_page", "2", null, null, null, "riskProcessRecord:page", "system_tool", "0", "Y", null, null, "1", "100", null, "0", null, null, null, null);

INSERT INTO `sys_menu` VALUES
("7587223413681915578", "7882084587464134379", "[0],[7882084587464134379],", "风险流程记录表新增", "riskprocessrecord_index_add", "2", null, null, null, "riskProcessRecord:add", "system_tool", "0", "Y", null, null, "1", "100", null, "0", null, null, null, null);

INSERT INTO `sys_menu` VALUES
("8917991255356281402", "7882084587464134379", "[0],[7882084587464134379],", "风险流程记录表编辑", "riskprocessrecord_index_edit", "2", null, null, null, "riskProcessRecord:edit", "system_tool", "0", "Y", null, null, "1", "100", null, "0", null, null, null, null);

INSERT INTO `sys_menu` VALUES
("5169772450928803698", "7882084587464134379", "[0],[7882084587464134379],", "风险流程记录表删除", "riskprocessrecord_index_delete", "2", null, null, null, "riskProcessRecord:delete", "system_tool", "0", "Y", null, null, "1", "100", null, "0", null, null, null, null);

INSERT INTO `sys_menu` VALUES
("6737398758344011952", "7882084587464134379", "[0],[7882084587464134379],", "风险流程记录表查看", "riskprocessrecord_index_detail", "2", null, null, null, "riskProcessRecord:detail", "system_tool", "0", "Y", null, null, "1", "100", null, "0", null, null, null, null);

INSERT INTO "sys_menu" VALUES
("8280565899252622981", "7882084587464134379", "[0],[7882084587464134379],", "风险流程记录表列表", "riskprocessrecord_index_list", "2", null, null, null, "riskProcessRecord:list", "system_tool", "0", "Y", null, null, "1", "100", null, "0", null, null, null, null);
