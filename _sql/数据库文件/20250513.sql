ALTER TABLE `correction_llm_plan` ADD COLUMN `show_basic_info` text NULL COMMENT '要展示的基本信息' AFTER `basic_info`;
ALTER TABLE `correction_llm_plan` ADD COLUMN `portrait_analyse` text NULL COMMENT '画像分析' AFTER `show_basic_info`;
ALTER TABLE `correction_llm_plan` ADD COLUMN `tag_info` text NULL COMMENT '标签信息' AFTER `portrait_analyse`;
ALTER TABLE `correction_llm_plan` ADD COLUMN `xb_name` text NULL COMMENT '性别' AFTER `tag_info`;
ALTER TABLE `correction_llm_plan` ADD COLUMN `jzjg` text NULL COMMENT '矫正单位' AFTER `xb_name`;
ALTER TABLE `correction_llm_plan` ADD COLUMN `jzjg_name` text NULL COMMENT '矫正单位名称' AFTER `jzjg`;
ALTER TABLE `correction_llm_plan` ADD COLUMN `jzlb_name` text NULL COMMENT '矫正类别' AFTER `jzjg_name`;
ALTER TABLE `correction_llm_plan` ADD COLUMN `sfcn_name` text NULL COMMENT '是否成年' AFTER `jzlb_name`;
ALTER TABLE `correction_llm_plan` ADD COLUMN `plan_effect` text NULL COMMENT '方案效果' AFTER `sfcn_name`;
ALTER TABLE `correction_llm_plan` ADD COLUMN `status` text NULL COMMENT '状态' AFTER `plan_effect`;
ALTER TABLE `correction_llm_plan` ADD COLUMN `adjust_count` int(11) NULL COMMENT '调整次数' AFTER `status`;
ALTER TABLE `correction_llm_plan` ADD COLUMN `last_plan_id` varchar(64) NULL COMMENT '上期方案id' AFTER `adjust_count`;
ALTER TABLE `correction_llm_plan` ADD COLUMN `last_plan_description` text NULL COMMENT '上期方案待评估信息' AFTER `last_plan_id`;
ALTER TABLE `correction_llm_plan` ADD COLUMN `is_show` int(11) NULL DEFAULT 1 COMMENT '是否列表可见(0-否，1-是),默认可见' AFTER `last_plan_description`;


ALTER TABLE `precision_correction0`.`correction_llm_plan`
    MODIFY COLUMN `tag_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '标签信息' AFTER `sfcn_name`,
    MODIFY COLUMN `portrait_analyse` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '画像分析' AFTER `tag_info`,
    MODIFY COLUMN `plan_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '方案信息' AFTER `plan_effect`;

ALTER TABLE `precision_correction0`.`correction_llm_plan`
    MODIFY COLUMN `reward_penalty_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '奖惩信息' AFTER `entry_plan_info`,
    MODIFY COLUMN `status` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '状态' AFTER `reward_penalty_info`,
    MODIFY COLUMN `is_show` int(11) NULL DEFAULT 1 COMMENT '是否列表可见(0-否，1-是),默认可见' AFTER `status`,
    MODIFY COLUMN `adjust_count` int(11) NULL DEFAULT NULL COMMENT '调整次数' AFTER `show_type`

