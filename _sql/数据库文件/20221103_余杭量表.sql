-- 量表配置基本信息表
CREATE TABLE correction_scale_base (
    id varchar(32) NOT NULL COMMENT '主键id',
    title varchar(50) DEFAULT NULL COMMENT '量表名称',
    status tinyint(1) DEFAULT 0 COMMENT '启用情况（0：启用 1：停用）',
    create_user varchar(64) binary CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
    update_user varchar(64) binary CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
    create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    update_time timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    del_flag tinyint(1) DEFAULT 0 COMMENT '是否删除（0：未删除，1删除）',
    PRIMARY KEY (id)
)
COMMENT = '量表配置基本信息表';

-- 量表配置--试题信息表
CREATE TABLE correction_question (
    id varchar(32) NOT NULL COMMENT '主键id',
    scale_base_id varchar(32) DEFAULT NULL COMMENT '量表id',
    question varchar(100) DEFAULT NULL COMMENT '问题名称',
    question_type tinyint(1) DEFAULT 0 COMMENT '问题类型（0：单选 1：多选）',
    order_index tinyint(1) DEFAULT 0 COMMENT '问题序号',
    relevance_item_id varchar(32) DEFAULT NULL COMMENT '关联的选项id',
    create_user varchar(64) binary CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
    update_user varchar(64) binary CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
    create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    update_time timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    del_flag tinyint(1) DEFAULT 0 COMMENT '是否删除（0：未删除，1删除）',
    PRIMARY KEY (id)
)
COMMENT = '量表配置--试题信息表';
ALTER TABLE correction_question ADD INDEX IDX_scale_base_id (scale_base_id);


-- 量表配置--试题选项表
CREATE TABLE correction_question_item (
    id varchar(32) NOT NULL COMMENT '主键id',
    question_id varchar(32) DEFAULT NULL COMMENT '问题id',
    order_index tinyint(1) DEFAULT 0 COMMENT '选项序号',
    content varchar(100) DEFAULT NULL COMMENT '选项内容',
    have_higher_question tinyint(1) DEFAULT 0 COMMENT '是否有关联问题（0：没有 1：有）',
    del_flag tinyint(1) DEFAULT 0 COMMENT '是否删除（0：未删除，1删除）',
    PRIMARY KEY (id)
)
COMMENT = '量表配置--试题选项表';
ALTER TABLE correction_question_item ADD INDEX IDX_question_id (question_id);


-- 评估管理--基本信息
CREATE TABLE correction_asses_base (
    id varchar(32) NOT NULL COMMENT '主键id',
    title varchar(50) DEFAULT NULL COMMENT '任务名称',
    scale_base_id varchar(32) DEFAULT NULL COMMENT '量表id',
    scale_base_name varchar(50) DEFAULT NULL COMMENT '量表名称',
    phase tinyint(1) DEFAULT 0 COMMENT '测评阶段：0：入矫 1: 在矫',
    cycle tinyint(1) DEFAULT 0 COMMENT '测评周期：0：单词 1：一月一次',
    start_time char(8) DEFAULT NULL COMMENT '测评开始时间，格式: yyyyMMdd',
    end_time char(8) DEFAULT NULL COMMENT '测评结束时间，格式: yyyyMMdd',
    asses_scope tinyint(1) DEFAULT 0 COMMENT '测评范围：0：部门  1：人员',
    need_warn tinyint(1) DEFAULT 0 COMMENT '是否临期提醒：0：否  1：是',
    day_num tinyint(1) DEFAULT 0 COMMENT '临期天数',
    total_num int(6) DEFAULT 0 COMMENT '进度--总次数',
    complete_num int(6) DEFAULT 0 COMMENT '进度--完成次数',
    status tinyint(1) DEFAULT 0 COMMENT '状态：0：未开始 1：进行中 2：已完成 3：停止',
    create_user varchar(64) binary CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
    update_user varchar(64) binary CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
    create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    update_time timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    del_flag tinyint(1) DEFAULT 0 COMMENT '是否删除（0：未删除，1删除）',
    PRIMARY KEY (id)
)
COMMENT = '评估管理--基本信息';


-- 评估管理--人员机构信息(只用于评估未开始前的查看)
CREATE TABLE correction_asses_members (
    id varchar(32) NOT NULL COMMENT '主键id',
    base_id varchar(32) DEFAULT NULL COMMENT '评估管理ID',
    type tinyint(1) DEFAULT 0 COMMENT '类型：0：机构 1: 人员',
    member_id varchar(50) DEFAULT NULL COMMENT '人员或机构id',
    name varchar(50) DEFAULT NULL COMMENT '人员或机构名称',
    PRIMARY KEY (id)
)
COMMENT = '评估管理--人员机构信息';
ALTER TABLE correction_asses_members ADD INDEX IDX_base_id (base_id);

-- 评估管理--评估人员信息
CREATE TABLE correction_asses_person (
    id varchar(32) NOT NULL COMMENT '主键id',
    base_id varchar(32) DEFAULT NULL COMMENT '评估管理ID',
    sqjzry_id varchar(32) DEFAULT NULL COMMENT '矫正对象id',
    status tinyint(1) DEFAULT 0 COMMENT '填写状态：0：未填写 1: 已填写',
    total_num int(6) DEFAULT 0 COMMENT '进度--总次数',
    complete_num int(6) DEFAULT 0 COMMENT '进度--完成次数',
    PRIMARY KEY (id)
)
COMMENT = '评估管理--评估人员信息';
ALTER TABLE correction_asses_person ADD INDEX IDX_base_id (base_id);

-- 评估管理--评估人员评估明细
CREATE TABLE correction_asses_person_dtl (
    id varchar(32) NOT NULL COMMENT '主键id',
    asses_person_id varchar(32) DEFAULT NULL COMMENT '评估人员信息ID',
    sqjzry_id varchar(32) DEFAULT NULL COMMENT '矫正对象id,冗余，方便查找问卷',
    title varchar(50) DEFAULT NULL COMMENT '任务名称',
    scale_base_id varchar(32) DEFAULT NULL COMMENT '量表id，冗余，方便查找题目',
    start_time char(8) DEFAULT NULL COMMENT '测评开始时间，格式: yyyyMMdd',
    end_time char(8) DEFAULT NULL COMMENT '测评结束时间，格式: yyyyMMdd',
    status tinyint(1) DEFAULT 0 COMMENT '填写状态：0：未填写 1: 已填写',
    order_index tinyint(1) DEFAULT 0 COMMENT '序号',
    need_warn tinyint(1) DEFAULT 0 COMMENT '是否临期提醒：0：否  1：是',
    day_num tinyint(1) DEFAULT 0 COMMENT '临期天数',
    tag tinyint(1) DEFAULT 0 COMMENT '移动端是否显示提醒： 0：否  1:是',
    PRIMARY KEY (id)
)
COMMENT = '评估管理--评估人员评估明细';
ALTER TABLE correction_asses_person_dtl ADD INDEX IDX_asses_person_id (asses_person_id);

-- 评估管理--评估人员评估明细答案
CREATE TABLE correction_asses_answer (
    id varchar(32) NOT NULL COMMENT '主键id',
    dtl_id varchar(32) DEFAULT NULL COMMENT '评估人员评估明细id(correction_asses_person_dtl.ID)',
    question_id varchar(32) DEFAULT NULL COMMENT '问题id',
    answer_ids varchar(500) DEFAULT NULL COMMENT '答案(选项)ids',
    PRIMARY KEY (id)
)
COMMENT = '评估管理--评估人员评估明细答案';
ALTER TABLE correction_asses_answer ADD INDEX IDX_dtl_id (dtl_id);
ALTER TABLE correction_asses_answer ADD INDEX IDX_ques_dtl_id (dtl_id, question_id);




