CREATE TABLE `correction_llm_label`
(
    `id`          varchar(64) NOT NULL COMMENT 'id',
    `label_name`  varchar(255) DEFAULT NULL COMMENT '标签名称',
    `label_type`  varchar(1)   DEFAULT NULL COMMENT '标签性质（1-积极；2-中性；3-消极）',
    `create_time` datetime     DEFAULT NULL COMMENT '创建时间',
    `create_user` varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_user` varchar(255) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大模型标签管理';
