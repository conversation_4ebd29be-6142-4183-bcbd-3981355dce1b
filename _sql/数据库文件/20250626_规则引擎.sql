-- 本项目暂时没有PC端，后续需要【规则脚本】、【规则调用】对应的前端模块，可以从象山反走私拷贝

CREATE TABLE js_liteflow_chain (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  application_name varchar(32) DEFAULT NULL,
  chain_name varchar(32) DEFAULT NULL,
  chain_desc varchar(64) DEFAULT NULL,
  el_data text DEFAULT NULL,
  route text DEFAULT NULL,
  namespace varchar(32) DEFAULT NULL,
  create_time datetime DEFAULT NULL,
  enable tinyint(1) DEFAULT 1,
  PRIMARY KEY (id)
)
COMMENT = '规则引擎调用链';


CREATE TABLE js_liteflow_script (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  application_name varchar(32) DEFAULT NULL,
  script_id varchar(32) DEFAULT NULL,
  script_name varchar(64) DEFAULT NULL,
  script_data text DEFAULT NULL,
  script_type varchar(16) DEFAULT NULL,
  language varchar(32) DEFAULT NULL,
  enable tinyint(1) DEFAULT 0,
  PRIMARY KEY (id)
)
COMMENT = '规则引擎脚本';


INSERT INTO sys_menu(id, pid, pids, name, code, type, icon, router, component, permission, application, open_type, visible, link, redirect, weight, sort, remark, status, create_time, create_user, update_time, update_user) VALUES
('1920357242399531009', '1264622039642255961', '[0],[1264622039642255961],', '规则脚本', 'scriptRule', 1, NULL, '/liteflowscript/index', 'main/liteflowscript/index', '', 'system', 1, 'Y', NULL, '', 1, 100, NULL, 0, NULL, NULL, NULL, NULL),
('1920400089098113025', '1264622039642255961', '[0],[1264622039642255961],', '规则调用', 'liteflowChain', 1, NULL, '/liteflowchain/index', 'main/liteflowchain/index', '', 'system', 1, 'Y', NULL, '', 1, 100, NULL, 0, NULL, NULL, NULL, NULL);

INSERT INTO js_liteflow_chain(id, application_name, chain_name, chain_desc, el_data, route, namespace, create_time, enable) VALUES
    (1938416832452182018, 'fzs_province', 'riskLevelChain', '静态风险等级判断', 'THEN(risk_level)', '', '', NULL, 1);

INSERT INTO js_liteflow_script(id, application_name, script_id, script_name, script_data, script_type, language, enable) VALUES
    (1938414586222034946, 'fzs_province', 'risk_level', '静态风险等级', 'import cn.hutool.core.util.ObjectUtil;\r\nimport cn.hutool.extra.spring.SpringUtil;\r\nimport com.concise.modular.riskpoint.entity.RiskPoint;\r\nimport com.concise.modular.riskpoint.service.RiskPointService;\r\nimport com.yomahub.liteflow.core.NodeComponent;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\npublic class RiskLevelChain extends NodeComponent {\r\n\r\n    private final Logger log = LoggerFactory.getLogger(RiskLevelChain.class);\r\n    @Override\r\n    public void process() throws Exception {\r\n        RiskPoint riskPoint = this.getRequestData();\r\n        //静态得分为0\r\n        int staticScore = 0;\r\n        //总分为0\r\n        double totalScore = 0;\r\n        //1、静态风险指标\r\n        //1.1 风险点类型，总权重为20分\r\n        int score1_1 = 0;\r\n        if (ObjectUtil.isNotEmpty(riskPoint.getRiskPointType())) {\r\n            //1-正常码头\r\n            if ("1".equals(riskPoint.getRiskPointType())) {\r\n                //1.1.1 是否有企业承包，有0分；无10分\r\n                if (ObjectUtil.isNotEmpty(riskPoint.getCorporateStatus())) {\r\n                    if (riskPoint.getCorporateStatus() == 0) {\r\n                        score1_1 += 10;\r\n                    }\r\n                }\r\n                //1.1.2 夜间是否有管理人员，有0分；无10分\r\n                if (ObjectUtil.isNotEmpty(riskPoint.getManagerAtNight())) {\r\n                    if (riskPoint.getManagerAtNight() == 0) {\r\n                        score1_1 += 10;\r\n                    }\r\n                }\r\n            }\r\n            //2-废弃码头，3-冲滩点，4-埠口，5-岙口 默认给20分\r\n            if ("2".equals(riskPoint.getRiskPointType()) || "3".equals(riskPoint.getRiskPointType()) || "4".equals(riskPoint.getRiskPointType()) || "5".equals(riskPoint.getRiskPointType())) {\r\n                score1_1 = 20;\r\n            }\r\n            staticScore += score1_1;\r\n            log.debug(">>> 1.1-风险点类型得分：{}", score1_1);\r\n        }\r\n        //1.2 周边环境\r\n        //1.2.1 附近有无居民社区或厂房，500M以内0分；500-1000M以内5分；1000M以上10分\r\n        int score1_2_1 = 0;\r\n        if (ObjectUtil.isNotEmpty(riskPoint.getFactoryNearbyDistance())) {\r\n            if ("1".equals(riskPoint.getFactoryNearbyDistance())) {\r\n                score1_2_1 = 0;\r\n            } else if ("2".equals(riskPoint.getFactoryNearbyDistance())) {\r\n                score1_2_1 = 5;\r\n            } else if ("3".equals(riskPoint.getFactoryNearbyDistance())) {\r\n                score1_2_1 = 10;\r\n            }\r\n            staticScore += score1_2_1;\r\n        }\r\n        log.debug(">>> 1.2.1-附近有无居民社区或厂房得分：{}", score1_2_1);\r\n        //1.2.2 夜间是否有灯光，有0分；无10分\r\n        int score1_2_2 = 0;\r\n        if (ObjectUtil.isNotEmpty(riskPoint.getLightAtNight())) {\r\n            if (riskPoint.getLightAtNight() == 0) {\r\n                score1_2_2 = 10;\r\n                staticScore += score1_2_2;\r\n            }\r\n        }\r\n        log.debug(">>> 1.2.2-夜间是否有灯光得分：{}", score1_2_2);\r\n        //1.3 交通条件\r\n        //1.3.1 上县道距离，5KM以上0分；5KM以下5分\r\n        int score1_3_1 = 0;\r\n        if (ObjectUtil.isNotEmpty(riskPoint.getRoadDistance())) {\r\n            if ("2".equals(riskPoint.getRoadDistance())) {\r\n                score1_3_1 = 5;\r\n                staticScore += score1_3_1;\r\n            }\r\n        }\r\n        log.debug(">>> 1.3.1-上县道距离得分：{}", score1_3_1);\r\n        //1.3.2 上高速距离，10KM以上0分；10KM以下5分\r\n        int score1_3_2 = 0;\r\n        if (ObjectUtil.isNotEmpty(riskPoint.getHighSpeedDistance())) {\r\n            if ("2".equals(riskPoint.getHighSpeedDistance())) {\r\n                score1_3_2 = 5;\r\n                staticScore += score1_3_2;\r\n            }\r\n        }\r\n        log.debug(">>> 1.3.2-上高速距离得分：{}", score1_3_2);\r\n        //1.4 作业条件\r\n        //1.4.1 是否具备停放大型车辆的场地，是15分；否0分\r\n        int score1_4_1 = 0;\r\n        if (ObjectUtil.isNotEmpty(riskPoint.getCarEnv())) {\r\n            if ("1".equals(riskPoint.getCarEnv())) {\r\n                score1_4_1 = 15;\r\n                staticScore += score1_4_1;\r\n            }\r\n        }\r\n        log.debug(">>> 1.4.1-是否具备停放大型车辆的场地得分：{}", score1_4_1);\r\n        //1.4.2 吊机作业空间、条件是否满足，是15分；否0分\r\n        int score1_4_2 = 0;\r\n        if (ObjectUtil.isNotEmpty(riskPoint.getDjEnv())) {\r\n            if ("1".equals(riskPoint.getDjEnv())) {\r\n                score1_4_2 = 15;\r\n                staticScore += score1_4_2;\r\n            }\r\n        }\r\n        log.debug(">>> 1.4.2-吊机作业空间、条件是否满足得分：{}", score1_4_2);\r\n        //1.4.3 是否具备船舶停靠条件\r\n        int score1_4_3 = 0;\r\n        if (ObjectUtil.isNotEmpty(riskPoint.getShipEnv())) {\r\n            //根据风险点类型来算\r\n            //正常码头，100t以下0分；100t-500t5分；500t以上10分\r\n            if ("1".equals(riskPoint.getRiskPointType())) {\r\n                if ("1".equals(riskPoint.getShipTons())) {\r\n                    score1_4_3 = 0;\r\n                } else if ("2".equals(riskPoint.getShipTons())) {\r\n                    score1_4_3 = 5;\r\n                } else if ("3".equals(riskPoint.getShipTons())) {\r\n                    score1_4_3 = 10;\r\n                }\r\n            }\r\n            //废弃码头，100t以下10分；100t-500t15分；500t以上20分\r\n            if ("2".equals(riskPoint.getRiskPointType())) {\r\n                if ("1".equals(riskPoint.getShipTons())) {\r\n                    score1_4_3 = 10;\r\n                } else if ("2".equals(riskPoint.getShipTons())) {\r\n                    score1_4_3 = 15;\r\n                } else if ("3".equals(riskPoint.getShipTons())) {\r\n                    score1_4_3 = 20;\r\n                }\r\n            }\r\n            //冲滩点/埠口/岙口，具备停靠条件或者上岸条件，20分\r\n            if ("3".equals(riskPoint.getRiskPointType()) || "4".equals(riskPoint.getRiskPointType()) || "5".equals(riskPoint.getRiskPointType())) {\r\n                score1_4_3 = 20;\r\n            }\r\n            staticScore += score1_4_3;\r\n        }\r\n        log.debug(">>> 1.4.3-是否具备船舶停靠条件得分：{}", score1_4_3);\r\n        log.debug(">>> 静态风险指标总得分：{}", staticScore);\r\n\r\n        //2、动态风险指标\r\n        int dynamicScore = 0;\r\n        //先判断静态+防控措施的得分\r\n        //2.1 防控措施\r\n        //2.1.1 限高杆, 未配置10分\r\n        int score2_1_1 = 0;\r\n        if (ObjectUtil.isNotEmpty(riskPoint.getFkcsXgg())) {\r\n            if (riskPoint.getFkcsXgg() == 0) {\r\n                score2_1_1 = 10;\r\n                dynamicScore += score2_1_1;\r\n            }\r\n        }\r\n        log.debug(">>> 2.1.1-限高杆得分：{}", score2_1_1);\r\n        //2.1.2 电子围栏, 未配置5分\r\n        int score2_1_2 = 0;\r\n        if (ObjectUtil.isNotEmpty(riskPoint.getFkcsDzwl())) {\r\n            if (riskPoint.getFkcsDzwl() == 0) {\r\n                score2_1_2 = 5;\r\n                dynamicScore += score2_1_2;\r\n            }\r\n        }\r\n        log.debug(">>> 2.1.2-电子围栏得分：{}", score2_1_2);\r\n        //2.1.3 视频监控, 未配置5分\r\n        int score2_1_3 = 0;\r\n        if (ObjectUtil.isNotEmpty(riskPoint.getFkcsSpjk())) {\r\n            if (riskPoint.getFkcsSpjk() == 0) {\r\n                score2_1_3 = 5;\r\n                dynamicScore += score2_1_3;\r\n            }\r\n        }\r\n        log.debug(">>> 2.1.3-视频监控得分：{}", score2_1_3);\r\n        //2.1.4 无人机, 未配置5分\r\n        int score2_1_4 = 0;\r\n        if (ObjectUtil.isNotEmpty(riskPoint.getFkcsWrj())) {\r\n            if (riskPoint.getFkcsWrj() == 0) {\r\n                score2_1_4 = 5;\r\n                dynamicScore += score2_1_4;\r\n            }\r\n        }\r\n        log.debug(">>> 2.1.4-无人机得分：{}", score2_1_4);\r\n        //2.2 日常防控措施落实情况，未落实10分\r\n        int score2_2 = 0;\r\n        if (ObjectUtil.isNotEmpty(riskPoint.getRcfkcs())) {\r\n            if ("0".equals(riskPoint.getRcfkcs())) {\r\n                score2_2 = 10;\r\n                dynamicScore += score2_2;\r\n            }\r\n        }\r\n        log.debug(">>> 2.2-日常防控措施落实情况得分：{}", score2_2);\r\n        //2.3 出警时长，5分钟以内0分；5-10分钟3分；10分钟以上5分\r\n        int score2_3 = 0;\r\n        if (ObjectUtil.isNotEmpty(riskPoint.getPoliceTime())) {\r\n            if ("1".equals(riskPoint.getPoliceTime())) {\r\n                score2_3 = 0;\r\n            } else if ("2".equals(riskPoint.getPoliceTime())) {\r\n                score2_3 = 3;\r\n            } else if ("3".equals(riskPoint.getPoliceTime())) {\r\n                score2_3 = 5;\r\n            }\r\n            dynamicScore += score2_3;\r\n        }\r\n        log.debug(">>> 2.3-出警时长得分：{}", score2_3);\r\n        log.debug(">>> 动态风险指标总得分：{}", dynamicScore);\r\n\r\n        totalScore = staticScore * 0.6 + dynamicScore;\r\n        log.debug(">>> 风险评分计算公式：静态得分({}) * 0.6 + 动态得分({}) = {}", staticScore, dynamicScore, totalScore);\r\n\r\n        //2.4 历史案件情况，1年内直接高风险，返回100\r\n        if (ObjectUtil.isNotEmpty(riskPoint.getHistoryCase())) {\r\n            //1-3年内，风险评分低于高风险，降级为中风险\r\n            if ("3".equals(riskPoint.getHistoryCase())) {\r\n                if (totalScore < 70) {\r\n                    log.debug(">>> 历史案件情况：1-3年内有走私案件，风险评分({})低于高风险，调整为中风险(70分)", totalScore);\r\n                    totalScore = 70;\r\n                }\r\n            }\r\n            if ("2".equals(riskPoint.getHistoryCase())) {\r\n                log.debug(">>> 历史案件情况：1年内有走私案件，直接判定为高风险(100分)");\r\n                totalScore = 100.0;\r\n            }\r\n        }\r\n        log.debug(">>> 最终风险评分：{}", totalScore);\r\n        //80分以上为高风险，60-79分为中风险，60分以下为低风险\r\n        if (totalScore >= 80) {\r\n            riskPoint.setRiskLevel(3);\r\n        } else if (totalScore >= 60) {\r\n            riskPoint.setRiskLevel(2);\r\n        } else {\r\n            riskPoint.setRiskLevel(1);\r\n        }\r\n        // 保留2位小数\r\n        riskPoint.setScore(String.format("%.2f", totalScore));\r\n        RiskPointService riskPointService = SpringUtil.getApplicationContext().getBean(RiskPointService.class);\r\n        riskPointService.updateById(riskPoint);\r\n    }\r\n\r\n\r\n}\r\n', 'script', 'java', 1);
