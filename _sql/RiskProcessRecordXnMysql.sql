-- ----------------------------
-- Table structure for risk_process_record
-- ----------------------------
DROP TABLE IF EXISTS `risk_process_record`;
CREATE TABLE `risk_process_record` (
  `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `pid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父级id',
  `process_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '流程名称',
  `process_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '流程编码',
  `current_step` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '当前步骤',
  `process_status` tinyint(0) NOT NULL DEFAULT 0 COMMENT '流程状态（0-进行中 1-已完成 2-已暂停 3-已终止）',
  `start_time` datetime(0) NULL DEFAULT NULL COMMENT '流程开始时间',
  `end_time` datetime(0) NULL DEFAULT NULL COMMENT '流程结束时间',
  
  -- 信息采集相关字段
  `record_collection_time` datetime(0) NULL DEFAULT NULL COMMENT '笔录采集时间',
  `record_collector` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '笔录采集人',
  `record_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '笔录内容',
  
  `form_collection_time` datetime(0) NULL DEFAULT NULL COMMENT '表单采集时间',
  `form_collector` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表单采集人',
  `form_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '表单内容',
  
  -- 危险性评估相关字段
  `assessment_time` datetime(0) NULL DEFAULT NULL COMMENT '评估时间',
  `assessor` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评估人',
  `manual_score` decimal(5,2) NULL DEFAULT NULL COMMENT '人工评分',
  `system_score` decimal(5,2) NULL DEFAULT NULL COMMENT '系统评分',
  `total_score` decimal(5,2) NULL DEFAULT NULL COMMENT '总评分',
  `risk_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '风险等级（低风险、中风险、高风险）',
  `assessment_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '评估结果',
  
  -- 参与人信息
  `participant_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参与人姓名',
  `participant_org` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参与人机构',
  `jzjg` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '矫正机构',
  
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `status` tinyint(0) NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(0) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(0) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_process_code` (`process_code`) USING BTREE,
  INDEX `idx_process_status` (`process_status`) USING BTREE,
  INDEX `idx_participant_name` (`participant_name`) USING BTREE,
  INDEX `idx_jzjg` (`jzjg`) USING BTREE,
  INDEX `idx_risk_level` (`risk_level`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '风险流程记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES 
('6381844291783960506', '0', '[0],', '风险流程记录', 'riskprocessrecord_index', '1', null, '/riskProcessRecord', 'main/riskprocessrecord/index', null, 'system_tool', '1', 'Y', null, null, '1', '100', null, '0', null, null, null, null);

INSERT INTO `sys_menu` VALUES 
('5035355736673226680', '6381844291783960506', '[0],[6381844291783960506],', '风险流程记录查询', 'riskprocessrecord_index_page', '2', null, null, null, 'riskProcessRecord:page', 'system_tool', '0', 'Y', null, null, '1', '100', null, '0', null, null, null, null);

INSERT INTO `sys_menu` VALUES 
('9167965846615325495', '6381844291783960506', '[0],[6381844291783960506],', '风险流程记录新增', 'riskprocessrecord_index_add', '2', null, null, null, 'riskProcessRecord:add', 'system_tool', '0', 'Y', null, null, '1', '100', null, '0', null, null, null, null);

INSERT INTO `sys_menu` VALUES 
('5012813610051216237', '6381844291783960506', '[0],[6381844291783960506],', '风险流程记录编辑', 'riskprocessrecord_index_edit', '2', null, null, null, 'riskProcessRecord:edit', 'system_tool', '0', 'Y', null, null, '1', '100', null, '0', null, null, null, null);

INSERT INTO `sys_menu` VALUES 
('7134030731092476105', '6381844291783960506', '[0],[6381844291783960506],', '风险流程记录删除', 'riskprocessrecord_index_delete', '2', null, null, null, 'riskProcessRecord:delete', 'system_tool', '0', 'Y', null, null, '1', '100', null, '0', null, null, null, null);

INSERT INTO `sys_menu` VALUES 
('6094411706406300726', '6381844291783960506', '[0],[6381844291783960506],', '风险流程记录查看', 'riskprocessrecord_index_detail', '2', null, null, null, 'riskProcessRecord:detail', 'system_tool', '0', 'Y', null, null, '1', '100', null, '0', null, null, null, null);

INSERT INTO `sys_menu` VALUES 
('5042871918608788705', '6381844291783960506', '[0],[6381844291783960506],', '风险流程记录列表', 'riskprocessrecord_index_list', '2', null, null, null, 'riskProcessRecord:list', 'system_tool', '0', 'Y', null, null, '1', '100', null, '0', null, null, null, null);
