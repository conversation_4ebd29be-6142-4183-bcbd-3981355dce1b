-- ----------------------------
-- Table structure for risk_configuration_management
-- ----------------------------
DROP TABLE IF EXISTS `risk_configuration_management`;
CREATE TABLE `risk_configuration_management` (
  `id` varchar(64) NOT NULL COMMENT '主键id',
  `config_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置名称',
  `config_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置类型（个别谈话等）',
  `correction_stage` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '矫正阶段（在矫-含入矫、解矫-解除前30日）',
  `risk_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '风险等级（低风险、中风险、高风险）',
  `execution_period` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行周期（按周期执行/每日、按月执行等）',
  `execution_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '待执行' COMMENT '执行状态（待执行、执行中）',
  `used_unit` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用单位',
  `task_period` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务周期（仅一次执行、周期执行）',
  `execution_frequency` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行频段',
  `frequency_interval` int(11) NULL DEFAULT 1 COMMENT '频率间隔（每X月）',
  `execution_days` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行日期（每月的第几天，JSON格式存储）',
  `reminder_method` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '提醒方式（浙政钉）',
  `reminder_days` int(11) NULL DEFAULT 3 COMMENT '临期天数',
  `reminder_unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '天' COMMENT '提醒单位（天、提醒）',
  `publisher` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发布人',
  `auto_publisher` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'Y' COMMENT '自动获取当前账号（Y-是，N-否）',
  `create_time_auto` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'Y' COMMENT '自动获取当前时间（Y-是，N-否）',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态（0正常 1停用 2删除）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(64) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` varchar(64) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_config_name`(`config_name`) USING BTREE,
  INDEX `idx_config_type`(`config_type`) USING BTREE,
  INDEX `idx_risk_level`(`risk_level`) USING BTREE,
  INDEX `idx_execution_status`(`execution_status`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '风险配置管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for risk_configuration_execution_record
-- ----------------------------
DROP TABLE IF EXISTS `risk_configuration_execution_record`;
CREATE TABLE `risk_configuration_execution_record` (
  `id` varchar(64) NOT NULL COMMENT '主键id',
  `config_id` varchar(64) NOT NULL COMMENT '配置管理ID',
  `sequence_no` int(11) NOT NULL COMMENT '序号',
  `send_time` datetime NOT NULL COMMENT '发送时间',
  `send_users` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发送人员（多个人员用逗号分隔）',
  `send_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发送状态（成功、失败等）',
  `retry_count` int(11) NULL DEFAULT 0 COMMENT '重试次数',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误信息',
  `execution_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行结果详情',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态（0正常 1停用 2删除）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(64) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` varchar(64) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_config_id`(`config_id`) USING BTREE,
  INDEX `idx_send_time`(`send_time`) USING BTREE,
  INDEX `idx_send_status`(`send_status`) USING BTREE,
  INDEX `idx_sequence_no`(`sequence_no`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '风险配置任务执行记录表' ROW_FORMAT = Dynamic;

